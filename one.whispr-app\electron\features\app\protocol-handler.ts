import { app } from 'electron';
import { getSettingsWindow } from '../../windows';
import { IS_DEV } from '../../constants';
import { AuthCallbackData } from './types';
import { AuthService } from './service';

/**
 * Builds redirect URL with user information for Electron app
 */
export function buildRedirectUrl(baseUrl: string, data: {
  token: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
}): string {
  const url = new URL(baseUrl);
  
  url.searchParams.set('token', data.token);
  url.searchParams.set('email', data.email);
  
  if (data.firstName) {
    url.searchParams.set('firstName', data.firstName);
  }
  if (data.lastName) {
    url.searchParams.set('lastName', data.lastName);
  }
  if (data.fullName) {
    url.searchParams.set('fullName', data.fullName);
  }
  
  return url.toString();
}

/**
 * Parses auth callback URL and extracts authentication data
 */
export function parseAuthCallback(url: string): AuthCallbackData | null {
  try {
    // Handle Windows protocol URL formatting issues
    let processedUrl = url;
    
    // Fix Windows protocol URL if needed (missing slash after whispr:/)
    if (url.startsWith('whispr:/') && !url.startsWith('whispr://')) {
      processedUrl = url.replace('whispr:/', 'whispr://');
    }
    
    // Check if this is our protocol
    if (!processedUrl.startsWith('whispr://')) {
      return null;
    }
    
    const urlObj = new URL(processedUrl);
    
    // Get token (check both 'token' and 'session_id' for backward compatibility)
    let token = urlObj.searchParams.get('token');
    if (!token) {
      token = urlObj.searchParams.get('session_id');
    }
    
    if (!token) {
      console.warn('No token found in auth callback URL');
      return null;
    }
    
    const email = urlObj.searchParams.get('email');
    if (!email) {
      console.warn('No email found in auth callback URL');
      return null;
    }
    
    return {
      token,
      email,
      firstName: urlObj.searchParams.get('firstName') || undefined,
      lastName: urlObj.searchParams.get('lastName') || undefined,
      fullName: urlObj.searchParams.get('fullName') || undefined,
    };
  } catch (error) {
    console.error('Error parsing auth callback URL:', error);
    return null;
  }
}

/**
 * Handles protocol URL during app startup
 * Checks if app was launched with a protocol URL and processes it
 */
export function handleStartupProtocolUrl(): boolean {
  let handled = false;
  
  if (process.argv.length > 1) {
    const url = process.argv[process.argv.length - 1];
    if (url.startsWith('whispr://')) {
      console.log('Startup protocol URL:', url);
      
      // Process the URL after a short delay to ensure the window is ready
      setTimeout(() => {
        processAuthCallback(url);
      }, 1000);
      
      handled = true;
    }
  }
  
  return handled;
}

/**
 * Registers the whispr:// protocol handler with the operating system
 * Also sets up event listeners for protocol activation
 */
export function registerProtocolHandler(): void {
  // Force removal of any existing protocol registration first
  app.removeAsDefaultProtocolClient('whispr');
  
  // Register the protocol with the full app path
  if (IS_DEV) {
    // In dev mode, we need to use the electron.exe directly
    const execPath = process.execPath;
    app.setAsDefaultProtocolClient('whispr', execPath, [app.getAppPath()]);
  } else {
    // In production, use standard registration
    app.setAsDefaultProtocolClient('whispr');
  }

  // Handle the protocol activation (Windows/Linux)
  const gotTheLock = app.requestSingleInstanceLock();

  if (!gotTheLock) {
    app.quit();
  } else {
    app.on('second-instance', (_, commandLine) => {
      // Someone tried to run a second instance with protocol URL
      const settingsWindow = getSettingsWindow();
      if (settingsWindow) {
        if (settingsWindow.isMinimized()) settingsWindow.restore();
        settingsWindow.focus();
      }
      
      // On Windows, the URL is the last argument
      const url = commandLine.pop();
      console.log('Protocol URL received (second-instance):', url);
      
      if (url) {
        processAuthCallback(url);
      }
    });
  }

  // Handle the protocol activation (macOS)
  app.on('open-url', (event, url) => {
    event.preventDefault();
    console.log('Protocol URL received (macOS):', url);
    processAuthCallback(url);
  });
}

/**
 * Processes authentication callback and notifies renderer
 */
export function processAuthCallback(url: string): void {
  console.log('Processing auth callback URL:', url);
  
  const callbackData = parseAuthCallback(url);
  if (!callbackData) {
    console.error('Failed to parse auth callback URL');
    notifyAuthFailure('Invalid callback URL format');
    return;
  }
  
  console.log('Auth callback data extracted:', { 
    token: callbackData.token ? '****' : null, 
    email: callbackData.email,
    firstName: callbackData.firstName,
    lastName: callbackData.lastName,
    fullName: callbackData.fullName
  });
  
  // Process through AuthService
  const authService = new AuthService();
  authService.processOAuthCallback(callbackData).catch(error => {
    console.error('Error processing OAuth callback in service:', error);
    notifyAuthFailure(error.message || 'Authentication failed');
  });
}

/**
 * Notifies renderer of authentication success
 */
export function notifyAuthSuccess(userData: any): void {
  const window = getSettingsWindow();
  if (window && window.webContents) {
    window.webContents.send('auth-success', userData);
  }
}

/**
 * Notifies renderer of authentication failure
 */
export function notifyAuthFailure(error: string): void {
  const window = getSettingsWindow();
  if (window && window.webContents) {
    window.webContents.send('auth-error', { error });
  }
}

/**
 * Notifies renderer of authentication state changes
 */
export function notifyAuthStateChange(isAuthenticated: boolean, user?: any): void {
  const window = getSettingsWindow();
  if (window && window.webContents) {
    window.webContents.send('auth-state-changed', { isAuthenticated, user });
  }
} 