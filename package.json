{"name": "one.whispr", "version": "1.0.0", "private": true, "scripts": {"install": "concurrently \"npm run install:app\" \"npm run install:site\" \"npm run install:setup\"", "install:app": "cd one.whispr-app && npm install", "install:site": "cd one.whispr-site && npm install", "install:setup": "cd one.whispr-setup && npm install", "dev": "concurrently \"npm run dev:app\" \"npm run dev:site\"", "dev:app": "cd one.whispr-app && npm run dev", "dev:app-preview": "cd one.whispr-app && npm run dev:no-reload", "dev:site": "cd one.whispr-site && npm run dev", "dev:setup": "cd one.whispr-setup && npm run dev", "dev:setup-preview": "cd one.whispr-setup && npm run dev:no-reload", "dev:setup-download": "cd one.whispr-setup && npm run dev:download", "build": "concurrently \"npm run build:app\" \"npm run build:site\" \"npm run build:setup\"", "build:app": "cd one.whispr-app && npm run build", "build:site": "cd one.whispr-site && npm run build", "build:setup": "cd one.whispr-setup && npm run build", "build:setup-installer": "cd one.whispr-setup && npm run build:dist", "clean": "rimraf .dist .release"}, "devDependencies": {"concurrently": "^8.2.1", "rimraf": "^5.0.1"}}