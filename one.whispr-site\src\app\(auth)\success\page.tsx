'use client';

import { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { FiCheckCircle, FiAlertCircle } from 'react-icons/fi';
import { AuthCard } from '../_components/ui/AuthCard';
import { Button } from '@/components/ui/button';
import { createClient } from '@/utils/supabase/client';
import { SearchParamsWrapper } from '@/components/suspense/SearchParamsWrapper';

// Content component that uses useSearchParams
function AuthSuccessContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [error, setError] = useState('');
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [redirectedToApp, setRedirectedToApp] = useState(false);
  
  // Extract parameters
  const callbackUrl = searchParams.get('callbackUrl');
  const action = searchParams.get('action') || 'login'; // login or register
  
  // Extract error parameters that might be passed from OAuth provider via Supabase
  const errorParam = searchParams.get('error');
  const errorDescription = searchParams.get('error_description');
  
  // Handle redirection on mount
  useEffect(() => {
    // First, check if we have OAuth error parameters and redirect back if so
    if (errorParam || errorDescription) {
      console.error('OAuth error:', { errorParam, errorDescription });
      
      // Build the redirect URL back to the origin page with error info
      const targetPage = action === 'register' ? '/register' : '/login';
      const params = new URLSearchParams();
      
      // Include original callback URL if it exists
      if (callbackUrl) {
        params.append('callbackUrl', callbackUrl);
      }
      
      // Use the error description if available, otherwise use the error param
      if (errorDescription) {
        params.append('message', `Authentication failed: ${errorDescription}`);
      } else if (errorParam) {
        params.append('message', `Authentication failed: ${errorParam}`);
      } else {
        params.append('message', 'Authentication failed. Please try again.');
      }
      
      // Redirect back to original page with error message
      router.push(`${targetPage}?${params.toString()}`);
      return;
    }

    // Helper function to build redirect URL with user information
    const buildRedirectUrl = (baseUrl: string, session: any) => {
      const { user } = session;
      const email = user.email;
      
      // Extract user information from metadata
      const firstName = user.user_metadata?.firstName || user.user_metadata?.first_name || '';
      const lastName = user.user_metadata?.lastName || user.user_metadata?.last_name || '';
      const fullName = user.user_metadata?.full_name || user.user_metadata?.name || '';
      
      // Build URL parameters
      const params = new URLSearchParams();
      params.set('token', session.access_token);
      
      if (email) {
        params.set('email', email);
      }
      
      if (firstName) {
        params.set('firstName', firstName);
      }
      
      if (lastName) {
        params.set('lastName', lastName);
      }
      
      if (fullName) {
        params.set('fullName', fullName);
      }
      
      return `${baseUrl}?${params.toString()}`;
    };

    const handleAuthSuccess = async () => {
      setIsRedirecting(true);
      
      try {
        // Initialize Supabase client
        const supabase = createClient();
        
        // Get the current session to verify the user is authenticated
        const { data, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError || !data.session) {
          // Handle session errors by setting error state
          setError(sessionError?.message || 'Authentication failed. Please try again.');
          setIsRedirecting(false);
          
          // Try refreshing the session as a fallback
          if (!sessionError) {
            const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();
            
            if (refreshData.session) {
              // Session refreshed successfully, proceed with the updated session
              
              // Proceed with redirections using the refreshed session
              if (callbackUrl && callbackUrl.startsWith('whispr://')) {
                const redirectUrl = buildRedirectUrl(callbackUrl, refreshData.session);
                window.location.href = redirectUrl;
                // Do not continue execution
                return;
              }
              
              // Check localStorage as well
              let storedCallbackUrl = null;
              try {
                storedCallbackUrl = localStorage.getItem('whisprCallbackUrl');
                if (storedCallbackUrl) {
                  localStorage.removeItem('whisprCallbackUrl');
                }
              } catch (storageError) {
                // Silently handle localStorage errors
              }
              
              if (storedCallbackUrl && storedCallbackUrl.startsWith('whispr://')) {
                const redirectUrl = buildRedirectUrl(storedCallbackUrl, refreshData.session);
                window.location.href = redirectUrl;
                // Do not continue execution
                return;
              }
              
              // If we got here, redirect to account page
              router.push('/account');
              return;
            }
          }
          
          return;
        }
        
        const session = data.session;
        
        // IMPORTANT: Make sure we handle desktop app callbacks and stop execution
        
        // Case 1: User came from the whispr app (has callbackUrl) - redirect to the app
        if (callbackUrl && callbackUrl.startsWith('whispr://')) {
          const redirectUrl = buildRedirectUrl(callbackUrl, session);
          
          // Initiate redirect to the desktop app
          window.location.href = redirectUrl;
          
          // Do NOT continue execution or set up any timers
          // This is critical - we don't want any other redirects to happen
          return;
        }
        
        // Case 2: User completed social auth and originally had a callbackUrl to whispr app
        let storedCallbackUrl = null;
        try {
          storedCallbackUrl = localStorage.getItem('whisprCallbackUrl');
          if (storedCallbackUrl) {
            localStorage.removeItem('whisprCallbackUrl');
          }
        } catch (error) {
          // Silently handle localStorage errors
        }
        
        if (storedCallbackUrl && storedCallbackUrl.startsWith('whispr://')) {
          const redirectUrl = buildRedirectUrl(storedCallbackUrl, session);
          
          // Initiate redirect to the desktop app
          window.location.href = redirectUrl;
          
          // Do NOT continue execution or set up any timers
          // This is critical - we don't want any other redirects to happen
          return;
        }
        
        // Case 3: Only if we didn't redirect to a desktop app, redirect to account
        router.push('/account');
      } catch (error) {
        console.error('Redirect error:', error);
        setError('Failed to return to application. Please try again.');
        setIsRedirecting(false);
      }
    };
    
    // Store callbackUrl in localStorage if it exists and we're about to start a social login
    // This will be needed when the social provider redirects back to success
    if (callbackUrl && callbackUrl.startsWith('whispr://')) {
      try {
        localStorage.setItem('whisprCallbackUrl', callbackUrl);
      } catch (error) {
        console.error('Failed to store callback URL in localStorage:', error);
        // Continue anyway, as the URL parameter will still work for direct flows
      }
    }
    
    handleAuthSuccess();
  }, [callbackUrl, router, action, errorParam, errorDescription]);
  
  // Generate appropriate title based on action
  const title = action === 'register' 
    ? 'Registration Successful' 
    : 'Login Successful';
  
  // Free Trial notification component for new registrations
  const FreeTrialNotification = () => {
    if (action !== 'register') return null;
    
    return (
      <div className="mt-6 px-6 py-4 border border-border/50 rounded-lg bg-primary/5 w-full">
        <h3 className="text-base font-semibold text-primary">Free Trial Activated</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Your account includes 30 minutes of recording time with all Pro features.
        </p>
      </div>
    );
  };
  
  // Only show this page when we're in the process of redirecting
  if (isRedirecting) {
    return (
      <AuthCard title="Authentication Successful" footerContent={null}>
        <div className="flex flex-col items-center justify-center space-y-6">
          <div className="text-primary">
            <FiCheckCircle size={58} strokeWidth={1.5} />
          </div>
          <p className="text-center">Successfully authenticated!</p>
        </div>
      </AuthCard>
    );
  }
  
  // Only show this when there's an error
  if (error) {
    return (
      <AuthCard title="Authentication Error" footerContent={null}>
        <div className="flex flex-col items-center justify-center space-y-6">
          <div className="text-destructive mx-auto mb-4">
            <FiAlertCircle size={58} strokeWidth={1.5} />
          </div>
          <p className="text-center text-lg font-semibold text-destructive">Error</p>
          <p className="text-center">{error}</p>
          <Button 
            onClick={() => router.push(action === 'register' ? '/register' : '/login')}
            variant="outline"
            className="mt-4"
          >
            Return to {action === 'register' ? 'Sign Up' : 'Sign In'}
          </Button>
        </div>
      </AuthCard>
    );
  }
  
  // Fallback in case the redirect doesn't happen immediately
  return (
    <AuthCard title={title} footerContent={null}>
      <div className="flex flex-col items-center justify-center space-y-6">
        <div className="text-primary">
          <FiCheckCircle size={58} strokeWidth={1.5} />
        </div>
        <p className="text-center">Authentication successful!</p>
        <FreeTrialNotification />
      </div>
    </AuthCard>
  );
}

// Main page component that uses the SearchParamsWrapper
export default function AuthSuccessPage() {
  return (
    <SearchParamsWrapper
      fallback={
        <AuthCard title="Loading..." footerContent={null}>
          <div className="flex flex-col items-center justify-center space-y-6">
            <div className="text-primary animate-pulse">
              <FiCheckCircle size={58} strokeWidth={1.5} />
            </div>
            <p className="text-center">Please wait...</p>
          </div>
        </AuthCard>
      }
    >
      <AuthSuccessContent />
    </SearchParamsWrapper>
  );
} 