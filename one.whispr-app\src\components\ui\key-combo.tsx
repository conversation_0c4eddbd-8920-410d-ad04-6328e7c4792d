import { Fragment, HTMLProps, ReactNode } from 'react';
import { cn } from "@src/lib/utils";
import { KeySymbol } from "@src/components/ui/key-symbol";

interface KeyComboProps {
  keyNames: string[];
  disableTooltips?: boolean;
  separator?: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'setup';
  className?: string;
}

export function KeyCombo({
  keyNames,
  disableTooltips = false,
  separator = '+',
  size = 'md',
  className,
  ...otherProps
}: KeyComboProps & Omit<HTMLProps<HTMLDivElement>, keyof KeyComboProps>) {
  return (
    <div className={cn('flex items-center gap-1', className)} {...otherProps}>
      {keyNames.map((keyName, idx) => (
        <Fragment key={`${keyName}-${idx}`}>
          <KeySymbol
            keyName={keyName}
            size={size}
            disableTooltip={disableTooltips}
          />
          {idx < keyNames.length - 1 && (
            <span className="text-muted-foreground font-medium text-xs">{separator}</span>
          )}
        </Fragment>
      ))}
    </div>
  );
} 