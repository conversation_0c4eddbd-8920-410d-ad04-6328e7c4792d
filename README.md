# Whispr - AI-Powered Voice to Text Tool

Whispr is an AI-powered voice-to-text tool that uses a Python backend for Whisper voice model processing, an Electron app for the desktop application, and a Next.js website for the landing page, user management, and subscription handling.

## Project Overview

Whispr provides voice-to-text functionality that works on any application with privacy-focused, local processing. The system consists of:

1. **Next.js Landing Site** - Marketing, authentication, and subscription management
2. **Electron Desktop App** - Voice transcription UI and local Whisper model integration
3. **Python Backend** (within Electron) - Whisper model processing
4. **Supabase Backend** - Authentication, database, and API

## Subscription Tiers

### Free Tier

- **Voice to Text**: Works on any application
- **Local Processing**: Unlimited use of small Whisper models for transcription
- **Text Replacement**: Create rules for formatting output
- **Vocabulary**: Adding custom words for better voice recognition
- **Privacy-Focused**: Completely local processing, no cloud storage

### Pro Tier

- **Voice to Text**: Works on any application
- **Local Processing**: Unlimited use of any Whisper models for transcription
- **Text Replacement**: Create rules for formatting output
- **Vocabulary**: Adding custom words for better voice recognition
- **AI Post-Processing**: Unlimited AI enhancement of transcriptions (starting with Gemini AI)
- **Multi-Modal Support**: Full text, image and file context integration
- **Privacy-Focused**: No cloud storage of transcriptions
- **BYOK Support**: Bring your own AI API keys

### Free Trial

- **AI Post-Processing Limit**: 45 minutes total of AI post-processing
- **No Credit Card**: No payment information required to start
- **Full Feature Access**: All Pro tier capabilities during trial period

## Marketing Approach

### Privacy-First Voice Transcription

- "Your voice data never leaves your device" (for local processing)
- "No cloud storage of sensitive transcriptions"

### AI Enhancement as Premium Value

- "Transform basic transcriptions into polished content"
- "Multi-modal understanding of context and attached files"

### Free Trial Messaging

- "Experience 45 minutes of AI-enhanced transcription free"
- "See the difference AI makes - no commitment required"

### File Context Explanation

- For Pro tier: "Include images, documents, and other files as context for richer AI understanding"
- The free tier appropriately has no file context since it's limited to local processing

## Architecture

### Core Components

1. **Next.js Landing Site**
   - Marketing pages, features, pricing
   - User authentication & account management
   - Subscription management
   - Admin dashboard (usage analytics)

2. **Electron Desktop App**
   - Voice transcription UI
   - Local Whisper model integration
   - Authentication with Supabase
   - License validation
   - Usage tracking

3. **Python Backend** (within Electron)
   - Whisper model management
   - Local transcription processing
   - Custom vocabulary handling
   - Text replacement rules

4. **Supabase Backend**
   - User authentication
   - Database for user profiles, subscriptions, usage
   - API for feature access control
   - Real-time license validation

5. **AI Integration**
   - Gemini AI as primary AI post-processing engine
   - Expandable to other AI models in the future
   - Secure handling of API keys (BYOK)

## Core Processing Flow

1. **Voice Transcription** (Local Processing for All Users)
   - Audio captured on user's device
   - Processed locally using Whisper models in Python
   - Transcribed text output within the Electron app
   - No audio or transcription data sent to servers
   - Free users limited to smaller models, Pro users get all models

2. **AI Post-Processing** (Trial & Pro Users Only)
   - Optional enhancement after transcription
   - User can include text, image, and file context
   - Custom prompts and examples for personalized output
   - Multiple return types (text, images, structured data, etc.)
   - Trial users get 45 minutes of AI post-processing
   - Pro users get unlimited access

3. **Subscription Validation**
   - Electron app checks subscription status with Next.js API
   - Feature access controlled by subscription tier
   - Usage tracking for quota management

### AI Output Formats

Using Vercel AI SDK, the system supports various return formats:

1. **Text-Based Output**
   - Enhanced transcription text
   - Formatting improvements
   - Grammar and style corrections
   - Summarized content

2. **Rich Media Output**
   - Images and visualizations
   - Markdown or HTML formatted content
   - Tables and structured data
   - Code blocks with syntax highlighting

3. **Structured Data**
   - JSON objects with organized information
   - Named entities and extracted information
   - Sentiment analysis
   - Topic classification

### Key Architectural Decisions

#### 1. Authentication Flow

- **Registration/Login**: Users register on the Next.js site or through the app
- **Token Management**: JWT token stored securely in Electron app
- **Offline Authentication**: Local token validation with periodic online checks
- **Device Restriction**: Strict one device per user policy

#### 2. License & Feature Management

- **Feature Flags**: Database-driven feature flags tied to subscription tiers
- **Usage Quotas**: Tracking limits for free trial AI processing
- **Graceful Degradation**: Features disable when downgraded or trial ends
- **Offline Grace Period**: Allow app to function offline for a set period before requiring validation

#### 3. Model Management

- **Model Distribution**: Bundle small model with installer, download larger models on demand
- **Model Updates**: Version control and update mechanism for Whisper models
- **Custom Model Integration**: Support for users bringing custom models
- **AI Models**: Initial Gemini AI integration with framework for adding more providers

#### 4. Data Privacy & Storage

- **Local-Only Processing**: All audio processing on device
- **No Cloud Transcription Storage**: Only metadata stored in cloud
- **Usage Analytics**: Anonymized metrics for product improvement
- **Secure API Key Management**: API keys for BYOK stored securely on device only
- **Local Settings Storage**: Text replacement rules and vocabulary stored locally only

## User Onboarding Process

### 1. Download Options

1. **Direct Download Path**
   - User visits website → Enters email → Gets immediate download
   - Email contains activation link (whispr://) for simplified onboarding
   - Minimal friction, quick path to try the product

2. **Registration Path** (Alternative)
   - User visits website → Clicks "Sign Up" → Creates account on website
   - Gets download link after registration
   - More steps but creates account first

### 2. First Launch Experience

1. **App Opens for First Time**
   - Three simple options:
     * "Start Trial" (for new users)
     * "Sign In" (for existing users)
     * "Create Account" (for new users who prefer to sign up directly in the app)
   - If launched via whispr:// link: email pre-filled

2. **Email Verification**
   - One-time verification code sent to email
   - Creates account if new user; logs in if existing user
   - Unlocks appropriate tier based on account status

### 3. Subscription Management

1. **Trial Experience**
   - Clear indicator of remaining trial usage
   - Simple in-app upgrade option when ready
   - No credit card required for trial

2. **Account-Based Licensing**
   - Subscription tied directly to user account
   - No manual license key entry
   - Automatic feature activation upon payment

## BYOK (Bring Your Own Key) Implementation

### Secure API Key Handling

1. **Local Encryption Storage**
   - User's API keys stored only on their device
   - Keys encrypted using Electron's safeStorage API
   - Keys never stored in our database
   - Only a boolean flag `has_own_keys` stored in database

2. **API Key Transmission**
   - When making AI requests, encrypted API key sent with request
   - HTTPS/TLS secures transmission
   - Key used only for the duration of that request
   - Key memory cleared after request completion

3. **Server Handling**
   - API key never persisted on server
   - Used only for the immediate request
   - Server tracks only usage metrics, not the key itself

### Implementation Details

```typescript
// Example Electron secure key storage
class SecureKeyStore {
  // Save API key with encryption
  saveKey(service: string, key: string): void {
    const encryptedKey = safeStorage.encryptString(key);
    // Store encrypted key in user's local storage
  }

  // Retrieve API key with decryption
  getKey(service: string): string | null {
    // Get encrypted key from local storage
    // Return decrypted key
  }
}

// Example API request with user's key
async function processWithAI(text: string, contextFiles: any[]): Promise<string> {
  // Get user's API key from secure storage
  const apiKey = secureKeyStore.getKey('gemini');
  
  // Make request to our Next.js API
  const response = await fetch('/api/ai/enhance', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${userAuthToken}` },
    body: JSON.stringify({
      text,
      contextFiles,
      useOwnKey: !!apiKey,
      apiKey     // Sent only if user has their own key
    })
  });
  
  return response.json();
}
```

### Security Considerations

1. **Minimal Exposure**: API key exposed only during actual requests
2. **Secure Transmission**: All API communication over HTTPS with TLS
3. **No Persistence**: Keys never stored on server
4. **User Control**: Users can update or revoke keys at any time
5. **Optional**: Completely local processing option for ultra-sensitive users

## Download and Activation Flow

### Custom URL Protocol Handling

1. **Protocol Registration**
   - Windows installer registers `whispr://` protocol
   - Allows email activation links to open app directly
   - Creates seamless connection between web and desktop app

2. **Activation Link Format**
   ```
   whispr://activate?token=xyz123&email=<EMAIL>
   ```

3. **Link Handling in App**
   - App detects activation parameters on launch
   - Pre-fills email field
   - Streamlines verification process

### Device Management

1. **Device Identification**
   - Hardware fingerprinting for unique device ID
   - Automatic registration during first sign-in
   - Enforces one-device-per-user policy

2. **Device Transfer Process**
   - Simple flow for changing devices
   - Deactivation of old device
   - Cooldown period to prevent abuse
   - Activation on new device

## Database Schema

```sql
-- PROFILES
CREATE TABLE profiles (
  id UUID REFERENCES auth.users PRIMARY KEY,
  full_name TEXT,
  company TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SUBSCRIPTION PLANS
CREATE TABLE plans (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SUBSCRIPTION FEATURES
CREATE TABLE plan_features (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  plan_id UUID REFERENCES plans(id) ON DELETE CASCADE,
  feature_key TEXT NOT NULL,
  feature_value JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(plan_id, feature_key)
);

-- USER SUBSCRIPTIONS
CREATE TABLE subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users NOT NULL,
  plan_id UUID REFERENCES plans(id) NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pro', 'canceled', 'expired', 'trial', 'free')),
  trial_ends_at TIMESTAMP WITH TIME ZONE,
  current_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
  current_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
  payment_provider TEXT, -- 'stripe', 'paypal', etc.
  provider_subscription_id TEXT, -- external ID
  cancel_reason TEXT, -- track why users cancel
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- REGISTERED DEVICES (one device per user)
CREATE TABLE devices (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users NOT NULL UNIQUE, -- Enforce one device per user
  device_id TEXT NOT NULL,
  device_name TEXT,
  last_active TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT TRUE,
  deactivated_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- USAGE TRACKING
CREATE TABLE usage_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users NOT NULL,
  feature_key TEXT NOT NULL,
  usage_amount NUMERIC NOT NULL,
  usage_unit TEXT NOT NULL,
  model_name TEXT, -- Which model was used (Whisper, Gemini, etc.)
  audio_duration NUMERIC, -- Length of audio in seconds
  processing_time NUMERIC, -- Time taken to process
  input_tokens INTEGER, -- Number of input tokens for AI processing
  output_tokens INTEGER, -- Number of output tokens for AI processing
  device_id UUID REFERENCES devices(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- USER AI SETTINGS (no API keys stored)
CREATE TABLE user_ai_settings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users NOT NULL UNIQUE,
  has_own_keys BOOLEAN DEFAULT FALSE, -- flag only, no actual keys
  ai_preferences JSONB, -- store non-sensitive preferences
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- FEEDBACK (optional user feedback on transcription quality)
CREATE TABLE feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users NOT NULL,
  transcription_id TEXT, -- Reference to local transcription
  rating SMALLINT, -- User rating (1-5)
  feedback_text TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- DOWNLOAD REQUESTS
CREATE TABLE download_requests (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  token TEXT NOT NULL UNIQUE,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  activated_at TIMESTAMP WITH TIME ZONE
);

-- EMAIL VERIFICATION CODES
CREATE TABLE verification_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  code TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  verified_at TIMESTAMP WITH TIME ZONE
);

-- AUDIT LOGS
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users,
  action TEXT NOT NULL,
  entity_type TEXT NOT NULL,
  entity_id TEXT,
  changes JSONB,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- RATE LIMITING
CREATE TABLE rate_limits (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  ip_address TEXT NOT NULL,
  endpoint TEXT NOT NULL,
  count INTEGER NOT NULL DEFAULT 1,
  reset_at TIMESTAMP WITH TIME ZONE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(ip_address, endpoint)
);

-- INDEXES FOR PERFORMANCE
CREATE INDEX idx_usage_logs_user_id ON usage_logs(user_id);
CREATE INDEX idx_usage_logs_created_at ON usage_logs(created_at);
CREATE INDEX idx_download_requests_email ON download_requests(email);
CREATE INDEX idx_download_requests_token ON download_requests(token);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

## Next.js 15 Application Structure

```
one.whispr-site/
├── public/
│   ├── assets/        # Static assets (images, etc.)
│   └── favicon.ico    # Site favicon
├── src/
│   ├── app/           # Next.js 15 App Router with partial prerendering
│   │   ├── (auth)/    # Authentication routes
│   │   │   ├── login/
│   │   │   │   ├── page.tsx
│   │   │   │   └── actions.ts # Server actions (Next.js 15)
│   │   │   ├── register/
│   │   │   │   ├── page.tsx
│   │   │   │   └── actions.ts
│   │   │   └── reset-password/
│   │   │       ├── page.tsx
│   │   │       └── actions.ts
│   │   ├── (dashboard)/ # User dashboard routes
│   │   │   ├── account/
│   │   │   │   ├── page.tsx
│   │   │   │   └── actions.ts
│   │   │   ├── billing/
│   │   │   │   ├── page.tsx 
│   │   │   │   └── actions.ts
│   │   │   └── usage/
│   │   │       ├── page.tsx
│   │   │       └── actions.ts
│   │   ├── (marketing)/ # Public marketing pages with partial prerendering
│   │   │   ├── pricing/
│   │   │   │   └── page.tsx
│   │   │   ├── features/
│   │   │   │   └── page.tsx
│   │   │   └── download/
│   │   │       ├── page.tsx
│   │   │       └── actions.ts # Server actions for downloads
│   │   ├── api/       # API routes
│   │   │   ├── auth/
│   │   │   │   ├── register/route.ts
│   │   │   │   ├── login/route.ts
│   │   │   │   ├── refresh/route.ts
│   │   │   │   ├── logout/route.ts
│   │   │   │   ├── request-verification/route.ts
│   │   │   │   └── verify/route.ts
│   │   │   ├── devices/
│   │   │   │   ├── route.ts
│   │   │   │   └── deactivate/route.ts
│   │   │   ├── subscriptions/
│   │   │   │   ├── route.ts
│   │   │   │   ├── checkout/route.ts
│   │   │   │   ├── webhook/route.ts
│   │   │   │   └── plans/route.ts
│   │   │   ├── usage/
│   │   │   │   ├── route.ts
│   │   │   │   ├── log/route.ts
│   │   │   │   └── token-count/route.ts
│   │   │   └── ai/    # AI processing endpoints
│   │   │       ├── enhance/route.ts   # Main AI enhancement endpoint
│   │   │       ├── models/route.ts    # Available AI models info
│   │   │       └── settings/route.ts  # AI preferences
│   │   ├── layout.tsx # Root layout
│   │   └── page.tsx   # Home page with partial prerendering
│   ├── components/    # UI components
│   │   ├── ui/        # Shadcn UI components
│   │   ├── auth/      # Authentication components
│   │   ├── dashboard/ # Dashboard components
│   │   └── marketing/ # Marketing components
│   ├── lib/           # Utility functions
│   │   ├── supabase.ts # Supabase client
│   │   ├── auth.ts     # Auth utilities
│   │   ├── subscription.ts # Subscription utilities
│   │   └── ai-clients.ts   # AI providers integration
│   ├── hooks/         # Custom React hooks
│   │   ├── use-auth.ts
│   │   └── use-subscription.ts
│   └── types/         # TypeScript types
├── tailwind.config.js # TailwindCSS configuration
├── next.config.js     # Next.js configuration
└── package.json       # Project dependencies
```

## API Endpoints

### Next.js Site API Endpoints

1. **Authentication**
   - `/api/auth/register` - User registration
   - `/api/auth/login` - User login
   - `/api/auth/refresh` - Refresh token
   - `/api/auth/logout` - Logout user
   - `/api/auth/request-verification` - Request email verification code
   - `/api/auth/verify` - Verify email with code

2. **Download and Activation**
   - `/api/download/request` - Process download request with email
   - `/api/download/track` - Track download events
   - `/api/activation/validate-token` - Validate activation token

3. **Subscription Management**
   - `/api/subscriptions` - Get/update subscription
   - `/api/subscriptions/checkout` - Initiate payment
   - `/api/subscriptions/webhook` - Payment webhook
   - `/api/subscriptions/plans` - Get available plans

4. **User Profile**
   - `/api/profile` - Get/update user profile

5. **Devices**
   - `/api/devices` - Register device (with enforcement of one-device policy)
   - `/api/devices/deactivate` - Deactivate current device

6. **Usage**
   - `/api/usage` - Get usage statistics
   - `/api/usage/log` - Log feature usage
   - `/api/usage/token-count` - Track token usage for AI processing

7. **AI Processing**
   - `/api/ai/enhance` - Process transcription with AI
   - `/api/ai/models` - Get available AI models
   - `/api/ai/settings` - Update AI processing preferences

## AI Processing Communication Flow

### From Electron to Next.js API

```
Electron App                         Next.js API                        AI Provider
    |                                    |                                   |
    |-- 1. Local transcription --------->|                                   |
    |   with Whisper model               |                                   |
    |                                    |                                   |
    |-- 2. Request AI enhancement ------>|                                   |
    |   (text + context + API key)       |                                   |
    |                                    |-- 3. Validate subscription ------>|
    |                                    |   and check usage quota           |
    |                                    |                                   |
    |                                    |-- 4. Process with AI provider --->|
    |                                    |   (using user's key or app key)   |
    |                                    |                                   |
    |                                    |<- 5. AI response ----------------|
    |                                    |   (text, images, structured data) |
    |                                    |                                   |
    |                                    |-- 6. Track usage ---------------->|
    |                                    |   (tokens, time, etc.)            |
    |                                    |                                   |
    |<- 7. Return enhanced output -------|                                   |
    |   (multiple possible formats)      |                                   |
```

### Electron App to Next.js Communication

1. **Initial Authentication**:
   ```
   Electron App                     Next.js Site
       |                                |
       |---- Use activation link ------>|
       |     or enter email manually    |
       |                                |
       |<--- Send verification code ----|
       |                                |
       |---- Submit verification ------>|
       |                                |
       |<--- Return JWT token ----------|
       |                                |
       |---- Store token securely ----->|
   ```

2. **Token Validation**:
   ```
   Electron App                     Next.js API
       |                                |
       |---- Send token for validation->|
       |                                |
       |<--- Validate token with DB ----|
       |                                |
       |<--- Return features/limits ----|
   ```

3. **Device Registration**:
   ```
   Electron App                     Next.js API
       |                                |
       |---- Send device fingerprint -->|
       |                                |
       |<--- Check for existing device -|
       |                                |
       |<--- Register or reject --------|
   ```

### License and Feature Management

1. **Feature Access Control**:
   - JWT contains subscription tier information
   - Electron app checks token for feature access rights
   - Periodic validation against server

2. **Usage Reporting**:
   ```
   Electron App                     Next.js API
       |                                |
       |---- Send usage statistics ---->|
       |                                |
       |<--- Update usage limits -------|
       |                                |
       |<--- Return updated quotas -----|
   ```

3. **Subscription Changes**:
   - Webhook notification from payment provider to Next.js
   - Next.js updates database
   - Next.js notifies Electron app via WebSocket or poll

### Security Considerations

1. **Secure Communication**:
   - HTTPS for all API requests
   - Request signing for integrity
   - Rate limiting to prevent abuse

2. **Token Management**:
   - Short-lived access tokens
   - Refresh token rotation
   - Token revocation capability

3. **Device Verification**:
   - Hardware fingerprinting
   - Suspicious activity detection
   - Automatic logout on suspicious patterns

4. **API Key Security**:
   - User API keys never stored on server
   - Keys securely encrypted on user's device
   - Keys transmitted only for immediate use
   - Only a "has_own_keys" flag stored in database

## Usage Tracking

The system will track the following usage metrics:

1. **Basic Usage**:
   - Transcription count and duration
   - Feature access and usage frequency

2. **AI Processing**:
   - Input tokens per request
   - Output tokens per request
   - Total tokens consumed per user
   - AI model used (Gemini, etc.)

3. **Performance Metrics**:
   - Audio length processed
   - Processing time for transcription
   - Processing time for AI enhancement
   - Model load times

4. **Quality Assessment**:
   - Optional user feedback
   - Error rates and retries
   - Model confidence scores

5. **System Performance**:
   - Resource usage (CPU, RAM)
   - Processing efficiency

## Offline Capabilities

1. **Offline Authentication**
   - Cache authenticated state with expiry
   - Refresh on reconnection

2. **Feature Access Control**
   - Store feature access rights locally
   - Periodic validation to prevent tampering

3. **Delayed Usage Syncing**
   - Queue usage data when offline
   - Batch sync when connection restored

## Implementation Plan

1. **Phase 1: Foundation**
   - Set up Supabase project and schema
   - Create Next.js 15 landing page with authentication
   - Implement device management API
   - Set up one-device-per-user policy
   - Implement download and activation flow

2. **Phase 2: Core Functionality**
   - Build subscription management system
   - Implement feature flags based on subscription
   - Create API endpoints for Electron app
   - Develop device management system
   - Implement email verification system

3. **Phase 3: License Management**
   - Set up subscription management
   - Implement feature flags based on subscription
   - Add trial expiration logic
   - Build usage tracking system

4. **Phase 4: Advanced Features**
   - Add Gemini AI post-processing API integration
   - Implement token tracking
   - Develop local API key management
   - Develop multi-modal context support

5. **Phase 5: Polish and Optimization**
   - Performance optimization
   - UI/UX improvements
   - Analytics and error tracking
   - Extensibility framework for additional AI services
