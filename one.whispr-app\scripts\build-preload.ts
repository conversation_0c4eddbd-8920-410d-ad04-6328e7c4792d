import fs from 'fs';
import path from 'path';

// This script automatically discovers and combines all preload modules
// into a single index.js that doesn't rely on module resolution at runtime

interface ModuleInfo {
  name: string;
  implementation: string;
}

function main() {
  const distDir = path.join(__dirname, '../.dist/main/electron');
  const preloadDir = path.join(distDir, 'preload');

  // Automatically discover all preload modules (except index.js)
  const modules = discoverPreloadModules(preloadDir);

  if (modules.length === 0) {
    console.warn('⚠️ No preload modules found to bundle');
    return;
  }

  console.log(`📦 Found ${modules.length} preload modules:`, modules.map(m => m.name).join(', '));

  // Generate the combined script
  const combinedScript = generateCombinedScript(modules);

  // Write the combined file
  const outputPath = path.join(preloadDir, 'index.js');
  fs.writeFileSync(outputPath, combinedScript);

  console.log('✅ Generated bundled preload index.js file');
}

function discoverPreloadModules(preloadDir: string): ModuleInfo[] {
  const modules: ModuleInfo[] = [];

  if (!fs.existsSync(preloadDir)) {
    console.warn(`⚠️ Preload directory not found: ${preloadDir}`);
    return modules;
  }

  // Get all .js files in the preload directory (except index.js)
  const files = fs.readdirSync(preloadDir)
    .filter(file => file.endsWith('.js') && file !== 'index.js')
    .sort(); // Sort for consistent ordering

  for (const file of files) {
    const filePath = path.join(preloadDir, file);
    const content = fs.readFileSync(filePath, 'utf8');
    const moduleName = path.basename(file, '.js');

    // Check if this is a valid preload module (contains a setupAPI function)
    if (hasSetupFunction(content)) {
      const setupFunction = extractFunction(content, 'setupAPI');
      if (setupFunction) {
        console.log(`✅ Module ${moduleName}: found setupAPI function`);
        modules.push({
          name: moduleName,
          implementation: setupFunction
        });
      }
    } else {
      console.log(`⚠️ Module ${moduleName}: no setupAPI function found, skipping`);
    }
  }

  return modules;
}

function hasSetupFunction(jsContent: string): boolean {
  // Check if the file contains a setupAPI function
  return /function setupAPI\(\)/.test(jsContent);
}

function generateCombinedScript(modules: ModuleInfo[]): string {
  // Rename the setupAPI functions to avoid conflicts and create implementations
  const renamedImplementations = modules.map((module, index) => {
    const renamedFunction = module.implementation.replace(/function setupAPI\(\)/, `function setupAPI${index + 1}()`);
    return `// ${module.name.charAt(0).toUpperCase() + module.name.slice(1)} module\n${renamedFunction}`;
  }).join('\n\n');

  const setupCalls = modules.map((_, index) => `...setupAPI${index + 1}()`).join(',\n    ');

  return `"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");

${renamedImplementations}

// Expose protected methods that allow the renderer process to use IPC
electron_1.contextBridge.exposeInMainWorld('electron', {
    ${setupCalls}
});
`;
}

function extractFunction(jsContent: string, functionName: string): string | null {
  // Find the function definition in the compiled JavaScript
  const functionRegex = new RegExp(`function ${functionName}\\(\\)[\\s\\S]*?^}`, 'm');
  const match = jsContent.match(functionRegex);

  if (!match) {
    return null;
  }

  return match[0];
}

main();
