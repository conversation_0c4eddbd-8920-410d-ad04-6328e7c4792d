import { BaseRepository } from '../../database/repositories/base';
import { SchemaWithMigrations } from '../../database/schemas/manager';
import { VoiceModel, ModelSettings, DEFAULT_MODEL_SETTINGS } from './types';

/**
 * Repository for managing voice models (synced from Python backend)
 */
export class VoiceModelRepository extends BaseRepository<VoiceModel> {
  constructor() {
    super('voiceModels');
    // NOTE: No initializeDefaults() - voice models come from Python backend
    // Frontend should call Python's handle_get_all_models to populate this
  }

  /**
   * Ensure the voice models table exists
   */
  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS voiceModels (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Sync voice models from Python backend
   * This should be called when the app starts or when models change
   */
  syncFromPython(models: VoiceModel[]): boolean {
    try {
      // Clear existing models
      this.clearAll();
      
      // Save all models from Python
      models.forEach(model => {
        this.save(model);
      });
      
      console.log(`Synced ${models.length} voice models from Python backend`);
      return true;
    } catch (error) {
      console.error('Error syncing voice models from Python:', error);
      return false;
    }
  }

  /**
   * Clear all voice models (for syncing)
   */
  private clearAll(): void {
    const models = this.findAll();
    models.forEach(model => {
      this.delete(model.id);
    });
  }

  /**
   * Get schema definition for this repository
   */
  static getSchema(): SchemaWithMigrations {
    return {
      name: 'voiceModels',
      createStatement: `
        CREATE TABLE IF NOT EXISTS voiceModels (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            -- Add index for faster lookups
            CREATE INDEX IF NOT EXISTS idx_voice_models_id ON voiceModels(id);
          `,
          down: `
            DROP INDEX IF EXISTS idx_voice_models_id;
          `
        }
      ]
    };
  }

  /**
   * Get all available voice models
   */
  getAvailableModels(): VoiceModel[] {
    return this.findAll().filter(model => model.status.isAvailable);
  }

  /**
   * Get all downloaded voice models
   */
  getDownloadedModels(): VoiceModel[] {
    return this.findAll().filter(model => model.status.isDownloaded);
  }

  /**
   * Get voice models by provider
   */
  findByProvider(provider: string): VoiceModel[] {
    return this.findAll().filter(model => model.provider === provider);
  }

  /**
   * Update voice model status (synced from Python backend)
   */
  updateStatus(id: string, status: Partial<VoiceModel['status']>): VoiceModel | null {
    const model = this.findById(id);
    if (!model) {
      return null;
    }

    return this.update(id, {
      status: {
        ...model.status,
        ...status
      }
    });
  }

  /**
   * Get statistics about voice models
   */
  getStatistics(): {
    total: number;
    available: number;
    downloaded: number;
    byProvider: Record<string, number>;
  } {
    const models = this.findAll();
    const byProvider: Record<string, number> = {};

    models.forEach(model => {
      byProvider[model.provider] = (byProvider[model.provider] || 0) + 1;
    });

    return {
      total: models.length,
      available: models.filter(m => m.status.isAvailable).length,
      downloaded: models.filter(m => m.status.isDownloaded).length,
      byProvider
    };
  }
}

// Note: AIModelRepository removed - AI models are not implemented in Python backend yet
// When AI processing is added to Python backend, we can add this back

/**
 * Repository for managing model settings (simplified)
 */
export class ModelSettingsRepository extends BaseRepository<ModelSettings> {
  constructor() {
    super('modelSettings');
    this.initializeDefaults();
  }

  /**
   * Ensure the model settings table exists
   */
  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS modelSettings (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Initialize with default settings if empty
   */
  private initializeDefaults(): void {
    try {
      const existing = this.findAll();
      if (existing.length === 0) {
        this.save(DEFAULT_MODEL_SETTINGS);
        console.log('Initialized default model settings');
      }
    } catch (error) {
      console.error('Error initializing default model settings:', error);
    }
  }

  /**
   * Get schema definition for this repository
   */
  static getSchema(): SchemaWithMigrations {
    return {
      name: 'modelSettings',
      createStatement: `
        CREATE TABLE IF NOT EXISTS modelSettings (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            -- Add index for faster lookups
            CREATE INDEX IF NOT EXISTS idx_model_settings_id ON modelSettings(id);
          `,
          down: `
            DROP INDEX IF EXISTS idx_model_settings_id;
          `
        }
      ]
    };
  }

  /**
   * Get the model settings (there should only be one record)
   */
  getModelSettings(): ModelSettings | null {
    const settings = this.findAll();
    return settings.length > 0 ? settings[0] : null;
  }

  /**
   * Update model settings
   */
  updateModelSettings(updates: Partial<Omit<ModelSettings, 'id'>>): ModelSettings | null {
    const settings = this.getModelSettings();
    
    if (!settings) {
      // Create new settings if none exist
      return this.create(updates as Omit<ModelSettings, 'id'>);
    }

    return this.update(settings.id, updates);
  }

  /**
   * Update active voice model
   */
  setActiveVoiceModel(modelId: string): ModelSettings | null {
    return this.updateModelSettings({ activeVoiceModelId: modelId });
  }

  /**
   * Sync available voice models from Python backend
   */
  syncAvailableVoiceModels(models: VoiceModel[]): ModelSettings | null {
    return this.updateModelSettings({ availableVoiceModels: models });
  }
} 