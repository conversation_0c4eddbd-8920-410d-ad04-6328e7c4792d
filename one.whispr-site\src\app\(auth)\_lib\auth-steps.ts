'use client';

import { EmailForm } from '../_components/forms/EmailForm';
import { PasswordForm } from '../_components/forms/PasswordForm';
import { OtpForm } from '../_components/forms/OtpForm';
import { RegisterInfoForm } from '../_components/forms/RegisterInfoForm';
import { AuthFlowSteps } from './auth-flow';

/**
 * Define the steps for the login flow
 */
export const loginSteps: AuthFlowSteps = {
  email: {
    component: EmailForm,
    title: 'Sign In',
    props: { mode: 'login' }
  },
  password: {
    component: PasswordForm,
    title: 'Sign In',
    props: { mode: 'login' }
  },
  otp: {
    component: OtpForm,
    title: 'Check Your Email',
    props: { mode: 'login' }
  }
};

/**
 * Define the steps for the registration flow
 */
export const registerSteps: AuthFlowSteps = {
  info: {
    component: RegisterInfoForm,
    title: 'Sign Up'
  },
  password: {
    component: PasswordForm,
    title: 'Sign Up',
    props: { mode: 'register' }
  },
  otp: {
    component: OtpForm,
    title: 'Check Your Email',
    props: { mode: 'register' }
  }
};

/**
 * Define the steps for the password reset flow
 */
export const passwordResetSteps: AuthFlowSteps = {
  email: {
    component: EmailForm,
    title: 'Reset Password',
    props: { mode: 'reset', showSocial: false }
  },
  otp: {
    component: OtpForm,
    title: 'Check Your Email',
    props: { mode: 'reset' }
  },
  reset: {
    component: PasswordForm,
    title: 'Reset Password',
    props: { mode: 'reset' }
  }
}; 