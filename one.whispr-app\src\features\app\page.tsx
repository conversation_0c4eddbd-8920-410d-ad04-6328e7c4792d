import { useAuth, useSetup } from './context';
import { Login } from './components/login';
import { WelcomeStep } from './components/welcome-step';
import { DownloadModelStep } from './components/download-model-step';
import { AudioSetupStep } from './components/audio-setup-step';
import { ShortcutsStep } from './components/shortcuts-step';
import { CompleteStep } from './components/complete-step';
import { SetupProgress } from './components/setup-progress';

export function AppPage() {
  const { isAuthenticated } = useAuth();
  const { currentStep } = useSetup();
  
  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center w-full h-full">
        <Login />
      </div>
    );
  }
  
  return (
    <div className="flex flex-col items-center justify-center w-full h-full p-4">
      <SetupProgress />
      
      {currentStep === 'welcome' && <WelcomeStep />}
      {currentStep === 'download-model' && <DownloadModelStep />}
      {currentStep === 'audio-setup' && <AudioSetupStep />}
      {currentStep === 'shortcuts' && <ShortcutsStep />}
      {currentStep === 'complete' && <CompleteStep />}
    </div>
  );
}
