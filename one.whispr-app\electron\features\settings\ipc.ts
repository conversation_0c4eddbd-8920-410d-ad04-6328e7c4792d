import { ipcMain } from 'electron';
import { SettingsRepository } from './repository';
import { Settings, AudioDevice } from './types';

const settingsRepository = new SettingsRepository();

/**
 * Sets up all settings-related IPC handlers
 */
export function setupSettingsIPCHandlers(): void {
  
  // Get settings
  ipcMain.handle('settings:get', async (): Promise<Settings> => {
    try {
      return settingsRepository.getSettings();
    } catch (error) {
      console.error('Error getting settings:', error);
      throw error;
    }
  });

  // Update settings
  ipcMain.handle('settings:update', async (_event, updates: Partial<Omit<Settings, 'id'>>): Promise<Settings> => {
    try {
      return settingsRepository.updateSettings(updates);
    } catch (error) {
      console.error('Error updating settings:', error);
      throw error;
    }
  });

  // Update audio devices
  ipcMain.handle('settings:update-audio-devices', async (_event, inputDevices: AudioDevice[], outputDevices: AudioDevice[]): Promise<Settings> => {
    try {
      return settingsRepository.updateAudioDevices(inputDevices, outputDevices);
    } catch (error) {
      console.error('Error updating audio devices:', error);
      throw error;
    }
  });

  // Update selected devices
  ipcMain.handle('settings:update-selected-devices', async (_event, inputDeviceId?: string, outputDeviceId?: string): Promise<Settings> => {
    try {
      return settingsRepository.updateSelectedDevices(inputDeviceId, outputDeviceId);
    } catch (error) {
      console.error('Error updating selected devices:', error);
      throw error;
    }
  });

  // Update shortcuts
  ipcMain.handle('settings:update-shortcuts', async (_event, shortcuts: Partial<Settings['shortcuts']>): Promise<Settings> => {
    try {
      return settingsRepository.updateShortcuts(shortcuts);
    } catch (error) {
      console.error('Error updating shortcuts:', error);
      throw error;
    }
  });

  // Update theme
  ipcMain.handle('settings:update-theme', async (_event, theme: string, accentColor?: string): Promise<Settings> => {
    try {
      return settingsRepository.updateTheme(theme, accentColor);
    } catch (error) {
      console.error('Error updating theme:', error);
      throw error;
    }
  });

  // Reset to defaults
  ipcMain.handle('settings:reset-to-defaults', async (): Promise<Settings> => {
    try {
      return settingsRepository.resetToDefaults();
    } catch (error) {
      console.error('Error resetting settings to defaults:', error);
      throw error;
    }
  });

  console.log('Settings IPC handlers registered');
} 