import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { getLauncherWindow } from '../../window';
import { BACKEND_UPDATES } from '../../constants';
import { ArchiveExtractor } from './archiveExtractor';
import { VersionManager } from '../version/versionManager';
import { MicrosoftStoreHandler } from '../microsoft/storeHandler';

interface BackendDownloadProgress {
  type: 'runtime' | 'scripts';
  progress: number;
  speed: number;
  eta: number;
  status: string;
}

/**
 * Handles downloading of individual backend components
 */
export class ComponentDownloaders {
  private backendPath: string;
  private abortController: AbortController | null = null;
  private archiveExtractor: ArchiveExtractor;
  private versionManager: VersionManager;
  private isDownloadingRuntime: boolean = false;
  private isDownloadingScripts: boolean = false;
  private lastProgressSent: { [key: string]: number } = {};

  constructor(backendPath: string, microsoftStoreHandler?: MicrosoftStoreHandler) {
    this.backendPath = backendPath;
    this.archiveExtractor = new ArchiveExtractor(backendPath);
    this.versionManager = new VersionManager(backendPath, microsoftStoreHandler);
  }

  setAbortController(controller: AbortController | null): void {
    this.abortController = controller;
  }

  /**
   * Download and extract base runtime
   */
  async downloadRuntime(): Promise<void> {
    const downloadId = Math.random().toString(36).substring(7);
    console.log(`[BACKEND] downloadRuntime() called - ID: ${downloadId}`);
    
    if (this.isDownloadingRuntime) {
      console.log(`[BACKEND] Runtime download already in progress - ID: ${downloadId}, skipping`);
      return;
    }
    
    this.isDownloadingRuntime = true;
    
    try {
      const runtimeInfo = await this.versionManager.fetchVersionInfo('runtime');
      if (!runtimeInfo) {
        throw new Error('Failed to fetch runtime version info');
      }
      
      console.log(`[BACKEND] Runtime version info fetched - ID: ${downloadId}`, runtimeInfo);
      
      this.sendProgress('runtime', 0, 0, 0, 'Downloading CUDA runtime...');
      console.log(`[BACKEND] Initial progress sent - ID: ${downloadId}`);
      
            // Clean up old runtime files first (but preserve scripts)
      console.log(`[BACKEND] Starting cleanup - ID: ${downloadId}`);
      await this.cleanupRuntimeFiles();
      
      // Download the 7z archive
      console.log(`[BACKEND] Starting download - ID: ${downloadId}`);
      const archivePath = path.join(this.backendPath, 'OneWhispr-Runtime-Base.7z');
      await this.downloadFile(BACKEND_UPDATES.runtime.archiveUrl, archivePath, 'runtime');
      
      console.log(`[BACKEND] Download completed, starting extraction - ID: ${downloadId}`);
      this.sendProgress('runtime', 90, 0, 0, 'Extracting CUDA runtime...');

      // Extract the archive
      await this.archiveExtractor.extract7z(archivePath, this.backendPath);

      // Clean up info files if version files exist
      await this.archiveExtractor.cleanupInfoFiles();

      // Clean up archive
      await fs.remove(archivePath);
      
      // Save version info
      await fs.writeJson(path.join(this.backendPath, 'runtime-version.json'), {
        version: runtimeInfo.version,
        releaseDate: runtimeInfo.releaseDate,
        checksum: runtimeInfo.checksum,  // Save checksum for future comparisons
        installedAt: new Date().toISOString()
      });
      
      this.sendProgress('runtime', 100, 0, 0, 'CUDA runtime installed');
      console.log(`[BACKEND] Runtime download completed successfully - ID: ${downloadId}`);
    } catch (error) {
      console.error(`[BACKEND] Runtime download error - ID: ${downloadId}:`, error);
      throw error;
    } finally {
      this.isDownloadingRuntime = false;
      console.log(`[BACKEND] Runtime download state reset - ID: ${downloadId}`);
    }
  }

  /**
   * Download and extract scripts code
   */
  async downloadScripts(): Promise<void> {
    const downloadId = Math.random().toString(36).substring(7);
    console.log(`[BACKEND] downloadScripts() called - ID: ${downloadId}`);
    
    if (this.isDownloadingScripts) {
      console.log(`[BACKEND] Scripts download already in progress - ID: ${downloadId}, skipping`);
      return;
    }
    
    this.isDownloadingScripts = true;
    
    try {
      const scriptsInfo = await this.versionManager.fetchVersionInfo('scripts');
      if (!scriptsInfo) {
        throw new Error('Failed to fetch scripts version info');
      }

      console.log(`[BACKEND] Scripts version info fetched - ID: ${downloadId}`, scriptsInfo);
      this.sendProgress('scripts', 0, 0, 0, 'Downloading backend scripts...');

    // Create scripts directory
    const scriptsPath = path.join(this.backendPath, 'scripts');

    // Clean up old scripts files first
    await this.cleanupScriptsFiles();

    // Download the 7z archive
    const archivePath = path.join(this.backendPath, 'OneWhispr-Scripts.7z');
    await this.downloadFile(BACKEND_UPDATES.scripts.archiveUrl, archivePath, 'scripts');

    this.sendProgress('scripts', 90, 0, 0, 'Extracting backend scripts...');

    // Extract the archive to scripts directory
    await this.archiveExtractor.extract7z(archivePath, scriptsPath);

    // Clean up info files if version files exist
    await this.archiveExtractor.cleanupInfoFiles();

    // Clean up archive
    await fs.remove(archivePath);

    // Save version info in scripts directory
    await fs.writeJson(path.join(scriptsPath, 'scripts-version.json'), {
      version: scriptsInfo.version,
      releaseDate: scriptsInfo.releaseDate,
      installedAt: new Date().toISOString()
    });

    this.sendProgress('scripts', 100, 0, 0, 'Backend scripts updated');
    console.log(`[BACKEND] Scripts download completed successfully - ID: ${downloadId}`);
    } catch (error) {
      console.error(`[BACKEND] Scripts download error - ID: ${downloadId}:`, error);
      throw error;
    } finally {
      this.isDownloadingScripts = false;
      console.log(`[BACKEND] Scripts download state reset - ID: ${downloadId}`);
    }
  }



  /**
   * Clean up old scripts files
   */
  private async cleanupScriptsFiles(): Promise<void> {
    try {
      const scriptsPath = path.join(this.backendPath, 'scripts');

      // Remove the entire scripts directory if it exists
      if (fs.existsSync(scriptsPath)) {
        await fs.remove(scriptsPath);
        console.log('[BACKEND] Cleaned up old scripts directory');
      }

      // Ensure the scripts directory exists
      await fs.ensureDir(scriptsPath);
    } catch (error) {
      console.warn('[BACKEND] Error cleaning up scripts files:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Clean up old runtime files (but preserve scripts)
   */
  private async cleanupRuntimeFiles(): Promise<void> {
    try {
      console.log('[BACKEND] Cleaning up old runtime files (preserving scripts)...');
      
      // Same selective cleanup as Microsoft Store - remove only runtime files
      const runtimeFiles = ['One Whispr Backend.exe', '_internal', 'runtime-version.json'];

      for (const file of runtimeFiles) {
        const filePath = path.join(this.backendPath, file);
        if (fs.existsSync(filePath)) {
          await fs.remove(filePath);
          console.log(`[BACKEND] Removed: ${file}`);
        }
      }
    } catch (error) {
      console.warn('[BACKEND] Error cleaning up runtime files:', error);
      // Don't throw - this is not critical
    }
  }

  /**
   * Download a file with progress reporting
   */
  private async downloadFile(url: string, filePath: string, type: 'runtime' | 'scripts'): Promise<void> {
    return new Promise(async (resolve, reject) => {
      let writer: fs.WriteStream | null = null;
      const downloadTimeout = setTimeout(() => {
        console.error(`[BACKEND] Download timeout for ${type} after 5 minutes`);
        reject(new Error(`Download timeout: ${type} download took longer than 5 minutes`));
      }, 300000); // 5 minute timeout

      try {
        console.log(`[BACKEND] Starting download for ${type} from ${url}`);

        // Ensure directory exists
        await fs.ensureDir(path.dirname(filePath));

        // Create write stream
        writer = fs.createWriteStream(filePath);

        // Track progress
        let downloadedBytes = 0;
        const startTime = Date.now();
        let lastUpdateTime = startTime;
        let lastBytes = 0;
        let speed = 0;
        let hasStartedDownload = false;

        console.log(`[BACKEND] Making axios request to ${url}`);

        // Download the file
        const response = await axios({
          method: 'get',
          url,
          responseType: 'stream',
          signal: this.abortController?.signal,
          timeout: 60000, // 1 minute timeout for initial connection
          headers: { 'User-Agent': 'OneWhispr-Setup/1.0.0' }
        });

        console.log(`[BACKEND] Axios response received, status: ${response.status}`);
        console.log(`[BACKEND] Content-Length: ${response.headers['content-length']}`);

        const totalSize = parseInt(response.headers['content-length'] || '0');
        
        if (totalSize === 0) {
          console.warn(`[BACKEND] Warning: Content-Length is 0 for ${type} download`);
        }

        // Set up data handler
        response.data.on('data', (chunk: Buffer) => {
          if (!hasStartedDownload) {
            hasStartedDownload = true;
            console.log(`[BACKEND] First data chunk received for ${type}, chunk size: ${chunk.length}`);
          }

          downloadedBytes += chunk.length;

          // Calculate progress (reserve 10% for extraction)
          const progress = totalSize > 0 ? Math.min(90, Math.round((downloadedBytes / totalSize) * 90)) : 0;

          // Calculate speed and ETA
          const now = Date.now();
          const timeDiff = (now - lastUpdateTime) / 1000;

          if (timeDiff >= 1) { // Update every second
            speed = Math.round((downloadedBytes - lastBytes) / timeDiff);
            lastBytes = downloadedBytes;
            lastUpdateTime = now;
            
            console.log(`[BACKEND] Download progress for ${type}: ${progress}%, ${downloadedBytes}/${totalSize} bytes`);
          }

          // Calculate ETA
          const remainingBytes = totalSize - downloadedBytes;
          const eta = speed > 0 ? Math.round(remainingBytes / speed) : 0;

          // Send progress update
          this.sendProgress(type, progress, speed, eta, `Downloading ${type}...`);
        });

        // Set up error handlers
        response.data.on('error', (error: Error) => {
          console.error(`[BACKEND] Stream error for ${type}:`, error);
          clearTimeout(downloadTimeout);
          reject(new Error(`Download stream error: ${error.message}`));
        });

        // Set up end handler
        response.data.on('end', () => {
          console.log(`[BACKEND] Download stream ended for ${type}`);
          if (!hasStartedDownload) {
            console.warn(`[BACKEND] Download stream ended but no data was received for ${type}`);
          }
        });

        // Handle completion
        response.data.pipe(writer);

        writer.on('finish', () => {
          console.log(`[BACKEND] Write stream finished for ${type}`);
          clearTimeout(downloadTimeout);
          resolve();
        });

        writer.on('error', (error: Error) => {
          console.error(`[BACKEND] Write stream error for ${type}:`, error);
          clearTimeout(downloadTimeout);
          reject(new Error(`File write error: ${error.message}`));
        });

        // Add timeout check for no data received
        setTimeout(() => {
          if (!hasStartedDownload) {
            console.error(`[BACKEND] No data received for ${type} after 30 seconds`);
            clearTimeout(downloadTimeout);
            reject(new Error(`No data received for ${type} download after 30 seconds`));
          }
        }, 30000);

      } catch (error) {
        console.error(`[BACKEND] Download error for ${type}:`, error);
        clearTimeout(downloadTimeout);
        
        // Clean up write stream if it exists
        if (writer) {
          writer.destroy();
        }
        
        // Clean up partial file
        try {
          if (fs.existsSync(filePath)) {
            await fs.remove(filePath);
            console.log(`[BACKEND] Cleaned up partial file: ${filePath}`);
          }
        } catch (cleanupError) {
          console.warn(`[BACKEND] Failed to clean up partial file:`, cleanupError);
        }
        
        if (axios.isAxiosError(error)) {
          reject(new Error(`Network error: ${error.message} (URL: ${url})`));
        } else {
          reject(new Error(`Download failed: ${error instanceof Error ? error.message : String(error)}`));
        }
      }
    });
  }

  /**
   * Send progress update to renderer (with rate limiting)
   */
  private sendProgress(type: 'runtime' | 'scripts', progress: number, speed: number, eta: number, status: string): void {
    // Rate limit: only send if progress changed by at least 1% or it's a status change
    const lastProgress = this.lastProgressSent[type] || -1;
    const progressChanged = Math.abs(progress - lastProgress) >= 1;
    const isStatusUpdate = progress === 0 || progress === 90 || progress === 100;
    
    if (!progressChanged && !isStatusUpdate) {
      return; // Skip this update to reduce IPC noise
    }
    
    const progressData = {
      type,
      progress,
      speed,
      eta,
      status
    } as BackendDownloadProgress;
    
    console.log(`[BACKEND] Sending progress to frontend:`, progressData);
    
    const launcherWindow = getLauncherWindow();
    if (launcherWindow) {
      launcherWindow.webContents.send('backend:progress', progressData);
      console.log(`[BACKEND] Progress sent successfully`);
      this.lastProgressSent[type] = progress;
    } else {
      console.error(`[BACKEND] No launcher window available to send progress`);
    }
  }
}
