import { BaseRepository } from '../../database/repositories/base';
import { SchemaWithMigrations } from '../../database/schemas/manager';
import { Settings, AudioDevice, DEFAULT_SETTINGS } from './types';

/**
 * Repository for application settings
 */
export class SettingsRepository extends BaseRepository<Settings> {
  constructor() {
    super('settings');
    this.initializeDefaults();
  }

  /**
   * Initialize with default settings if empty
   */
  private initializeDefaults(): void {
    try {
      const existing = this.findAll();
      if (existing.length === 0) {
        // Create default settings
        this.save(DEFAULT_SETTINGS);
        console.log('Initialized default settings');
      }
    } catch (error) {
      console.error('Error initializing default settings:', error);
    }
  }

  /**
   * Ensure the table exists with proper schema
   */
  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Get the settings configuration (singleton pattern)
   */
  getSettings(): Settings {
    const settings = this.findAll();
    if (settings.length === 0) {
      // Create default settings
      const { id, ...settingsWithoutId } = DEFAULT_SETTINGS;
      return this.create(settingsWithoutId);
    }
    return settings[0];
  }

  /**
   * Update settings configuration
   */
  updateSettings(updates: Partial<Omit<Settings, 'id'>>): Settings {
    const settings = this.getSettings();
    const updated = this.update(settings.id, updates);
    if (!updated) {
      throw new Error('Failed to update settings');
    }
    return updated;
  }

  /**
   * Update audio devices
   */
  updateAudioDevices(inputDevices: AudioDevice[], outputDevices: AudioDevice[]): Settings {
    return this.updateSettings({
      availableInputDevices: inputDevices,
      availableOutputDevices: outputDevices
    });
  }

  /**
   * Update selected audio devices
   */
  updateSelectedDevices(inputDeviceId?: string, outputDeviceId?: string): Settings {
    const updates: Partial<Omit<Settings, 'id'>> = {};
    
    if (inputDeviceId !== undefined) {
      updates.selectedInputDevice = inputDeviceId;
      updates.useDefaultInputDevice = inputDeviceId === 'default';
    }
    
    if (outputDeviceId !== undefined) {
      updates.selectedOutputDevice = outputDeviceId;
      updates.useDefaultOutputDevice = outputDeviceId === 'default';
    }
    
    return this.updateSettings(updates);
  }

  /**
   * Update shortcuts
   */
  updateShortcuts(shortcuts: Partial<Settings['shortcuts']>): Settings {
    const currentSettings = this.getSettings();
    return this.updateSettings({
      shortcuts: {
        ...currentSettings.shortcuts,
        ...shortcuts
      }
    });
  }

  /**
   * Update theme settings
   */
  updateTheme(theme: string, accentColor?: string): Settings {
    const updates: Partial<Omit<Settings, 'id'>> = { theme };
    if (accentColor) {
      updates.accentColor = accentColor;
    }
    return this.updateSettings(updates);
  }

  /**
   * Reset settings to defaults
   */
  resetToDefaults(): Settings {
    const settings = this.getSettings();
    const { id, ...defaultsWithoutId } = DEFAULT_SETTINGS;
    const updated = this.update(settings.id, defaultsWithoutId);
    if (!updated) {
      throw new Error('Failed to reset settings');
    }
    return updated;
  }

  /**
   * Get schema definition for this repository
   */
  static getSchema(): SchemaWithMigrations {
    return {
      name: 'settings',
      createStatement: `
        CREATE TABLE IF NOT EXISTS settings (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            -- Add index for faster lookups (though settings table will only have one record)
            CREATE INDEX IF NOT EXISTS idx_settings_id ON settings(id);
          `,
          down: `
            DROP INDEX IF EXISTS idx_settings_id;
          `
        }
      ]
    };
  }
} 