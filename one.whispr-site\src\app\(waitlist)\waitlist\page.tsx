import Image from 'next/image';
import WaitlistForm from '../_components/WaitlistForm';
import { Card, CardContent } from '@/components/ui/card';

export default function WaitlistPage() {
  return (
    <div className="min-h-screen flex items-center justify-center relative">
      <div className="w-full max-w-md">
        {/* Logo and title positioned above the card - copied from AuthCard.tsx */}
        <div className="absolute left-1/2 -translate-x-1/2 -mt-45 flex flex-col items-center">
          <div className="mb-3">
            <Image 
              src="/one.whispr-icon.png" 
              alt="One Whispr" 
              width={80} 
              height={80} 
              className="rounded-md" 
            />
          </div>
          <h1 className="text-2xl font-semibold">One Whispr</h1>
          <p className="text-center text-muted-foreground mt-2">
            AI-powered voice to text tool
          </p>
        </div>

        {/* Main card with waitlist form */}
        <Card className="w-full">
          <CardContent className="space-y-6 px-6">
            <div>
              <div className="text-center mb-6">
                <h2 className="text-xl font-semibold">Join the Waitlist</h2>
                <p className="text-sm text-muted-foreground mt-1">
                  Be the first to experience our private beta
                </p>
              </div>
              <WaitlistForm />
            </div>
          </CardContent>
        </Card>
        
        {/* Terms of service - at bottom of page - copied from AuthCard.tsx */}
        <div className="absolute bottom-8 left-0 right-0 text-center">
          <p className="text-base text-muted-foreground">
            By continuing, you agree to our{' '}
            <a href="/terms" className="underline underline-offset-4 hover:text-foreground">
              Terms of Service
            </a>
            {' '}and{' '}
            <a href="/privacy" className="underline underline-offset-4 hover:text-foreground">
              Privacy Policy
            </a>
          </p>
        </div>
      </div>
    </div>
  );
} 