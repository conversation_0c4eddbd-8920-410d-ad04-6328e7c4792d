"""
Transcription Engine - Main Coordinator

Clean coordinator that orchestrates transcription processing using specialized modules.
Handles the main transcription flow and end-of-stream signaling for race condition fix.
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, Any, Optional

from .transcription_types import ProcessingMode, TranscriptionResult
from .transcription_inference import TranscriptionInferenceEngine
from .transcription_session import TranscriptionSessionManager
from .transcription_events import TranscriptionEventEmitter
from .realtime_segment_manager import RealtimeSegmentManager
from .text_accumulator import TextAccumulator

logger = logging.getLogger(__name__)


class TranscriptionEngine:
    """
    Main transcription coordinator that orchestrates the transcription pipeline.
    
    Responsibilities:
    - Coordinate audio processing flow
    - Manage processing sessions
    - Handle end-of-stream signaling (race condition fix)
    - Dispatch events via IPC bridge
    """
    
    def __init__(self, model_service=None, service_container=None):
        """Initialize the transcription engine.
        
        Args:
            model_service: ModelService instance for accessing loaded models
            service_container: Service container for IPC bridge access
        """
        # Core components
        self.model_service = model_service
        self.service_container = service_container  # Store service container for later use
        self.inference_engine = TranscriptionInferenceEngine()
        self.session_manager = TranscriptionSessionManager()
        
        # Get IPC bridge from service container for events
        ipc_bridge = None
        if service_container and hasattr(service_container, 'get_service'):
            try:
                ipc_bridge = service_container.get_service('ipc')
            except Exception as e:
                logger.debug(f"No IPC bridge available: {e}")
        
        self.event_emitter = TranscriptionEventEmitter(ipc_bridge)
        
        # Processing state
        self.processing_mode = ProcessingMode.REAL_TIME
        self.is_processing = False
        self.current_session_id = None
        
        # Audio processing settings
        self.target_sample_rate = 16000
        self.min_audio_length_samples = 1600  # 0.1 seconds at 16kHz
        # No max audio length limit - Whisper can handle long audio
        
        # Real-time processing throttling (inspired by RealtimeSTT)
        self.realtime_processing_interval_ms = 200  # Process every 200ms instead of 100ms
        self.chunks_per_processing = self.realtime_processing_interval_ms // 100  # 2 chunks
        self.chunk_accumulator = 0  # Track chunks since last processing
        
        # Batch mode accumulation
        self.accumulated_audio = []  # List of audio chunks for batch processing
        self.batch_chunk_count = 0   # Track chunks accumulated
        
        # Processing statistics
        self.processed_chunks = 0
        self.total_processing_time = 0.0
        self.error_count = 0
        
        # Initialize segment manager with smaller window and throttling for better performance
        self.realtime_segment_manager = RealtimeSegmentManager(
            sliding_window_duration=7.0,  # 7 second window (was 10s)
            stable_threshold=3.0,
            target_sample_rate=self.target_sample_rate
        )
        
        # Initialize text accumulator for preserving complete transcription
        self.text_accumulator = TextAccumulator(
            stability_threshold=3.0,  # Same as segment manager
            min_stable_length=10
        )
        
        logger.info("TranscriptionEngine initialized")
    
    async def process_audio(self, audio_data: np.ndarray, session_id: Optional[str] = None, 
                          chunk_metadata: Optional[Dict[str, Any]] = None) -> Optional[TranscriptionResult]:
        """Process audio data and return transcription result.
        
        Args:
            audio_data: Preprocessed audio samples from AudioChunk
            session_id: Optional session identifier
            chunk_metadata: Optional metadata about the audio chunk
            
        Returns:
            TranscriptionResult if successful, None if failed or no speech detected
        """
        if not self.is_processing:
            logger.debug("TranscriptionEngine not in processing state, skipping audio")
            return None
        
        # Handle different processing modes
        if self.processing_mode == ProcessingMode.BATCH:
            return await self._accumulate_audio_chunk(audio_data, session_id, chunk_metadata)
        else:
            return await self._process_realtime_chunk(audio_data, session_id, chunk_metadata)
    
    async def _accumulate_audio_chunk(self, audio_data: np.ndarray, session_id: Optional[str] = None, 
                                    chunk_metadata: Optional[Dict[str, Any]] = None) -> None:
        """Accumulate audio chunk for batch processing.
        
        Args:
            audio_data: Audio samples as numpy array
            session_id: Optional session identifier
            chunk_metadata: Optional metadata about the audio chunk
            
        Returns:
            None (batch processing happens on stop)
        """
        try:
            # Audio is already preprocessed by audio system, just validate
            if audio_data is not None and len(audio_data) > 0:
                self.accumulated_audio.append(audio_data)
                self.session_manager.update_chunk_stats(0.0, False)
                logger.debug(f"Accumulated chunk for batch processing ({len(audio_data)} samples)")
            
        except Exception as e:
            logger.error(f"Error accumulating audio chunk: {e}")
            self.session_manager.update_chunk_stats(0.0, True)
        
        return None
    
    async def _process_realtime_chunk(self, audio_data: np.ndarray, session_id: Optional[str] = None, 
                                     chunk_metadata: Optional[Dict[str, Any]] = None) -> Optional[TranscriptionResult]:
        """Process audio chunk with sliding window and throttling for real-time transcription.
        
        Args:
            audio_data: New audio chunk to add to sliding window
            session_id: Optional session identifier
            chunk_metadata: Optional metadata about the audio chunk
            
        Returns:
            TranscriptionResult if successful, None if failed
        """
        if audio_data is None or len(audio_data) == 0:
            return None
        
        # Always add chunk to sliding window (for continuity)
        window_info = self.realtime_segment_manager.add_audio_chunk(audio_data, session_id)
        self.chunk_accumulator += 1
        
        # Only process every N chunks (throttling)
        should_process = self.chunk_accumulator >= self.chunks_per_processing
        
        if not should_process:
            # Skip processing but log accumulation
            logger.debug(f"Accumulating chunk {self.chunk_accumulator}/{self.chunks_per_processing} "
                        f"(throttling: {self.realtime_processing_interval_ms}ms interval)")
            return None
        
        # Reset accumulator
        self.chunk_accumulator = 0
        
        start_time = time.time()
        
        try:
            # Process current sliding window
            accumulated_audio = window_info['accumulated_audio']
            if accumulated_audio is not None:
                # Get model from model service and run inference
                if not self.model_service or not self.model_service._loaded_model:
                    logger.error("No model loaded in ModelService")
                    return None
                
                loaded_model = self.model_service._loaded_model
                model = loaded_model.model
                processor = model.processor
                
                # Run Whisper inference on sliding window audio
                result = await self.inference_engine.run_whisper_inference(accumulated_audio, model, processor, loaded_model)
                
                if result:
                    processing_time = (time.time() - start_time) * 1000
                    result.processing_time_ms = processing_time
                    self.session_manager.update_chunk_stats(processing_time, False)
                    
                    # Update text accumulator with new window result
                    current_time = time.time()
                    accumulator_result = self.text_accumulator.update(result.text, current_time)
                    
                    # Get window metadata from segment manager (no longer handles text)
                    window_metadata = self.realtime_segment_manager.get_window_metadata()
                    
                    # Combine accumulator result with window metadata for rich event data
                    combined_metadata = {
                        **window_metadata,
                        **accumulator_result,
                        'window_text': result.text,  # Original window-only text
                        'confidence': result.confidence,
                        'processing_time_ms': processing_time
                    }
                    
                    # Emit partial result with complete accumulated text
                    self.event_emitter.emit_partial_result(
                        accumulator_result['complete_text'], 
                        result.confidence, 
                        combined_metadata
                    )
                    
                    # Log sliding window performance with throttling info
                    window_duration = window_info['window_duration']
                    window_chunks = window_info['window_chunks']
                    window_samples = window_info['window_samples']
                    
                    logger.debug(f"Real-time sliding window (throttled): WINDOW='{result.text}' COMPLETE='{accumulator_result['complete_text']}' "
                               f"(confidence: {result.confidence:.2f}, time: {processing_time:.1f}ms, "
                               f"window: {window_duration:.1f}s, chunks: {window_chunks}, "
                               f"samples: {window_samples}, interval: {self.realtime_processing_interval_ms}ms, "
                               f"stable_len: {accumulator_result['stable_length']}, growing_len: {accumulator_result['growing_length']})")
                    
                    return result
            
        except Exception as e:
            logger.error(f"Error processing real-time chunk with sliding window: {e}")
            self.session_manager.update_chunk_stats(0.0, True)
            self.event_emitter.emit_error("realtime_processing_error", str(e))
        
        return None
    
    async def _process_accumulated_audio(self) -> Optional[TranscriptionResult]:
        """Process all accumulated audio chunks together (batch mode).
        
        Returns:
            TranscriptionResult if successful, None if failed
        """
        if not self.accumulated_audio:
            logger.warning("No accumulated audio to process")
            return None
        
        start_time = time.time()
        
        try:
            # Concatenate all accumulated chunks
            combined_audio = np.concatenate(self.accumulated_audio)
            total_duration = len(combined_audio) / 16000  # Assuming 16kHz
            
            logger.info(f"Processing batch audio: {len(combined_audio)} samples "
                       f"({total_duration:.2f}s) from {len(self.accumulated_audio)} chunks")
            
            # Get model from model service and run inference
            if not self.model_service or not self.model_service._loaded_model:
                logger.error("No model loaded in ModelService")
                return None
            
            loaded_model = self.model_service._loaded_model
            model = loaded_model.model
            processor = model.processor
            
            # Run Whisper inference on combined audio
            result = await self.inference_engine.run_whisper_inference(combined_audio, model, processor, loaded_model)
            
            if result:
                processing_time = (time.time() - start_time) * 1000
                result.processing_time_ms = processing_time
                self.session_manager.update_chunk_stats(processing_time, False)
                
                # Emit transcription result event
                self.event_emitter.emit_transcription_result(result)
                
                logger.info(f"Batch transcription completed: '{result.text}' "
                           f"(confidence: {result.confidence:.2f}, time: {processing_time:.1f}ms)")
                
                return result
            
        except Exception as e:
            logger.error(f"Error processing accumulated audio: {e}")
            self.session_manager.update_chunk_stats(0.0, True)
            self.event_emitter.emit_error("batch_processing_error", str(e))
        
        return None
    
    def set_processing_mode(self, mode: ProcessingMode) -> None:
        """Set the processing mode.
        
        Args:
            mode: Processing mode to set
        """
        self.processing_mode = mode
        logger.info(f"Processing mode set to: {mode.value}")
    
    def start_processing(self, session_id: Optional[str] = None) -> bool:
        """Start transcription processing.
        
        Args:
            session_id: Optional session identifier
            
        Returns:
            True if started successfully
        """
        try:
            if self.is_processing:
                logger.warning("Processing already active")
                return True
            
            # Initialize session
            self.current_session_id = session_id or f"session_{int(time.time())}"
            self.session_manager.start_session(self.current_session_id)
            self.event_emitter.set_session_id(self.current_session_id)
            
            # Reset state
            self.is_processing = True
            self.accumulated_audio.clear()
            
            # Reset segment manager and text accumulator for real-time mode
            self.realtime_segment_manager.reset()
            self.text_accumulator.reset()
            if self.processing_mode == ProcessingMode.REAL_TIME:
                self.realtime_segment_manager.start_new_session(self.current_session_id)
            
            # Get model info for session start event
            model_size = 'unknown'
            if self.model_service and self.model_service._loaded_model:
                loaded_model = self.model_service._loaded_model
                model_size = getattr(loaded_model, 'model_id', 'unknown')
            
            # Emit session started event
            self.event_emitter.emit_session_started(self.processing_mode.value, model_size)
            
            logger.info(f"Transcription processing started: session_id={self.current_session_id}, "
                       f"mode={self.processing_mode.value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start processing: {e}")
            return False
    

    
    async def stop_processing(self, cancelled: bool = False) -> Dict[str, Any]:
        """Stop transcription processing and return final statistics.
        
        Args:
            cancelled: Whether processing was cancelled
            
        Returns:
            Final session statistics
        """
        if not self.is_processing:
            logger.warning("Processing not active")
            return {}
        
        logger.info(f"Stopping transcription processing: session_id={self.current_session_id}, "
                   f"cancelled={cancelled}")
        
        # Finalize any active real-time segment before stopping
        if not cancelled and self.processing_mode == ProcessingMode.REAL_TIME:
            try:
                # Finalize text accumulator to get complete text
                final_text = self.text_accumulator.finalize()
                
                # Get session metadata from segment manager
                final_metadata = self.realtime_segment_manager.finalize_current_session()
                
                if final_text and final_metadata:
                    # Combine final metadata
                    complete_metadata = {
                        **final_metadata,
                        'final_text': final_text,
                        'text_accumulator_status': self.text_accumulator.get_status()
                    }
                    
                    # Emit final result with complete accumulated text
                    self.event_emitter.emit_final_result(
                        final_text, 
                        1.0,  # High confidence for finalized
                        metadata=complete_metadata
                    )
                    logger.info(f"Finalized active real-time session: '{final_text}'")
            except Exception as e:
                logger.error(f"Error finalizing real-time session on stop: {e}")
        
        # Process accumulated audio in batch mode before finishing
        if not cancelled and self.processing_mode == ProcessingMode.BATCH and self.accumulated_audio:
            try:
                logger.info(f"Processing {len(self.accumulated_audio)} accumulated chunks in batch mode")
                # Send batch processing request to worker thread
                if self.service_container:
                    transcription_service = self.service_container.resolve("transcription")
                    if transcription_service and hasattr(transcription_service, 'batch_processing_requests'):
                        # Create batch request
                        batch_request = {
                            'session_id': self.current_session_id,
                            'accumulated_audio': self.accumulated_audio.copy(),  # Copy for thread safety
                            'processing_mode': self.processing_mode.value,
                            'requested_at': time.time()
                        }
                        
                        # Create future for result coordination
                        import concurrent.futures
                        result_future = concurrent.futures.Future()
                        transcription_service.batch_processing_results[self.current_session_id] = result_future
                        
                        # Send request to worker thread
                        transcription_service.batch_processing_requests.put(batch_request)
                        
                        logger.info(f"Batch processing request sent to worker thread for session {self.current_session_id}")
                        
                        # Return stats immediately - worker will handle completion
                        stats = {
                            "session_id": self.current_session_id,
                            "processing_mode": self.processing_mode.value,
                            "accumulated_chunks": len(self.accumulated_audio),
                            "status": "batch_processing_scheduled"
                        }
                        
                        return stats
                    else:
                        logger.warning("TranscriptionService not available for batch processing, falling back to main thread")
                        # Fallback to old behavior
                        import asyncio
                        try:
                            loop = asyncio.get_event_loop()
                            if loop.is_running():
                                task = asyncio.create_task(self._process_accumulated_audio())
                                stats = {
                                    "session_id": self.current_session_id,
                                    "processing_mode": self.processing_mode.value,
                                    "accumulated_chunks": len(self.accumulated_audio),
                                    "status": "batch_processing_scheduled_main_thread"
                                }
                                return stats
                        except Exception as fallback_error:
                            logger.error(f"Fallback batch processing failed: {fallback_error}")
                else:
                    logger.warning("No service container available for batch processing")
            except Exception as e:
                logger.error(f"Error in batch processing during stop: {e}")
        
        # For real-time mode or cancelled batch mode, finish immediately
        return self._finish_session(cancelled)
    
    def _finish_session(self, cancelled: bool = False) -> Dict[str, Any]:
        """Finish the current session and return statistics.
        
        Args:
            cancelled: Whether session was cancelled
            
        Returns:
            Final session statistics
        """
        try:
            # Get final statistics
            final_stats = self.session_manager.stop_session("cancelled" if cancelled else "normal")
            
            # Emit session ended event (only once!)
            self.event_emitter.emit_session_ended(final_stats)
            
            # Reset state
            self.is_processing = False
            self.current_session_id = None
            self.accumulated_audio.clear()
            
            # Reset segment manager and text accumulator
            self.realtime_segment_manager.reset()
            self.text_accumulator.reset()
            
            logger.info(f"Session finished: {final_stats}")
            return final_stats
            
        except Exception as e:
            logger.error(f"Error finishing session: {e}")
            return {}
    
    def get_engine_status(self) -> Dict[str, Any]:
        """Get current engine status.
        
        Returns:
            Dictionary with current status information
        """
        # Get model info
        model_info = None
        if self.model_service and self.model_service._loaded_model:
            loaded_model = self.model_service._loaded_model
            model_info = {
                'size': getattr(loaded_model, 'model_id', 'unknown'),
                'device': loaded_model.device,
                'dtype': getattr(loaded_model, 'dtype', 'unknown')
            }
        
        session_stats = self.session_manager.get_status(self.processing_mode)
        
        return {
            "is_processing": self.is_processing,
            "processing_mode": self.processing_mode.value,
            "current_session_id": self.current_session_id,
            "model_info": model_info,
            "session_stats": session_stats,
            "accumulated_chunks": len(self.accumulated_audio)
        } 