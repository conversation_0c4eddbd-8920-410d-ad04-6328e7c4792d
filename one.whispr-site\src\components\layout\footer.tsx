"use client";

import Link from 'next/link';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aEnvelope } from 'react-icons/fa';
import { cn } from "@/lib/utils";

export default function Footer() {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="pb-4 px-4 bg-background">
      <div className="w-[98%] max-w-[1550px] mx-auto">
        {/* Top bar with email and copyright */}
        <div className="flex flex-col sm:flex-row justify-between items-center mt-5 mb-5 gap-4 px-6">
          <a href="mailto:<EMAIL>" className="font-display text-sm tracking-widest text-muted-foreground hover:text-foreground transition-colors uppercase">
            <EMAIL>
          </a>
          <p className="text-sm text-muted-foreground">
            © {currentYear}
          </p>
        </div>

        {/* Main Content Area */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-3">
          {/* Stay in Touch Section */}
          <div className="bg-card/30 p-6 rounded-xl">
            <h3 className="font-display uppercase text-[15px] tracking-widest font-medium mb-4">Let's Stay In Touch</h3>
            <p className="text-[15px] text-muted-foreground mb-6 font-display tracking-wider">
              Never miss important updates, new features, and special offers.
            </p>
            <div className="flex items-center space-x-4">
              <a 
                href="https://twitter.com/whispr_one" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-background rounded-full p-3 text-muted-foreground hover:text-foreground hover:bg-primary/10 transition-all duration-300"
                aria-label="Twitter"
              >
                <FaTwitter size={20} />
              </a>
              <a 
                href="https://github.com/yourorg/whispr-one" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-background rounded-full p-3 text-muted-foreground hover:text-foreground hover:bg-primary/10 transition-all duration-300"
                aria-label="GitHub"
              >
                <FaGithub size={20} />
              </a>
              <a 
                href="https://discord.gg/whispr-one" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-background rounded-full p-3 text-muted-foreground hover:text-foreground hover:bg-primary/10 transition-all duration-300"
                aria-label="Discord"
              >
                <FaDiscord size={20} />
              </a>
            </div>
          </div>
          
          {/* Product */}
          <div className="bg-card/30 p-6 rounded-xl">
            <h3 className="font-display uppercase text-[15px] tracking-widest font-medium mb-4">Product</h3>
            <nav className="flex flex-col space-y-3">
              <Link href="/features" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Features
              </Link>
              <Link href="/use-cases" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Use Cases
              </Link>
              <Link href="/#pricing" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Pricing
              </Link>
              <Link href="/#download" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Download
              </Link>
            </nav>
          </div>
          
          {/* Resources */}
          <div className="bg-card/30 p-6 rounded-xl">
            <h3 className="font-display uppercase text-[15px] tracking-widest font-medium mb-4">Resources</h3>
            <nav className="flex flex-col space-y-3">
              <Link href="/faq" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                FAQ
              </Link>
              <Link href="/docs" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Docs
              </Link>
              <Link href="/support" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Support
              </Link>
              <Link href="/about" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                About Us
              </Link>
            </nav>
          </div>

          {/* Legal */}
          <div className="bg-card/30 p-6 rounded-xl">
            <h3 className="font-display uppercase text-[15px] tracking-widest font-medium mb-4">Legal</h3>
            <nav className="flex flex-col space-y-3">
              <Link href="/terms" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Terms & Conditions
              </Link>
              <Link href="/privacy" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Privacy Policy
              </Link>
              <Link href="/cookies" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Cookie Policy
              </Link>
              <Link href="/refund" className="text-[15px] font-medium tracking-widest font-display uppercase text-muted-foreground hover:text-foreground/80 transition-colors">
                Refund Policy
              </Link>
            </nav>
          </div>
        </div>
      </div>
    </footer>
  );
} 