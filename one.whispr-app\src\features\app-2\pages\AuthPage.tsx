/**
 * AuthPage Component
 * Authentication page connecting AuthContext to auth components
 */

import React from 'react';
import { useAuth } from '../contexts/AuthContext';

import { LoginForm } from '../components/auth/LoginForm';
import { SocialLoginButton } from '../components/auth/SocialLoginButton';
import { LoadingSpinner } from '../components/shared/LoadingSpinner';
import { ErrorMessage } from '../components/shared/ErrorMessage';

interface AuthPageProps {
  onNext?: () => void;
  onPrevious?: () => void;
}

export function AuthPage({ onNext, onPrevious }: AuthPageProps) {
  const { 
    state,
    loginWithEmail,
    loginWithGoogle,
    loginWithTwitter,
    register,
    clearError
  } = useAuth();

  const { isAuthenticated, loading, error } = state;

  // Auto-advance when authenticated
  React.useEffect(() => {
    if (isAuthenticated && onNext) {
      onNext();
    }
  }, [isAuthenticated, onNext]);

  const handleEmailContinue = async (email: string) => {
    await loginWithEmail(email);
  };

  const handleRegister = async () => {
    await register();
  };

  const handleGoogleLogin = async () => {
    await loginWithGoogle();
  };

  const handleTwitterLogin = async () => {
    await loginWithTwitter();
  };

  const handleSkip = () => {
    // For now, just continue to next step
    onNext?.();
  };

  return (
    <>
      {/* Login Form - handles its own layout and centering */}
      <LoginForm
        onEmailLogin={handleEmailContinue}
        onGoogleLogin={handleGoogleLogin}
        onTwitterLogin={handleTwitterLogin}
        onRegister={handleRegister}
        loading={loading}
        error={error}
        onDismissError={clearError}
      />
    </>
  );
} 