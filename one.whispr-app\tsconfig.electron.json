{"extends": "./tsconfig.json", "compilerOptions": {"module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "outDir": ".dist/main", "sourceMap": true, "composite": true, "declaration": true, "declarationMap": true, "rootDir": "."}, "include": ["electron/**/*.ts", "src/**/types/*.ts", "python/main.ts", "python/types.ts", "python/websocket.ts", "python/requests.ts", "python/process.ts"], "exclude": ["node_modules", ".dist"]}