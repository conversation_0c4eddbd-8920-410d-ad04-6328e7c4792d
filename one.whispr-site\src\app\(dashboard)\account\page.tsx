'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { createClient } from '@/utils/supabase/client';

export default function AccountPage() {
  const router = useRouter();
  const [user, setUser] = useState<{
    email: string;
    name: string;
  } | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function loadUserProfile() {
      setLoading(true);
      const supabase = createClient();
      
      // Get the current user session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        // If no session, redirect to login
        router.push('/login');
        return;
      }
      
      // Get user data from session
      const { user: authUser } = session;
      
      if (authUser) {
        // Extract user metadata (if available) or fallback to email
        const firstName = authUser.user_metadata?.firstName || '';
        const lastName = authUser.user_metadata?.lastName || '';
        const fullName = authUser.user_metadata?.full_name || `${firstName} ${lastName}`.trim();
        
      setUser({
          email: authUser.email || '',
          name: fullName || authUser.email?.split('@')[0] || 'User'
        });
      }
      
      setLoading(false);
    }
    
    loadUserProfile();
  }, [router]);
  
  async function handleSignOut() {
    const supabase = createClient();
    await supabase.auth.signOut();
    router.push('/login');
    }

  if (loading) {
    return (
      <div className="container mx-auto py-10">
        <p className="text-center">Loading...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">My Account</h1>
      
      {user && (
        <div className="bg-card p-6 rounded-lg shadow-sm border mb-8">
          <h2 className="text-xl font-semibold mb-4">Profile Information</h2>
          <p className="mb-2"><span className="font-medium">Name:</span> {user.name}</p>
          <p><span className="font-medium">Email:</span> {user.email}</p>
        </div>
      )}
      
      <div className="mt-8 text-center">
        <Button variant="outline" onClick={handleSignOut}>
          Sign Out
        </Button>
      </div>
    </div>
  );
} 