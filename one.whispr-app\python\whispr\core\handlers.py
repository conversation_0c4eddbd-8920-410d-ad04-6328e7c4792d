"""
Command handling system for One.Whispr.

This module contains the CommandHandlers class that manages and dispatches
command requests from the Electron frontend to appropriate handlers.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, Type, Callable

from whispr.core.base import CommandHandler, ServiceContainer
from whispr.core.registry import CommandHandlerRegistry
from whispr.core.response_utils import error_response, ErrorCodes


class CommandHandlers:
    """Manages and dispatches command requests."""
    
    def __init__(self, service_container: Optional[ServiceContainer] = None):
        """Initialize the command handlers.
        
        Args:
            service_container: The service container for dependency resolution
        """
        self.logger = logging.getLogger("whispr.CommandHandlers")
        self.service_container = service_container
        self.registry = CommandHandlerRegistry(service_container)
        
        # Register all handlers from handlers modules
        self._register_all_handlers()
        
    def _register_all_handlers(self) -> None:
        """Register all handlers from handlers modules."""
        from whispr.handlers import register_all_handlers
        register_all_handlers(self)
        
    def register_handler(self, handler: CommandHandler) -> None:
        """Register a command handler.
        
        Args:
            handler: The command handler to register
        """
        self.registry.register(handler)
        
    def register_function(self, command: str, handler_fn: Callable) -> None:
        """Register a function as a command handler.
        
        Args:
            command: The command name
            handler_fn: The handler function
        """
        self.registry.register_function(command, handler_fn)
        
    def register_class(self, handler_class: Type[CommandHandler]) -> CommandHandler:
        """Register a command handler class.
        
        Args:
            handler_class: The command handler class to register
            
        Returns:
            The instantiated handler
        """
        return self.registry.register_class(handler_class)
        
    async def dispatch(self, params: Dict[str, Any], context: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Dispatch a command to the appropriate handler.
        
        Args:
            params: The command parameters
            context: The execution context
            
        Returns:
            The command result or None if no handler is found
        """
        # Add service container to context for handlers to use
        context.setdefault('service_container', self.service_container)
        
        command = params.get("command")
        if not command:
            self.logger.warning("Missing command in dispatch request")
            return error_response("Missing command", ErrorCodes.PARAMETER_ERROR)
            
        # Try object handlers first
        handler = self.registry.get_handler(command)
        if handler:
            try:
                return await handler.handle(params, context)
            except Exception as e:
                self.logger.error(f"Error in handler '{command}': {e}", exc_info=True)
                return error_response(f"Error in {command} handler: {str(e)}", ErrorCodes.SERVICE_ERROR)
                
        # Try function handlers
        handler_fn = self.registry.get_function_handler(command)
        if handler_fn:
            try:
                # Call the handler function
                if asyncio.iscoroutinefunction(handler_fn):
                    # Async handler
                    result = await handler_fn(params.get("params", {}), context)
                else:
                    # Sync handler
                    result = handler_fn(params.get("params", {}), context)
                    
                return result
            except Exception as e:
                self.logger.error(f"Error in function handler '{command}': {e}", exc_info=True)
                return error_response(f"Error in {command} handler: {str(e)}", ErrorCodes.SERVICE_ERROR)
                
        # No handler found
        self.logger.warning(f"No handler found for command: {command}")
        return error_response(f"Unknown command: {command}", ErrorCodes.RESOURCE_NOT_FOUND)
        
    def initialize_websocket_handlers(self, websocket_server: Any) -> None:
        """Initialize WebSocket command handlers.
        
        This registers handlers with the WebSocket server.
        
        Args:
            websocket_server: The WebSocket server
        """
        if not websocket_server:
            self.logger.warning("No WebSocket server provided for handler registration")
            return
            
        # Register handlers with WebSocket server
        for command in self.registry.get_registered_commands():
            # Create dispatcher for each command
            async def create_dispatcher(cmd):
                async def dispatch_command(params, context):
                    return await self.dispatch({"command": cmd, "params": params}, context)
                return dispatch_command
                
            # Create a closure that captures the current command
            def make_dispatcher(cmd=command):
                async def dispatch_command(params, context):
                    return await self.dispatch({"command": cmd, "params": params}, context)
                return dispatch_command
            
            # Register the dispatcher directly without using asyncio.run()
            websocket_server.register_command_handler(command, make_dispatcher()) 