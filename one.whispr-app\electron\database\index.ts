import { getDatabase, getMainDatabasePath, closeDatabase, transaction, initializeBackendSync, sendInitialSync } from './core/connection';
import { generateId } from './core/id';
import { 
  initializeSchemas, 
  TableSchema, 
  SchemaWithMigrations, 
  Migration, 
  registerSchema,
  applyMigrations,
  rollbackToVersion,
  getSchema,
  getAllSchemas 
} from './schemas/manager';
import {
  BaseEntity,
  BaseRepository,
  DatabaseChangeType,
  DatabaseChangeEvent,
  dbEvents
} from './repositories/base';

// Import feature repositories for auto-registration
import { AppRepository, UserRepository, UserSessionRepository, AuthSettingsRepository } from '../features/app/repository';
import { ModeRepository } from '../features/modes/repository';
import { VocabularyRepository } from '../features/vocabulary/repository';
import { TextReplacementRepository } from '../features/text-replacements/repository';
import { SettingsRepository } from '../features/settings/repository';
import { VoiceModelRepository, ModelSettingsRepository } from '../features/ai-models/repository';

/**
 * Register all feature repository schemas
 */
function registerFeatureRepositories(): void {
  // Register schemas from all feature repositories
  registerSchema(AppRepository.getSchema());
  registerSchema(UserRepository.getSchema());
  registerSchema(UserSessionRepository.getSchema());
  registerSchema(AuthSettingsRepository.getSchema());
  registerSchema(ModeRepository.getSchema());
  registerSchema(VocabularyRepository.getSchema());
  registerSchema(TextReplacementRepository.getSchema());
  registerSchema(SettingsRepository.getSchema());
  registerSchema(VoiceModelRepository.getSchema());
  registerSchema(ModelSettingsRepository.getSchema());
  
  // Add more feature repositories here as they are created
}

/**
 * Initialize all repositories to ensure default data is inserted
 */
function initializeRepositories(): void {
  try {
    console.log('Initializing repositories with default data...');
    
    // Instantiate all repositories to trigger their default initialization
    new AppRepository();
    new UserRepository();
    new UserSessionRepository();
    new AuthSettingsRepository();
    new ModeRepository();
    new VocabularyRepository();
    new TextReplacementRepository();
    new SettingsRepository();
    new VoiceModelRepository();
    new ModelSettingsRepository();
    
    console.log('Repositories initialized with default data');
  } catch (error) {
    console.error('Error initializing repositories:', error);
    throw error;
  }
}

/**
 * Initialize the database system
 * This should be called during application startup
 */
export function initializeDatabaseSystem(): void {
  try {
    // Ensure database connection exists
    getDatabase();
    
    // Register all feature repository schemas
    registerFeatureRepositories();
    
    // Initialize all schemas (creates tables)
    initializeSchemas();
    
    // Initialize repositories (inserts default data)
    initializeRepositories();
    
    // Initialize sync with backend
    initializeBackendSync();
    
  } catch (error) {
    console.error('Failed to initialize database system:', error);
    throw error;
  }
}

/**
 * Shutdown the database system
 * This should be called during application shutdown
 */
export function shutdownDatabaseSystem(): void {
  try {
    // Close database connection
    closeDatabase();
    
  } catch (error) {
    console.error('Error shutting down database system:', error);
  }
}

// Re-export everything
export {
  // Core
  getDatabase,
  getMainDatabasePath,
  closeDatabase,
  transaction,
  generateId,
  sendInitialSync,
  
  // Schema
  TableSchema,
  SchemaWithMigrations,
  Migration,
  registerSchema,
  applyMigrations,
  rollbackToVersion,
  getSchema,
  getAllSchemas,
  
  // Repository
  BaseEntity,
  BaseRepository,
  DatabaseChangeType,
  DatabaseChangeEvent,
  dbEvents,
  
  // Feature Repositories
  AppRepository,
  UserRepository,
  UserSessionRepository,
  AuthSettingsRepository,
  ModeRepository,
  VocabularyRepository,
  TextReplacementRepository,
  SettingsRepository,
  VoiceModelRepository,
  ModelSettingsRepository,
}; 