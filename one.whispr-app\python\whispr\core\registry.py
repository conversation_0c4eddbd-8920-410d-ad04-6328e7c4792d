"""
Command handler registry for One.Whispr.

This module contains the CommandHandlerRegistry class that manages
registration and lookup of command handlers.
"""

import logging
from typing import Dict, Any, Optional, List, Callable, Type

from whispr.core.base import CommandHandler, ServiceContainer


class CommandHandlerRegistry:
    """Registry for command handlers."""
    
    def __init__(self, service_container: Optional[ServiceContainer] = None):
        """Initialize the command handler registry.
        
        Args:
            service_container: The service container for dependency resolution
        """
        self.logger = logging.getLogger("whispr.command.Registry")
        self.service_container = service_container
        self.handlers: Dict[str, CommandHandler] = {}
        self.function_handlers: Dict[str, Callable] = {}
        
    def register(self, handler: CommandHandler) -> None:
        """Register a command handler.
        
        Args:
            handler: The command handler to register
        """
        command_type = handler.get_command_type()
        self.handlers[command_type] = handler
        self.logger.debug(f"Registered handler for command: {command_type}")
        
    def register_function(self, command: str, handler_fn: Callable) -> None:
        """Register a function as a command handler.
        
        Args:
            command: The command name
            handler_fn: The handler function
        """
        if command in self.function_handlers:
            self.logger.warning(f"Overriding existing handler for command: {command}")
            
        self.function_handlers[command] = handler_fn
        self.logger.debug(f"Registered function handler for command: {command}")
        
    def register_class(self, handler_class: Type[CommandHandler]) -> CommandHandler:
        """Register a command handler class.
        
        Args:
            handler_class: The command handler class to register
            
        Returns:
            The instantiated handler
        """
        handler = handler_class(self.service_container)
        self.register(handler)
        return handler
        
    def get_handler(self, command_type: str) -> Optional[CommandHandler]:
        """Get a command handler.
        
        Args:
            command_type: The command type
            
        Returns:
            The command handler or None if not found
        """
        return self.handlers.get(command_type)
        
    def get_function_handler(self, command: str) -> Optional[Callable]:
        """Get a function handler.
        
        Args:
            command: The command name
            
        Returns:
            The handler function or None if not found
        """
        return self.function_handlers.get(command)
        
    def has_handler(self, command_type: str) -> bool:
        """Check if a handler is registered for a command.
        
        Args:
            command_type: The command type
            
        Returns:
            True if a handler is registered, False otherwise
        """
        return command_type in self.handlers or command_type in self.function_handlers
        
    def get_registered_commands(self) -> List[str]:
        """Get a list of registered commands.
        
        Returns:
            A list of registered command types
        """
        return list(self.handlers.keys()) + list(self.function_handlers.keys()) 