/**
 * Auth Context
 * Authentication state management with OAuth integration
 * Integrates with SetupFlowContext for step lifecycle management
 */

import React, { 
  createContext, 
  useContext, 
  useReducer, 
  useCallback, 
  useEffect
} from 'react';


import { useStepIntegration } from '../hooks/useStepIntegration';
import type { AuthState, User } from '../types/auth';

// ============================================================================
// ACTIONS
// ============================================================================

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_AUTH_SUCCESS'; payload: { isAuthenticated: boolean; user: User | null } }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' };

// ============================================================================
// REDUCER
// ============================================================================

function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_AUTH_SUCCESS':
      return {
        ...state,
        isAuthenticated: action.payload.isAuthenticated,
        user: action.payload.user,
        loading: false,
        error: null,
      };

    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };

    case 'CLEAR_ERROR':
      return { ...state, error: null };

    default:
      return state;
  }
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  loading: false,
  error: null,
};

// ============================================================================
// CONTEXT INTERFACE
// ============================================================================

interface AuthContextValue {
  state: AuthState;
  
  // Auth methods
  checkAuthStatus: () => Promise<void>;
  loginWithEmail: (email: string) => Promise<void>;
  loginWithGoogle: () => Promise<void>;
  loginWithTwitter: () => Promise<void>;
  register: () => Promise<void>;
  logout: () => Promise<void>;
  
  // Error handling
  clearError: () => void;
}

const AuthContext = createContext<AuthContextValue | null>(null);

// ============================================================================
// PROVIDER
// ============================================================================

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  // Note: setupActions no longer needed as useStepIntegration handles the proceed state

  // ============================================================================
  // STEP LIFECYCLE INTEGRATION
  // ============================================================================

  // Use the step integration hook to handle proceed state and lifecycle
  useStepIntegration({
    step: 'auth',
    canProceed: state.isAuthenticated,
    onEnter: async () => {
      console.log('🔐 Entering auth step - checking authentication status');
      await checkAuthStatus();
    },
    onExit: async () => {
      console.log('🔐 Exiting auth step');
      // Auth step doesn't need cleanup
    },
    deps: []
  });

  // ============================================================================
  // AUTH API METHODS
  // ============================================================================

  const checkAuthStatus = useCallback(async () => {
    try {
      console.log('🔍 Checking authentication status...');
      dispatch({ type: 'SET_LOADING', payload: true });
      
      if (!window.electron?.getAuthStatus) {
        throw new Error('Electron auth API not available');
      }

      const authStatus = await window.electron.getAuthStatus();
      console.log('✓ Auth status checked:', { isAuthenticated: authStatus.isAuthenticated });
      
      dispatch({
        type: 'SET_AUTH_SUCCESS',
        payload: {
          isAuthenticated: authStatus.isAuthenticated,
          user: authStatus.user || null,
        },
      });
    } catch (error: any) {
      console.error('❌ Failed to check auth status:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error.message || 'Failed to check authentication status' 
      });
    }
  }, []);

  const loginWithEmail = useCallback(async (email: string) => {
    try {
      console.log('📧 Initiating email login for:', email);
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.openExternalUrl) {
        throw new Error('Electron API not available');
      }

      const loginUrl = `http://whispr.one/login?step=password&email=${encodeURIComponent(email)}&callbackUrl=${encodeURIComponent('whispr://auth')}`;
      await window.electron.openExternalUrl(loginUrl);
      
      console.log('✓ Email login page opened');
      // Loading state will be cleared by auth event handlers
    } catch (error: any) {
      console.error('❌ Failed to open login page:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error.message || 'Failed to open login page' 
      });
    }
  }, []);

  const loginWithGoogle = useCallback(async () => {
    try {
      console.log('🔍 Initiating Google login...');
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.openExternalUrl) {
        throw new Error('Electron API not available');
      }

      const googleLoginUrl = `http://whispr.one/login?callbackUrl=${encodeURIComponent('whispr://auth')}&action=google`;
      await window.electron.openExternalUrl(googleLoginUrl);
      
      console.log('✓ Google login page opened');
      // Loading state will be cleared by auth event handlers
    } catch (error: any) {
      console.error('❌ Failed to open Google login:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error.message || 'Failed to open Google login' 
      });
    }
  }, []);

  const loginWithTwitter = useCallback(async () => {
    try {
      console.log('🐦 Initiating Twitter login...');
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.openExternalUrl) {
        throw new Error('Electron API not available');
      }

      const twitterLoginUrl = `http://whispr.one/login?callbackUrl=${encodeURIComponent('whispr://auth')}&action=twitter`;
      await window.electron.openExternalUrl(twitterLoginUrl);
      
      console.log('✓ Twitter login page opened');
      // Loading state will be cleared by auth event handlers
    } catch (error: any) {
      console.error('❌ Failed to open Twitter login:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error.message || 'Failed to open Twitter login' 
      });
    }
  }, []);

  const register = useCallback(async () => {
    try {
      console.log('📝 Initiating registration...');
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.openExternalUrl) {
        throw new Error('Electron API not available');
      }

      const registerUrl = `http://whispr.one/register?callbackUrl=${encodeURIComponent('whispr://auth')}`;
      await window.electron.openExternalUrl(registerUrl);
      
      console.log('✓ Registration page opened');
      // Loading state will be cleared by auth event handlers
    } catch (error: any) {
      console.error('❌ Failed to open register page:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error.message || 'Failed to open register page' 
      });
    }
  }, []);

  const logout = useCallback(async () => {
    try {
      console.log('🚪 Initiating logout...');
      dispatch({ type: 'SET_LOADING', payload: true });

      if (window.electron?.logout) {
        await window.electron.logout();
      }

      dispatch({
        type: 'SET_AUTH_SUCCESS',
        payload: { isAuthenticated: false, user: null },
      });
      
      console.log('✓ Logout successful');
    } catch (error: any) {
      console.error('❌ Failed to logout:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error.message || 'Failed to logout' 
      });
    }
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // ============================================================================
  // ELECTRON AUTH EVENT HANDLERS
  // ============================================================================

  useEffect(() => {
    console.log('🔧 Setting up authentication event listeners');

    const handleAuthSuccess = (data: any) => {
      console.log('🎉 Authentication successful:', data);
      dispatch({ type: 'SET_LOADING', payload: false });
      dispatch({ type: 'CLEAR_ERROR' });
      // Auth status will be updated by the auth state change handler
    };

    const handleAuthError = (data: any) => {
      console.error('❌ Authentication error:', data);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: data.error || 'Authentication failed. Please try again.' 
      });
    };

    const handleAuthStateChange = (data: any) => {
      console.log('🔄 Auth state changed:', data);
      dispatch({
        type: 'SET_AUTH_SUCCESS',
        payload: {
          isAuthenticated: data.isAuthenticated,
          user: data.user || null,
        },
      });
    };

    // Set up event listeners
    const cleanupAuthSuccess = window.electron?.onAuthSuccess?.(handleAuthSuccess);
    const cleanupAuthError = window.electron?.onAuthError?.(handleAuthError);
    const cleanupAuthStateChange = window.electron?.onAuthStateChanged?.(handleAuthStateChange);

    return () => {
      console.log('🧹 Cleaning up authentication event listeners');
      cleanupAuthSuccess?.();
      cleanupAuthError?.();
      cleanupAuthStateChange?.();
    };
  }, []);

  // ============================================================================
  // INITIAL AUTH CHECK
  // ============================================================================

  // Check auth status on mount
  useEffect(() => {
    console.log('🚀 Initializing AuthProvider');
    checkAuthStatus();
  }, [checkAuthStatus]);

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const contextValue: AuthContextValue = {
    state,
    checkAuthStatus,
    loginWithEmail,
    loginWithGoogle,
    loginWithTwitter,
    register,
    logout,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// ============================================================================
// HOOK
// ============================================================================

export function useAuth(): AuthContextValue {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
} 