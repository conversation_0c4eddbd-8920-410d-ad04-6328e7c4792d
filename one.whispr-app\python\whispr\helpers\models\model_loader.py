"""
Model loader for One.Whispr voice models.

This module provides efficient loading and unloading of Whisper models
with memory management, progress tracking, and hardware optimization.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
import threading

from .hardware_detector import HardwareDetector, HardwareConfig, MemoryInfo
from .storage_manager import StorageManager
from .model_registry import ModelRegistry
from .model_validator import ModelValidator

logger = logging.getLogger('whispr.helpers.models.model_loader')

# Pre-import heavy libraries to avoid delays during model loading
_TORCH_AVAILABLE = None
_TRANSFORMERS_AVAILABLE = None
_ACCELERATE_AVAILABLE = None
_TORCH_MODULE = None
_TRANSFORMERS_MODULES = None
_ACCELERATE_MODULES = None
_INITIALIZATION_THREAD = None
_INITIALIZATION_COMPLETE = threading.Event()

def _initialize_libraries_worker():
    """Worker function to initialize heavy libraries in background thread."""
    global _TORCH_AVAILABLE, _TRANSFORMERS_AVAILABLE, _TORCH_MODULE, _TRANSFORMERS_MODULES
    global _ACCELERATE_AVAILABLE, _ACCELERATE_MODULES
    
    try:
        # Initialize PyTorch
        if _TORCH_AVAILABLE is None:
            try:
                import torch
                _TORCH_MODULE = torch
                _TORCH_AVAILABLE = True
                logger.info("PyTorch initialized successfully")
            except ImportError as e:
                _TORCH_AVAILABLE = False
                logger.warning(f"PyTorch not available: {e}")
        
        # Initialize Transformers
        if _TRANSFORMERS_AVAILABLE is None:
            try:
                logger.info("Initializing Transformers library (this may take a moment)...")
                from transformers import AutoModelForSpeechSeq2Seq, AutoProcessor
                _TRANSFORMERS_MODULES = {
                    'AutoModelForSpeechSeq2Seq': AutoModelForSpeechSeq2Seq,
                    'AutoProcessor': AutoProcessor
                }
                _TRANSFORMERS_AVAILABLE = True
                logger.info("Transformers initialized successfully")
            except ImportError as e:
                _TRANSFORMERS_AVAILABLE = False
                logger.warning(f"Transformers not available: {e}")
        
        # Initialize Accelerate
        if _ACCELERATE_AVAILABLE is None:
            try:
                logger.info("Initializing Accelerate library...")
                import accelerate
                _ACCELERATE_MODULES = {
                    'init_empty_weights': accelerate.init_empty_weights,
                    'load_checkpoint_and_dispatch': accelerate.load_checkpoint_and_dispatch,
                    'infer_auto_device_map': accelerate.infer_auto_device_map
                }
                _ACCELERATE_AVAILABLE = True
                logger.info("Accelerate initialized successfully")
            except ImportError as e:
                _ACCELERATE_AVAILABLE = False
                logger.warning(f"Accelerate not available: {e}")
        
        logger.info("Background ML library initialization completed")
        print("Background ML library initialization completed successfully", flush=True)
        
    except Exception as e:
        logger.error(f"Error during background library initialization: {e}")
        print(f"Background ML library initialization failed: {e}", flush=True)
    finally:
        # Signal that initialization is complete (whether successful or not)
        _INITIALIZATION_COMPLETE.set()

def _initialize_libraries():
    """Initialize heavy libraries in background thread."""
    global _INITIALIZATION_THREAD
    
    if _INITIALIZATION_THREAD is not None:
        # Already started
        return
    
    # Start background initialization
    _INITIALIZATION_THREAD = threading.Thread(
        target=_initialize_libraries_worker,
        daemon=True,
        name="MLLibraryInit"
    )
    _INITIALIZATION_THREAD.start()
    logger.info("Started background ML library initialization")
    print("Background ML library initialization started successfully", flush=True)

def _wait_for_initialization(timeout: float = 30.0) -> bool:
    """Wait for background initialization to complete.
    
    Args:
        timeout: Maximum time to wait in seconds
        
    Returns:
        True if initialization completed within timeout
    """
    if _INITIALIZATION_COMPLETE.is_set():
        return True
    
    logger.info("Waiting for background ML library initialization to complete...")
    completed = _INITIALIZATION_COMPLETE.wait(timeout)
    
    if not completed:
        logger.warning(f"Background initialization did not complete within {timeout} seconds")
    
    return completed

def get_initialization_status() -> Dict[str, Any]:
    """Get the status of background library initialization.
    
    Returns:
        Dictionary with initialization status information
    """
    return {
        "initialization_started": _INITIALIZATION_THREAD is not None,
        "initialization_complete": _INITIALIZATION_COMPLETE.is_set(),
        "torch_available": _TORCH_AVAILABLE,
        "transformers_available": _TRANSFORMERS_AVAILABLE, 
        "accelerate_available": _ACCELERATE_AVAILABLE,
        "thread_alive": _INITIALIZATION_THREAD.is_alive() if _INITIALIZATION_THREAD else False
    }

def _check_torch_availability():
    """Check if PyTorch is available."""
    if _TORCH_AVAILABLE is None:
        # Start background initialization if not already started
        _initialize_libraries()
        # Wait for completion before returning result
        _wait_for_initialization()
    return _TORCH_AVAILABLE

def _check_transformers_availability():
    """Check if Transformers is available."""
    if _TRANSFORMERS_AVAILABLE is None:
        # Start background initialization if not already started
        _initialize_libraries()
        # Wait for completion before returning result
        _wait_for_initialization()
    return _TRANSFORMERS_AVAILABLE

def _check_accelerate_availability():
    """Check if Accelerate is available."""
    if _ACCELERATE_AVAILABLE is None:
        # Start background initialization if not already started
        _initialize_libraries()
        # Wait for completion before returning result
        _wait_for_initialization()
    return _ACCELERATE_AVAILABLE

def get_torch_module():
    """Get the torch module."""
    if _TORCH_MODULE is None:
        # Start background initialization if not already started
        _initialize_libraries()
        # Wait for completion before returning result
        _wait_for_initialization()
    return _TORCH_MODULE

def get_transformers_modules():
    """Get the transformers modules."""
    if _TRANSFORMERS_MODULES is None:
        # Start background initialization if not already started
        _initialize_libraries()
        # Wait for completion before returning result
        _wait_for_initialization()
    return _TRANSFORMERS_MODULES

def get_accelerate_modules():
    """Get the accelerate modules."""
    if _ACCELERATE_MODULES is None:
        # Start background initialization if not already started
        _initialize_libraries()
        # Wait for completion before returning result
        _wait_for_initialization()
    return _ACCELERATE_MODULES

class LoadingState(Enum):
    """Model loading states."""
    NOT_LOADED = "not_loaded"
    LOADING = "loading"
    LOADED = "loaded"
    UNLOADING = "unloading"
    FAILED = "failed"


@dataclass
class LoadingProgress:
    """Progress information for model loading."""
    model_id: str
    state: LoadingState
    progress_percent: float
    current_step: str
    total_steps: int
    current_step_index: int
    elapsed_time: float
    estimated_remaining: Optional[float] = None
    error_message: Optional[str] = None
    memory_usage: Optional[int] = None


@dataclass
class LoadedModel:
    """Information about a loaded model."""
    model_id: str
    model: Any  # The actual Whisper model object
    device: str
    dtype: str
    memory_usage: int
    load_time: float
    model_size: int
    config: Dict[str, Any]
    is_optimized: bool = False
    is_compiled: bool = False  # Whether torch.compile was applied


class ModelLoader:
    """Efficient model loader with memory management."""
    
    def __init__(self, storage_manager: StorageManager):
        """Initialize the model loader.
        
        Args:
            storage_manager: Storage manager for file operations
        """
        self.storage_manager = storage_manager
        self.hardware_detector = HardwareDetector()
        self.model_registry = ModelRegistry()
        self.model_validator = ModelValidator(storage_manager)
        
        # Current state
        self._loaded_model: Optional[LoadedModel] = None
        self._loading_state = LoadingState.NOT_LOADED
        self._progress_callbacks: List[Callable[[LoadingProgress], None]] = []
        self._loading_lock = threading.Lock()
        
        # Loading steps for progress tracking
        self._loading_steps = [
            "Initializing libraries",
            "Validating model",
            "Checking hardware compatibility", 
            "Cleaning up memory",
            "Loading model files",
            "Loading processor",
            "Loading model weights", 
            "Moving to device",
            "Verifying model",
            "Finalizing"
        ]
    
    def add_progress_callback(self, callback: Callable[[LoadingProgress], None]) -> None:
        """Add a progress callback for loading operations.
        
        Args:
            callback: Function to call with progress updates
        """
        if callback not in self._progress_callbacks:
            self._progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable[[LoadingProgress], None]) -> None:
        """Remove a progress callback.
        
        Args:
            callback: Function to remove
        """
        if callback in self._progress_callbacks:
            self._progress_callbacks.remove(callback)
    
    def _notify_progress(self, progress: LoadingProgress) -> None:
        """Notify all progress callbacks.
        
        Args:
            progress: Progress information to send
        """
        for callback in self._progress_callbacks:
            try:
                callback(progress)
            except Exception as e:
                logger.error(f"Error in progress callback: {e}")
    
    async def load_model(self, model_id: str) -> LoadedModel:
        """Load a model with optimal configuration.
        
        Args:
            model_id: Model identifier to load
            
        Returns:
            LoadedModel instance
            
        Raises:
            ValueError: If model not found or invalid
            RuntimeError: If loading fails
            MemoryError: If insufficient memory
        """
        
        with self._loading_lock:
            if self._loading_state == LoadingState.LOADING:
                raise RuntimeError("Another model is currently being loaded")
            
            self._loading_state = LoadingState.LOADING
        
        start_time = time.time()
        
        try:
            # Step 0: Initialize libraries
            await self._update_progress(model_id, 0, 5, start_time, "Initializing libraries")
            
            if not _check_torch_availability() or not _check_transformers_availability():
                raise RuntimeError("Transformers/PyTorch not available - cannot load models")
            
            # Get pre-imported modules
            torch = get_torch_module()
            transformers_modules = get_transformers_modules()
            AutoModelForSpeechSeq2Seq = transformers_modules['AutoModelForSpeechSeq2Seq']
            AutoProcessor = transformers_modules['AutoProcessor']
            
            # Check for accelerate
            has_accelerate = _check_accelerate_availability()
            
            # Step 1: Validate model
            await self._update_progress(model_id, 1, 15, start_time, "Validating model")
            
            model_metadata = self.model_registry.get_model(model_id)
            if not model_metadata:
                raise ValueError(f"Model '{model_id}' not found in registry")
            
            # Validate model files
            validation_report = await self.model_validator.validate_model(model_id)
            if not validation_report.is_valid:
                error_msg = f"Model validation failed: {validation_report.errors[0].message if validation_report.errors else 'Unknown error'}"
                raise ValueError(error_msg)
            
            # Step 2: Check hardware compatibility
            await self._update_progress(model_id, 2, 25, start_time, "Checking hardware compatibility")
            
            model_size = validation_report.total_size
            can_load, reason = self.hardware_detector.check_model_compatibility(model_size)
            if not can_load:
                raise MemoryError(f"Cannot load model: {reason}")
            
            # Get optimal loading strategy
            loading_strategy = self.hardware_detector.get_optimal_loading_strategy(model_size)
            
            # Step 3: Cleanup memory and unload current model
            await self._update_progress(model_id, 3, 35, start_time, "Cleaning up memory")
            
            if self._loaded_model:
                await self._unload_current_model()
            
            self.hardware_detector.cleanup_memory()
            
            # Step 4: Load model files
            await self._update_progress(model_id, 4, 45, start_time, "Loading model files")
            
            model_path = self.storage_manager.get_model_directory(model_id)
            logger.debug(f"Model path for {model_id}: {model_path}")
            
            if not model_path.exists():
                raise ValueError(f"Model '{model_id}' not found locally at {model_path}. Please download the model first.")
            
            # Debug the loading strategy we already got earlier
            logger.debug(f"Loading strategy: {loading_strategy}")
            
            # Ensure safe defaults if strategy values are None
            device = loading_strategy.get("device") or "cpu"
            dtype_str = loading_strategy.get("dtype") or "float32"
            torch_dtype = torch.float16 if dtype_str == "float16" else torch.float32
            use_device_mapping = loading_strategy.get("use_device_mapping", False)
            attn_implementation = loading_strategy.get("attn_implementation", "sdpa")
            
            logger.debug(f"Using device: {device}, dtype: {dtype_str}, attention: {attn_implementation}")
            logger.debug(f"Loading model from: {model_path}")
            logger.debug(f"Model files: {list(model_path.glob('*'))}")
            
            # Step 5: Load processor
            await self._update_progress(model_id, 5, 55, start_time, "Loading processor")
            
            try:
                logger.debug(f"Loading processor from: {str(model_path)}")
                processor = AutoProcessor.from_pretrained(model_path)
                logger.debug("Processor loaded successfully")
            except Exception as e:
                logger.error(f"Failed to load processor: {e}")
                raise RuntimeError(f"Processor loading failed: {e}")
            
            # Step 6: Load model weights
            await self._update_progress(model_id, 6, 75, start_time, "Loading model weights")
            
            try:
                # Prepare loading kwargs with optimizations
                loading_kwargs = {
                    "torch_dtype": torch_dtype,
                    "low_cpu_mem_usage": True,
                }
                
                # Add device_map for GPU if available
                if device == "cuda":
                    if use_device_mapping and has_accelerate:
                        logger.debug("Using device mapping for efficient model loading")
                        loading_kwargs["device_map"] = "auto"
                    else:
                        # We'll move the model to device later if we don't use device_map
                        pass
                
                # Add attention implementation if available in transformers version
                try:
                    # Test if attn_implementation is a valid parameter
                    from inspect import signature
                    sig = signature(AutoModelForSpeechSeq2Seq.from_pretrained)
                    if "attn_implementation" in sig.parameters:
                        loading_kwargs["attn_implementation"] = attn_implementation
                        logger.debug(f"Using {attn_implementation} attention implementation")
                except Exception as e:
                    logger.debug(f"attn_implementation parameter not available: {e}")
                
                # Log what we're doing
                logger.debug(f"Loading model with parameters: {loading_kwargs}")
                
                # Actual model loading
                model = AutoModelForSpeechSeq2Seq.from_pretrained(
                    model_path,
                    **loading_kwargs
                )
                logger.debug("Model loaded successfully")
                
                # Check if we're using optimized attention
                is_optimized = (
                    (device == "cuda" and attn_implementation in ["flash_attention_2", "sdpa"]) or 
                    (device == "cpu" and attn_implementation == "sdpa")
                )
                
            except Exception as e:
                logger.error(f"Failed to load model: {e}")
                raise RuntimeError(f"Model loading failed: {e}")
            
            # Step 7: Move to device if not already done by device_map
            await self._update_progress(model_id, 7, 85, start_time, "Moving to device")
            
            try:
                if not hasattr(model, 'hf_device_map') and device == "cuda":
                    logger.debug(f"Moving model to device: {device}")
                    model = model.to(device)
                    logger.debug("Model moved to device successfully")
            except Exception as e:
                logger.error(f"Failed to move model to device: {e}")
                raise RuntimeError(f"Device transfer failed: {e}")
            
            # Store processor reference for later use
            model.processor = processor
            
            # Step 8: Verify model
            await self._update_progress(model_id, 8, 95, start_time, "Verifying model")
            
            # Quick verification that model loaded correctly
            if not hasattr(model, 'generate'):
                raise RuntimeError("Model loaded but missing generate method")
            
            if not hasattr(model, 'processor'):
                raise RuntimeError("Model loaded but processor not attached")

            # Apply torch.compile optimization for better inference performance
            is_compiled = False
            try:
                # Check if torch.compile is available (PyTorch 2.0+)
                if hasattr(torch, 'compile'):
                    logger.info("Applying torch.compile optimization for faster inference...")

                    # Compile the model with optimizations for inference
                    if device == "cuda":
                        # GPU-specific compilation settings
                        model = torch.compile(
                            model,
                            mode="reduce-overhead",  # Optimize for inference latency
                            fullgraph=False,  # Allow graph breaks for compatibility
                            dynamic=True  # Handle dynamic input shapes
                        )
                        logger.info("✅ Model compiled with torch.compile for GPU - expect 1.5-3x faster inference")
                    else:
                        # CPU-specific compilation settings
                        model = torch.compile(
                            model,
                            mode="reduce-overhead",
                            fullgraph=False,
                            dynamic=True
                        )
                        logger.info("✅ Model compiled with torch.compile for CPU - expect improved performance")

                    is_compiled = True
                else:
                    logger.info("torch.compile not available (requires PyTorch 2.0+)")

            except Exception as e:
                logger.warning(f"Failed to compile model with torch.compile: {e}")
                logger.info("Continuing with uncompiled model - performance may be slower")

            # Get memory usage
            memory_info = self.hardware_detector.get_memory_info()
            if device == "cuda" and memory_info.gpu_memory_used:
                memory_usage = memory_info.gpu_memory_used
            else:
                memory_usage = memory_info.used_system_memory
            
            # Step 9: Finalize
            await self._update_progress(model_id, 9, 100, start_time, "Finalizing")
            
            load_time = time.time() - start_time
            
            loaded_model = LoadedModel(
                model_id=model_id,
                model=model,
                device=device,
                dtype=dtype_str,
                memory_usage=memory_usage,
                load_time=load_time,
                model_size=model_size,
                config=loading_strategy,
                is_optimized=is_optimized,
                is_compiled=is_compiled
            )
            
            self._loaded_model = loaded_model
            self._loading_state = LoadingState.LOADED
            
            # Report optimizations used
            optimization_details = []
            if is_optimized:
                optimization_details.append(f"using {attn_implementation} attention")
            if use_device_mapping:
                optimization_details.append("with device mapping")
            if loading_strategy.get("use_8bit_quantization", False):
                optimization_details.append("with 8-bit quantization")
            if is_compiled:
                optimization_details.append("with torch.compile")
            
            optimization_status = f" ({', '.join(optimization_details)})" if optimization_details else ""
            logger.info(f"Model '{model_id}' loaded successfully in {load_time:.2f}s on {device}{optimization_status}")
            
            # Final progress update
            final_progress = LoadingProgress(
                model_id=model_id,
                state=LoadingState.LOADED,
                progress_percent=100.0,
                current_step="Model loaded successfully",
                total_steps=len(self._loading_steps),
                current_step_index=len(self._loading_steps),
                elapsed_time=load_time,
                memory_usage=memory_usage
            )
            self._notify_progress(final_progress)
            
            return loaded_model
            
        except Exception as e:
            self._loading_state = LoadingState.FAILED
            error_progress = LoadingProgress(
                model_id=model_id,
                state=LoadingState.FAILED,
                progress_percent=0.0,
                current_step="Loading failed",
                total_steps=len(self._loading_steps),
                current_step_index=0,
                elapsed_time=time.time() - start_time,
                error_message=str(e)
            )
            self._notify_progress(error_progress)
            
            logger.error(f"Failed to load model '{model_id}': {e}")
            raise
    
    async def unload_model(self) -> bool:
        """Unload the currently loaded model.
        
        Returns:
            True if model was unloaded successfully
        """
        with self._loading_lock:
            if self._loading_state == LoadingState.NOT_LOADED:
                return True
            
            if self._loading_state == LoadingState.LOADING:
                raise RuntimeError("Cannot unload while loading")
            
            self._loading_state = LoadingState.UNLOADING
        
        try:
            success = await self._unload_current_model()
            self._loading_state = LoadingState.NOT_LOADED
            return success
        except Exception as e:
            self._loading_state = LoadingState.FAILED
            logger.error(f"Failed to unload model: {e}")
            return False
    
    async def _unload_current_model(self) -> bool:
        """Internal method to unload the current model."""
        if not self._loaded_model:
            return True
        
        try:
            model_id = self._loaded_model.model_id
            logger.info(f"Unloading model: {model_id}")
            
            # Clear the model reference
            del self._loaded_model.model
            self._loaded_model = None
            
            # Aggressive memory cleanup
            self.hardware_detector.cleanup_memory()
            
            logger.info(f"Model '{model_id}' unloaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error unloading model: {e}")
            return False
    
    async def switch_model(self, new_model_id: str) -> LoadedModel:
        """Switch to a different model.
        
        Args:
            new_model_id: Model ID to switch to
            
        Returns:
            LoadedModel instance for the new model
        """
        # If same model is already loaded, return it
        if self._loaded_model and self._loaded_model.model_id == new_model_id:
            return self._loaded_model
        
        # Load the new model (this will automatically unload the current one)
        return await self.load_model(new_model_id)
    
    def get_loaded_model(self) -> Optional[LoadedModel]:
        """Get the currently loaded model information.
        
        Returns:
            Information about the loaded model, or None if no model is loaded
        """
        return self._loaded_model
    
    def get_model(self) -> Optional[Any]:
        """Get the actual model object from the currently loaded model.
        
        Returns:
            The model object, or None if no model is loaded
        """
        if self._loaded_model:
            return self._loaded_model.model
        return None
    
    def get_loading_state(self) -> LoadingState:
        """Get the current loading state.
        
        Returns:
            Current LoadingState
        """
        return self._loading_state
    
    def is_model_loaded(self, model_id: str) -> bool:
        """Check if a specific model is currently loaded.
        
        Args:
            model_id: Model ID to check
            
        Returns:
            True if the model is loaded
        """
        return (self._loaded_model is not None and 
                self._loaded_model.model_id == model_id and
                self._loading_state == LoadingState.LOADED)
    
    def get_memory_info(self) -> MemoryInfo:
        """Get current memory usage information.
        
        Returns:
            MemoryInfo with current memory statistics
        """
        return self.hardware_detector.get_memory_info()
    
    def get_hardware_config(self) -> HardwareConfig:
        """Get hardware configuration.
        
        Returns:
            HardwareConfig with system capabilities
        """
        return self.hardware_detector.detect_optimal_config()
    
    async def _update_progress(self, model_id: str, step_index: int, progress_percent: float, 
                             start_time: float, step_description: str) -> None:
        """Update loading progress.
        
        Args:
            model_id: Model being loaded
            step_index: Current step index
            progress_percent: Overall progress percentage
            start_time: Loading start time
            step_description: Description of current step
        """
        elapsed_time = time.time() - start_time
        
        # Estimate remaining time based on progress
        if progress_percent > 0:
            estimated_total = elapsed_time / (progress_percent / 100)
            estimated_remaining = estimated_total - elapsed_time
        else:
            estimated_remaining = None
        
        # Get current memory usage
        memory_info = self.get_memory_info()
        memory_usage = memory_info.used_system_memory
        
        progress = LoadingProgress(
            model_id=model_id,
            state=LoadingState.LOADING,
            progress_percent=progress_percent,
            current_step=step_description,
            total_steps=len(self._loading_steps),
            current_step_index=step_index,
            elapsed_time=elapsed_time,
            estimated_remaining=estimated_remaining,
            memory_usage=memory_usage
        )
        
        self._notify_progress(progress)
        
        # Small delay to allow UI updates
        await asyncio.sleep(0.1)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert loader state to dictionary.
        
        Returns:
            Dictionary representation of loader state
        """
        result = {
            "loading_state": self._loading_state.value,
            "hardware_info": self.hardware_detector.to_dict(),
            "transformers_available": _check_transformers_availability(),
            "accelerate_available": _check_accelerate_availability()
        }
        
        if self._loaded_model:
            # Get the attention implementation used
            attn_implementation = self._loaded_model.config.get("attn_implementation", "default")
            
            result["loaded_model"] = {
                "model_id": self._loaded_model.model_id,
                "device": self._loaded_model.device,
                "dtype": self._loaded_model.dtype,
                "memory_usage": self._loaded_model.memory_usage,
                "load_time": self._loaded_model.load_time,
                "model_size": self._loaded_model.model_size,
                "config": self._loaded_model.config,
                "is_optimized": self._loaded_model.is_optimized,
                "is_compiled": self._loaded_model.is_compiled,
                "optimizations": {
                    "attention_implementation": attn_implementation,
                    "device_mapping": self._loaded_model.config.get("use_device_mapping", False),
                    "quantization": self._loaded_model.config.get("use_8bit_quantization", False),
                    "torch_compile": self._loaded_model.is_compiled
                }
            }
        
        return result 