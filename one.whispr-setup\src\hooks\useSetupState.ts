import { useState, useCallback } from 'react';
import { SetupState } from '@src/types/setup';

/**
 * Hook for managing setup state with clean state transitions
 */
export const useSetupState = () => {
  const [state, setState] = useState<SetupState>({
    // Overall readiness
    launchReady: null,

    // Download state
    downloadNeeded: false,
    downloadReason: '',
    isDownloading: false,
    downloadProgress: null,
    downloadError: null,
    downloadComplete: false,

    // Backend download state
    backendDownloading: false,
    backendProgress: null,
    backendError: null,
    backendComplete: false,

    // Main app state
    mainAppError: null,

    // Overall state
    currentPhase: 'checking'
  });

  // State update helpers
  const updateState = useCallback((updates: Partial<SetupState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const setPhase = useCallback((phase: SetupState['currentPhase']) => {
    setState(prev => ({ ...prev, currentPhase: phase }));
  }, []);

  const setError = useCallback((error: string, type: 'download' | 'backend' | 'mainApp' = 'download') => {
    const errorField = type === 'download' ? 'downloadError' : 
                      type === 'backend' ? 'backendError' : 'mainAppError';
    
    setState(prev => ({
      ...prev,
      [errorField]: error,
      currentPhase: 'error',
      isDownloading: false,
      backendDownloading: false
    }));
  }, []);

  const clearErrors = useCallback(() => {
    setState(prev => ({
      ...prev,
      downloadError: null,
      backendError: null,
      mainAppError: null
    }));
  }, []);

  // Download state helpers
  const setDownloadState = useCallback((downloading: boolean, progress?: any) => {
    setState(prev => ({
      ...prev,
      isDownloading: downloading,
      downloadProgress: progress || prev.downloadProgress,
      downloadError: downloading ? null : prev.downloadError
    }));
  }, []);

  const setDownloadComplete = useCallback(() => {
    setState(prev => ({
      ...prev,
      downloadComplete: true,
      isDownloading: false,
      downloadProgress: null
    }));
  }, []);

  // Backend state helpers
  const setBackendState = useCallback((downloading: boolean, progress?: any) => {
    setState(prev => ({
      ...prev,
      backendDownloading: downloading,
      backendProgress: progress || prev.backendProgress,
      backendError: downloading ? null : prev.backendError,
      currentPhase: downloading ? 'downloading' : prev.currentPhase
    }));
  }, []);

  const setBackendComplete = useCallback(() => {
    setState(prev => ({
      ...prev,
      backendDownloading: false,
      backendComplete: true,
      backendProgress: null,
      backendError: null,
      currentPhase: 'starting'
    }));
  }, []);

  // Launch readiness
  const setLaunchReady = useCallback((readiness: any) => {
    setState(prev => ({
      ...prev,
      launchReady: readiness,
      downloadNeeded: readiness.mainAppNeeded || readiness.backendNeeded,
      downloadReason: readiness.reason,
      currentPhase: readiness.allReady ? 'starting' : 'downloading'
    }));
  }, []);

  return {
    state,
    actions: {
      updateState,
      setPhase,
      setError,
      clearErrors,
      setDownloadState,
      setDownloadComplete,
      setBackendState,
      setBackendComplete,
      setLaunchReady
    }
  };
};
