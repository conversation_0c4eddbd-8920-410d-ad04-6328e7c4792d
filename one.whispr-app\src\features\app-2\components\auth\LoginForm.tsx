/**
 * Login Form
 * Authentication form component matching app-latest LoginStep design exactly
 * Handles email input, social login, and registration flows
 */

import { Card, CardContent } from '@src/components/ui/card';
import { Button } from '@src/components/ui/button';
import { Input } from '@src/components/ui/input';
import { Separator } from '@src/components/ui/separator';
import { Form, FormControl, FormField, FormItem, FormLabel } from '@src/components/ui/form';
import { useForm } from 'react-hook-form';
import { SocialLoginButton } from './SocialLoginButton';
import { ErrorMessage } from '../shared/ErrorMessage';

// Import logo assets
import logoWhite from '@src/assets/one.whispr-white.png';
import logoBlack from '@src/assets/one.whispr-black.png';

// ============================================================================
// TYPES
// ============================================================================

interface LoginFormProps {
  onEmailLogin: (email: string) => void;
  onGoogleLogin: () => void;
  onTwitterLogin: () => void;
  onRegister: () => void;
  loading?: boolean;
  error?: string | null;
  onDismissError?: () => void;
  className?: string;
}

type FormData = {
  email: string;
};

// ============================================================================
// COMPONENT
// ============================================================================

export function LoginForm({
  onEmailLogin,
  onGoogleLogin,
  onTwitterLogin,
  onRegister,
  loading = false,
  error = null,
  onDismissError,
  className
}: LoginFormProps) {
  const form = useForm<FormData>({
    defaultValues: {
      email: '',
    },
  });

  const emailValue = form.watch('email');

  const handleEmailSubmit = (data: FormData) => {
    onEmailLogin(data.email);
  };

  return (
    <div className={`h-full flex items-center justify-center relative pt-8 ${className || ''}`}>
      <div className="w-full max-w-md">
        {/* Welcome header with logo */}
        <div className="absolute left-1/2 -translate-x-1/2 -mt-29 flex flex-col items-center">
          <div className="mb-3">
            <img 
              src={logoWhite} 
              alt="Whispr" 
              width={48} 
              height={48} 
              className="rounded-sm hidden dark:block" 
            />
            <img 
              src={logoBlack} 
              alt="Whispr" 
              width={48} 
              height={48} 
              className="rounded-sm block dark:hidden" 
            />
          </div>
          <h1 className="text-2xl font-semibold">Let's get you started</h1>
        </div>

        {/* Main login card */}
        <Card className="w-full bg-card border-border">
          <CardContent className="space-y-6">
            {/* Error display */}
            {error && (
              <ErrorMessage 
                message={error}
                onDismiss={onDismissError}
              />
            )}

            {/* Email form */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleEmailSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Your email address"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <Button 
                  type="submit"
                  className="w-full" 
                  disabled={!emailValue?.trim() || loading}
                >
                  {loading ? 'Opening...' : 'Continue'}
                </Button>
              </form>
            </Form>
            
            {/* OR divider */}
            <div className="relative flex items-center justify-center">
              <Separator className="absolute w-full" />
              <span className="relative bg-card px-3 text-sm text-muted-foreground">
                OR
              </span>
            </div>
            
            {/* Social login buttons */}
            <div className="space-y-3">
              <SocialLoginButton
                provider="google"
                onLogin={onGoogleLogin}
                loading={loading}
              />
              
              <SocialLoginButton
                provider="twitter"
                onLogin={onTwitterLogin}
                loading={loading}
              />
            </div>
          </CardContent>
        </Card>
        
        {/* Footer with registration link */}
        <div className="mt-6 w-full text-center">
          <p className="text-base text-muted-foreground">
            Don't have an account?{' '}
            <button 
              onClick={onRegister}
              disabled={loading}
              className="font-medium text-primary hover:underline underline-offset-4 disabled:opacity-50"
            >
              Sign Up
            </button>
          </p>
        </div>
        
        {/* Terms and privacy */}
        <div className="absolute bottom-16 left-0 right-0 text-center">
          <p className="text-base text-muted-foreground">
            By continuing, you agree to our{' '}
            <a 
              href="https://whispr.one/terms" 
              className="underline underline-offset-4 hover:text-foreground"
              target="_blank"
              rel="noopener noreferrer"
            >
              Terms of Service
            </a>
            {' '}and{' '}
            <a 
              href="https://whispr.one/privacy" 
              className="underline underline-offset-4 hover:text-foreground"
              target="_blank"
              rel="noopener noreferrer"
            >
              Privacy Policy
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}

export default LoginForm; 