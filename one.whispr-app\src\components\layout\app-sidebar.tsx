import { Link, useLocation } from "react-router-dom";
import { useState, useEffect } from "react";
import { 
  Sidebar, 
  SidebarHeader, 
  SidebarContent, 
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarSeparator,
  useSidebar
} from "@src/components/ui/sidebar";

import { 
  LuBookOpen, 
  LuSettings, 
  LuHistory, 
  LuTextCursorInput,
  LuSpeech,
  LuLayoutDashboard,
  LuBrain,
  LuZap
} from "react-icons/lu";

import { Badge } from "@src/components/ui/badge";
import { Button } from "@src/components/ui/button";
import { Progress } from "@src/components/ui/progress";

// Subscription type
type SubscriptionType = 'free' | 'trial' | 'pro';

// Placeholder components for each page
const Home = () => <div>Home Page Content</div>;
const Modes = () => <div>Modes Page Content</div>;
const TextReplacements = () => <div>Text Replacements Page Content</div>;
const Vocabulary = () => <div>Vocabulary Page Content</div>;
const AiModels = () => <div>AI Models Page Content</div>;
const Settings = () => <div>Settings Page Content</div>;
const History = () => <div>History Page Content</div>;

// Define navigation items
const navigationItems = [
  {
    title: "Home",
    url: "/home",
    icon: LuLayoutDashboard
  },
  { separator: true },
  {
    title: "Modes",
    url: "/modes",
    icon: LuSpeech
  },
  {
    title: "Text Replacements",
    url: "/text-replacements",
    icon: LuTextCursorInput
  },
  {
    title: "Vocabulary",
    url: "/vocabulary",
    icon: LuBookOpen
  },
  { separator: true },
  {
    title: "AI Models",
    url: "/ai-models",
    icon: LuBrain
  },
  {
    title: "Settings",
    url: "/settings",
    icon: LuSettings
  },
  { separator: true },
  {
    title: "History",
    url: "/history", 
    icon: LuHistory
  }
];

/**
 * Component that preloads and manages all page components
 */
export function PreloadedPages() {
  const location = useLocation();
  const currentPath = location.pathname;

  // Preload all components on first render and memoize them
  const [preloadedComponents] = useState(() => {
    return {
      home: <Home />,
      modes: <Modes />,
      textReplacements: <TextReplacements />,
      vocabulary: <Vocabulary />,
      aiModels: <AiModels />,
      settings: <Settings />,
      history: <History />,
    };
  });

  // Log when navigation happens
  useEffect(() => {
    console.log(`Navigated to: ${currentPath}`);
  }, [currentPath]);

  // Render all components with smooth transitions like app-2
  return (
    <div className="relative h-full">
      <div
        className={`absolute inset-0 transition-opacity duration-200 ${
          currentPath === '/home' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
        }`}
      >
        {preloadedComponents.home}
      </div>
      <div
        className={`absolute inset-0 transition-opacity duration-200 ${
          currentPath === '/modes' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
        }`}
      >
        {preloadedComponents.modes}
      </div>
      <div
        className={`absolute inset-0 transition-opacity duration-200 ${
          currentPath === '/text-replacements' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
        }`}
      >
        {preloadedComponents.textReplacements}
      </div>
      <div
        className={`absolute inset-0 transition-opacity duration-200 ${
          currentPath === '/vocabulary' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
        }`}
      >
        {preloadedComponents.vocabulary}
      </div>
      <div
        className={`absolute inset-0 transition-opacity duration-200 ${
          currentPath === '/ai-models' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
        }`}
      >
        {preloadedComponents.aiModels}
      </div>
      <div
        className={`absolute inset-0 transition-opacity duration-200 ${
          currentPath === '/settings' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
        }`}
      >
        {preloadedComponents.settings}
      </div>
      <div
        className={`absolute inset-0 transition-opacity duration-200 ${
          currentPath === '/history' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
        }`}
      >
        {preloadedComponents.history}
      </div>
    </div>
  );
}

/**
 * Sidebar navigation component
 */
export function AppSidebar() {
  const location = useLocation();
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";
  
  // For demo purposes, we'll use a state to toggle between subscription types
  // In a real app, this would come from your auth or subscription service
  const [subscriptionType, setSubscriptionType] = useState<SubscriptionType>('trial');

  // Toggle subscription type for demo purposes
  const toggleSubscription = () => {
    const types: SubscriptionType[] = ['free', 'trial', 'pro'];
    const currentIndex = types.indexOf(subscriptionType);
    const nextIndex = (currentIndex + 1) % types.length;
    setSubscriptionType(types[nextIndex]);
  };
  
  // Automatically toggle subscription type every 3 seconds for demo
  useEffect(() => {
    const timer = setInterval(() => {
      toggleSubscription();
    }, 3000);
    
    // Clean up on unmount
    return () => clearInterval(timer);
  }, [subscriptionType]);

  return (
    <Sidebar collapsible="icon" className="pt-[33px]">

      <SidebarContent className="mt-2">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationItems.map((item, index) => 
                'separator' in item ? (
                  <SidebarSeparator key={index} className="my-3" />
                ) : (
                  <SidebarMenuItem key={item.title}>  
                    <SidebarMenuButton 
                      asChild 
                      isActive={location.pathname === item.url}
                      tooltip={isCollapsed ? item.title : undefined}
                      className="h-10 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:items-center"
                    >
                      <Link 
                        to={'url' in item && item.url ? item.url : '/'}
                        onClick={(event) => {
                          if (event.ctrlKey || event.metaKey) {
                            event.preventDefault(); 
                          }
                        }}
                        className="flex items-center w-full"
                        draggable={false}
                      >
                        <div className="flex items-center justify-center w-[24px]">
                          <item.icon className="size-5" />
                        </div>
                        <span className="ml-1 group-data-[collapsible=icon]:hidden text-base">{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        
        {/* Upgrade to Pro button - only shown for free and trial users */}
        {subscriptionType !== 'pro' && (
          <div className="mt-auto px-2">
            <SidebarGroup>
              <SidebarGroupContent>
                {/* Trial progress - only shown for trial users */}
                {subscriptionType === 'trial' && (
                  <div className="flex flex-col space-y-2 mb-4 group-data-[collapsible=icon]:hidden">
                    <div className="text-base text-sidebar-foreground/80 text-center">
                      15 minutes remaining
                    </div>
                    <Progress value={50} className="h-1.5" />
                  </div>
                )}
                <SidebarMenu>
                  <SidebarMenuItem className="px-1">  
                    <Button 
                      onClick={() => console.log("Upgrade to Pro clicked")}
                      className="w-full h-10 flex items-center justify-center gap-2 bg-sidebar-primary text-sidebar-primary-foreground font-medium hover:bg-sidebar-primary/90"
                    >
                      <LuZap className="size-5" />
                      <span className="text-base group-data-[collapsible=icon]:hidden">Upgrade to Pro</span>
                    </Button>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </div>
        )}
      </SidebarContent>
      <SidebarFooter className="mt-auto">
        <SidebarSeparator />
        <div className="p-3 flex flex-col gap-2 group-data-[collapsible=icon]:hidden">
          <div className="flex items-center justify-center space-x-1.5 footer">
            <div className="flex items-center hover-trigger">
              <span className="text-sm font-medium text-sidebar-foreground">One Whispr</span>
              <span className="text-sm text-sidebar-foreground ml-1 opacity-0 max-w-0 overflow-hidden transition-all duration-300 version">v1.0.0</span>
            </div>
            {/* Only show badge for trial and pro users */}
            {subscriptionType !== 'free' && (
              <Badge 
                className="px-1.5 py-0 h-5 text-[10px] bg-sidebar-primary text-sidebar-primary-foreground border-0 font-semibold hover:bg-sidebar-primary/90 hover-trigger"
              >
                {subscriptionType === 'trial' ? 'TRIAL' : 'PRO'}
              </Badge>
            )}
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
}
