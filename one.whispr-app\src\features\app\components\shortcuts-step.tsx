import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription, Card<PERSON>ontent, CardFooter } from '@src/components/ui/card';
import { Button } from '@src/components/ui/button';
import { Label } from '@src/components/ui/label';
import { Input } from '@src/components/ui/input';
import { useSetup, useShortcuts } from '../context';
import { RecordingMode } from '../types';
import { RiMicLine, RiStopLine, RiKeyboardLine, RiRefreshLine, RiHeadphoneLine, RiCheckboxCircleLine, RiSoundModuleLine } from 'react-icons/ri';

export function ShortcutsStep() {
  const { 
    goToNextStep, 
    goToPreviousStep
  } = useSetup();
  
  const {
    shortcuts, 
    recordingMode,
    updateRecordingMode
  } = useShortcuts();

  const resetToDefaults = () => {
    // Reset functionality could be implemented later
    console.log("Reset to defaults clicked");
  };
  
  const handleRecordingModeChange = (mode: RecordingMode) => () => {
    updateRecordingMode(mode);
  };
  
  // Format shortcut keys array to display string
  const formatShortcut = (keys: string[]) => {
    return keys.join(' + ');
  };
  
  // Helper function to render a shortcut input
  const renderShortcutInput = (action: keyof typeof shortcuts, icon: React.ReactNode, label: string) => (
    <div className="space-y-2">
      <Label htmlFor={action} className="flex items-center gap-1.5 text-sm">
        {icon} {label}
      </Label>
      <div className="relative">
        <Input 
          id={action}
          value={formatShortcut(shortcuts[action])}
          readOnly
          className="font-mono text-sm bg-muted/30"
          placeholder="Keyboard shortcut"
        />
      </div>
    </div>
  );
  
  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center space-y-1">
        <CardTitle className="text-2xl">Configure Shortcuts</CardTitle>
        <CardDescription>
          Choose recording mode and set keyboard shortcuts
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <Label className="text-sm flex items-center gap-1.5 font-medium">
            <RiSoundModuleLine size={16} /> Recording Mode
          </Label>
          <div className="grid grid-cols-2 gap-3">
            <div 
              className={`border rounded-md p-3 cursor-pointer transition-all ${
                recordingMode === 'toggle' 
                  ? 'border-primary bg-primary/5' 
                  : 'hover:border-primary/30 hover:bg-muted/50'
              }`}
              onClick={handleRecordingModeChange('toggle')}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="font-medium flex items-center gap-1.5">
                  <RiMicLine size={16} /> Toggle Mode
                </div>
                {recordingMode === 'toggle' && (
                  <RiCheckboxCircleLine size={16} className="text-primary" />
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Press once to start recording, press again to stop
              </p>
            </div>
            
            <div 
              className={`border rounded-md p-3 cursor-pointer transition-all ${
                recordingMode === 'pushToTalk' 
                  ? 'border-primary bg-primary/5' 
                  : 'hover:border-primary/30 hover:bg-muted/50'
              }`}
              onClick={handleRecordingModeChange('pushToTalk')}
            >
              <div className="flex items-center justify-between mb-2">
                <div className="font-medium flex items-center gap-1.5">
                  <RiHeadphoneLine size={16} /> Push To Talk
                </div>
                {recordingMode === 'pushToTalk' && (
                  <RiCheckboxCircleLine size={16} className="text-primary" />
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                Hold key down to record, release to stop
              </p>
            </div>
          </div>
        </div>
        
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-1.5">
            <RiKeyboardLine size={16} /> Active Shortcuts
          </h3>
          
          <div className="grid grid-cols-1 gap-4">
            {recordingMode === 'pushToTalk' ? (
              <>
                {renderShortcutInput('pushToTalk', <RiMicLine size={16} />, 'Push To Talk')}
              </>
            ) : (
              <>
                {renderShortcutInput('toggle', <RiMicLine size={16} />, 'Toggle Recording')}
                {renderShortcutInput('cancel', <RiStopLine size={16} />, 'Cancel Recording')}
              </>
            )}
            {renderShortcutInput('modeSwitch', <RiKeyboardLine size={16} />, 'Mode Switch')}
          </div>
          
          <Button 
            variant="outline" 
            size="sm" 
            className="w-full flex gap-1 items-center justify-center border-dashed mt-2"
            onClick={resetToDefaults}
          >
            <RiRefreshLine size={14} /> Reset to Default Shortcuts
          </Button>
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={goToPreviousStep}>Back</Button>
        <Button onClick={goToNextStep}>Continue</Button>
      </CardFooter>
    </Card>
  );
} 