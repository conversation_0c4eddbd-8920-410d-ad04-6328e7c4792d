import { useSetup } from '../context';
import { LuCheck } from 'react-icons/lu';
import { SetupStep } from '../types';

export function SetupProgress() {
  const { currentStep, goToStep } = useSetup();
  
  const steps: Record<SetupStep, {
    label: string;
    canNavigateTo: boolean;
  }> = {
    'welcome': {
      label: 'Welcome',
      canNavigateTo: true
    },
    'download-model': {
      label: 'Voice Model',
      canNavigateTo: true
    },
    'audio-setup': {
      label: 'Audio',
      canNavigateTo: true
    },
    'shortcuts': {
      label: 'Shortcuts',
      canNavigateTo: true
    },
    'complete': {
      label: 'Complete',
      canNavigateTo: false // Can't skip to complete
    }
  };
  
  const stepKeys = Object.keys(steps) as SetupStep[];
  const currentStepIndex = stepKeys.indexOf(currentStep);
  
  // Handle step click - only allow navigating to previous or available steps
  const handleStepClick = (step: SetupStep) => {
    const stepIndex = stepKeys.indexOf(step);
    const isClickable = steps[step].canNavigateTo && stepIndex <= currentStepIndex;
    
    if (isClickable) {
      goToStep(step);
    }
  };
  
  return (
    <div className="w-full max-w-md mb-6">
      {/* Step labels - now using grid to ensure proper alignment */}
      <div className="grid grid-cols-5 mb-2">
        {stepKeys.map((step, index) => {
          const isPreviousStep = index < currentStepIndex;
          const isCurrentStep = step === currentStep;
          const isClickable = steps[step].canNavigateTo && index <= currentStepIndex;
          
          return (
            <div 
              key={`label-${step}`}
              className="flex justify-center"
            >
              <span 
                className={`text-xs font-medium ${
                  isCurrentStep 
                    ? 'text-primary' 
                    : isPreviousStep
                      ? 'text-muted-foreground'
                      : 'text-muted-foreground/50'
                } ${isClickable ? 'cursor-pointer hover:text-primary' : ''}`}
                onClick={() => handleStepClick(step)}
              >
                {steps[step].label}
              </span>
            </div>
          );
        })}
      </div>
      
      {/* Progress indicators */}
      <div className="grid grid-cols-5 gap-1">
        {stepKeys.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isCurrent = index === currentStepIndex;
          const isClickable = steps[step].canNavigateTo && index <= currentStepIndex;
          
          return (
            <div 
              key={`indicator-${step}`}
              className={`h-2 rounded-full transition-all ${
                isCompleted 
                  ? 'bg-primary/90' 
                  : isCurrent 
                    ? 'bg-primary' 
                    : 'bg-muted'
              } ${isClickable ? 'cursor-pointer' : ''}`}
              onClick={() => handleStepClick(step)}
            />
          );
        })}
      </div>
      
      {/* Step circles for completed steps */}
      <div className="grid grid-cols-5 -mt-[9px]">
        {stepKeys.map((step, index) => {
          const isCompleted = index < currentStepIndex;
          const isClickable = steps[step].canNavigateTo && index <= currentStepIndex;
          
          return isCompleted ? (
            <div 
              key={`circle-${step}`}
              className={`flex justify-center ${isClickable ? 'cursor-pointer' : ''}`}
              onClick={() => handleStepClick(step)}
            >
              <div className="w-4 h-4 rounded-full bg-primary flex items-center justify-center">
                <LuCheck size={10} className="text-primary-foreground" />
              </div>
            </div>
          ) : (
            <div key={`circle-${step}`}></div>
          );
        })}
      </div>
    </div>
  );
} 