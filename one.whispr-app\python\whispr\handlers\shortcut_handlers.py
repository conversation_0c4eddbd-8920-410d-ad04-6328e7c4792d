"""
Shortcut command handlers for One.Whispr.

This module contains handlers for keyboard shortcut configuration commands.
"""

import logging
from typing import Dict, Any, TYPE_CHECKING

from whispr.core.response_utils import success_response, error_response, ErrorCodes

if TYPE_CHECKING:
    from whispr.core.handlers import CommandHandlers

# Configure logging
logger = logging.getLogger('whispr.handlers.shortcut')


async def handle_start_shortcut_update(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle start shortcut update request from the client.
    
    Args:
        params: The command parameters containing target
        context: The execution context
        
    Returns:
        Success response with operation status
    """
    try:
        target = params.get('target')
        logger.info(f"Received shortcut update request with target: {target}")
        
        if not target:
            logger.error("No target provided for shortcut update")
            return error_response("No target provided for shortcut update", ErrorCodes.PARAMETER_ERROR)

        service_container = context.get('service_container')
        if service_container:
            # Get shortcut service from service container
            shortcut_service = service_container.resolve("shortcut")
            if shortcut_service:
                # Start shortcut recording
                shortcut_service.start_shortcut_recording(target)
                logger.info(f"Shortcut recording started for target: {target}")
                return success_response(None, f"Started recording shortcut for target: {target}")
            else:
                logger.warning("ShortcutService not available in service container")
                return error_response("ShortcutService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        else:
            logger.warning("No service container available for shortcut update")
            return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)
    except Exception as e:
        logger.error(f"Error starting shortcut update: {e}", exc_info=True)
        return error_response(f"Error starting shortcut update: {str(e)}", ErrorCodes.UNKNOWN_ERROR)


async def handle_shortcut_test_mode(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Enter shortcut testing mode for setup flow.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response with operation result
    """
    try:
        logger.info("Received request to start shortcut test mode")
        
        service_container = context.get('service_container')
        if not service_container:
            logger.error("Service container not available for shortcut test mode")
            return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Check if transcription is active and stop it first
        transcription_service = service_container.resolve("transcription")
        if transcription_service:
            try:
                if transcription_service.is_transcribing:
                    logger.info("Stopping active transcription before enabling test mode")
                    await transcription_service.stop_transcription()
                    logger.info("Successfully stopped transcription")
            except Exception as e:
                logger.error(f"Error stopping transcription: {e}")
        
        # Get shortcut service
        shortcut_service = service_container.resolve("shortcut")
        if not shortcut_service:
            logger.error("ShortcutService not available for shortcut test mode")
            return error_response("ShortcutService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Enable test mode using the new method
        shortcut_service.enable_test_mode()
        
        logger.info("Shortcut test mode started successfully")
        return success_response({"test_mode": True}, "Shortcut test mode started")
        
    except Exception as e:
        logger.error(f"Error starting shortcut test mode: {e}", exc_info=True)
        return error_response(str(e), ErrorCodes.UNKNOWN_ERROR)


async def handle_shortcut_exit_test_mode(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Exit shortcut testing mode.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response with operation result
    """
    try:
        logger.info("Received request to stop shortcut test mode")
        
        service_container = context.get('service_container')
        if not service_container:
            logger.error("Service container not available for exiting shortcut test mode")
            return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        shortcut_service = service_container.resolve("shortcut")
        if not shortcut_service:
            logger.error("ShortcutService not available for exiting shortcut test mode")
            return error_response("ShortcutService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Disable test mode using the new method
        shortcut_service.disable_test_mode()
        
        logger.info("Shortcut test mode stopped successfully")
        return success_response({"test_mode": False}, "Shortcut test mode stopped")
        
    except Exception as e:
        logger.error(f"Error stopping shortcut test mode: {e}", exc_info=True)
        return error_response(str(e), ErrorCodes.UNKNOWN_ERROR)


def register_handlers(command_handlers: 'CommandHandlers') -> None:
    """Register shortcut handlers with the command handlers.
    
    Args:
        command_handlers: The CommandHandlers instance to register with
    """
    logger.debug("Registering shortcut handlers")
    
    command_handlers.registry.register_function("shortcut.start_update", handle_start_shortcut_update)
    
    # Setup flow test mode commands
    command_handlers.registry.register_function("shortcut.test_mode", handle_shortcut_test_mode)
    command_handlers.registry.register_function("shortcut.exit_test_mode", handle_shortcut_exit_test_mode)
    
    logger.info("Shortcut handlers registered successfully") 