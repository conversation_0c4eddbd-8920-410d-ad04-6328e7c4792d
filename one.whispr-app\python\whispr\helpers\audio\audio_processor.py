"""Audio processing middleware for consumer callbacks."""

import logging
import numpy as np
from typing import Dict, Any, Callable, Optional

from .silero_vad import SileroVAD
from .audio_manager import AudioChunk, AudioConsumerType

logger = logging.getLogger(__name__)


class AudioProcessor:
    """Audio processing middleware that wraps consumer callbacks with VAD filtering."""

    def __init__(self, settings: Optional[Dict[str, Any]] = None):
        """Initialize audio processor.

        Args:
            settings: Audio processing settings (required - no defaults)
        """
        if settings is None:
            raise ValueError("AudioProcessor requires settings to be provided")
        self.settings = settings.copy()
        
        # Processing components (pre-loaded to prevent startup delay)
        self.silero_vad: Optional[SileroVAD] = None
        
        # Wrapped callbacks
        self.wrapped_callbacks: Dict[str, Callable] = {}
        self.original_callbacks: Dict[str, Callable] = {}
        
        # Pre-load VAD model during initialization to prevent first-chunk delay
        self._initialize_vad()
        
        logger.info("AudioProcessor initialized")
    
    def _initialize_vad(self) -> None:
        """Pre-load VAD model during initialization to prevent startup delay."""
        try:
            logger.info("Pre-loading Silero VAD model...")
            self.silero_vad = SileroVAD(
                sample_rate=16000,  # Default sample rate
                base_threshold=self.settings.get('vad_threshold', 0.5)
            )
            if self.silero_vad.initialize():
                logger.info("Silero VAD model pre-loaded successfully")
            else:
                logger.warning("Failed to pre-load VAD model, will fall back to on-demand loading")
                self.silero_vad = None
        except Exception as e:
            logger.warning(f"Failed to pre-load VAD model: {e}, will fall back to on-demand loading")
            self.silero_vad = None
    
    def wrap_callback(self, consumer_type: AudioConsumerType, original_callback: Callable) -> Callable:
        """Wrap a consumer callback with processing.
        
        Args:
            consumer_type: Type of consumer
            original_callback: Original callback to wrap
            
        Returns:
            Wrapped callback with processing
        """
        def wrapped_callback(audio_chunk: AudioChunk):
            """Enhanced callback with VAD processing."""
            try:
                # Apply VAD filtering based on consumer type
                if self.settings.get('enable_vad_filtering', False):
                    if consumer_type == AudioConsumerType.TRANSCRIPTION:
                        # Always apply VAD to transcription
                        if self._should_process_for_speech(audio_chunk):
                            original_callback(audio_chunk)
                        else:
                            # Skip if no speech detected (removing debug spam)
                            pass
                        return
                    elif consumer_type == AudioConsumerType.RECORDING and self.settings.get('apply_vad_to_recording', False):
                        # Optionally apply VAD to recording
                        if self._should_process_for_speech(audio_chunk):
                            original_callback(audio_chunk)
                        else:
                            # Skip if no speech detected (removing debug spam)
                            pass
                        return
                
                # For UI levels and non-VAD cases, always process
                original_callback(audio_chunk)
                
            except Exception as e:
                logger.error(f"Error in wrapped callback for {consumer_type}: {e}")
                # Fallback to original callback
                try:
                    original_callback(audio_chunk)
                except Exception as fallback_error:
                    logger.error(f"Fallback callback also failed: {fallback_error}")
        
        # Store references
        self.wrapped_callbacks[str(consumer_type)] = wrapped_callback
        self.original_callbacks[str(consumer_type)] = original_callback
        
        return wrapped_callback
    

    
    def _should_process_for_speech(self, audio_chunk: AudioChunk) -> bool:
        """Check if audio chunk contains speech using VAD.
        
        Args:
            audio_chunk: Audio chunk to analyze
            
        Returns:
            True if speech is detected
        """
        try:
            # Use pre-loaded VAD, fall back to on-demand loading if needed
            if self.silero_vad is None:
                logger.warning("VAD not pre-loaded, initializing on-demand")
                self.silero_vad = SileroVAD(
                    sample_rate=audio_chunk.sample_rate,
                    base_threshold=self.settings.get('vad_threshold', 0.5)
                )
                if not self.silero_vad.initialize():
                    logger.error("Failed to initialize VAD")
                    return True  # Default to processing if VAD fails
            
            # Check if VAD sample rate matches audio chunk sample rate
            if self.silero_vad.sample_rate != audio_chunk.sample_rate:
                logger.warning(f"VAD sample rate {self.silero_vad.sample_rate} doesn't match chunk rate {audio_chunk.sample_rate}, reinitializing")
                self.silero_vad = SileroVAD(
                    sample_rate=audio_chunk.sample_rate,
                    base_threshold=self.settings.get('vad_threshold', 0.5)
                )
                if not self.silero_vad.initialize():
                    logger.error("Failed to reinitialize VAD with correct sample rate")
                    return True  # Default to processing if VAD fails
            
            # Analyze for speech
            vad_result = self.silero_vad.process_chunk(audio_chunk.data)
            is_speech = vad_result.get('is_speech', False)
            probability = vad_result.get('probability', 0.0)
            adaptive_threshold = vad_result.get('adaptive_threshold', 0.5)
            
            if is_speech:
                logger.debug(f"Speech detected (VAD: {probability:.3f}, threshold: {adaptive_threshold:.3f})")
            
            return is_speech
            
        except Exception as e:
            logger.error(f"Error in VAD processing: {e}")
            return True  # Default to processing if VAD fails
    
    def cleanup(self) -> None:
        """Cleanup audio processor resources."""
        try:
            if self.silero_vad:
                self.silero_vad.cleanup()
                self.silero_vad = None
            
            self.wrapped_callbacks.clear()
            self.original_callbacks.clear()
            
            logger.info("AudioProcessor cleaned up")
        except Exception as e:
            logger.error(f"Error during AudioProcessor cleanup: {e}")
    
    def update_settings(self, new_settings: Dict[str, Any]) -> bool:
        """Update processing settings.
        
        Args:
            new_settings: New settings dictionary
            
        Returns:
            True if update successful
        """
        try:
            self.settings.update(new_settings)
            
            # Update VAD threshold if changed and component exists
            if self.silero_vad and 'vad_threshold' in new_settings:
                self.silero_vad.set_base_threshold(new_settings['vad_threshold'])
            
            logger.info("Audio processing settings updated")
            return True
            
        except Exception as e:
            logger.error(f"Error updating processing settings: {e}")
            return False
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics.
        
        Returns:
            Dictionary with processing statistics
        """
        stats = {
            "settings": {
                "vad_filtering_enabled": self.settings.get('enable_vad_filtering', False),
                "vad_threshold": self.settings.get('vad_threshold', 0.5),
                "vad_applied_to_recording": self.settings.get('apply_vad_to_recording', False)
            },
            "components": {
                "silero_vad_initialized": self.silero_vad is not None
            }
        }
        
        # Add component statistics
        if self.silero_vad:
            stats["vad"] = self.silero_vad.get_statistics()
        
        return stats
    
    def cleanup(self) -> None:
        """Cleanup processing resources."""
        try:
            if self.silero_vad:
                self.silero_vad.cleanup()
                self.silero_vad = None
            
            self.wrapped_callbacks.clear()
            self.original_callbacks.clear()
            
            logger.info("AudioProcessor cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during AudioProcessor cleanup: {e}") 