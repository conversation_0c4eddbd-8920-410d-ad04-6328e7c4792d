"""
Transcription helpers for One.Whispr.

This package provides transcription processing components including the
core transcription engine and supporting utilities for real-time sliding window transcription.
"""

from .transcription_engine import TranscriptionEngine
from .transcription_types import ProcessingMode, TranscriptionResult
from .transcription_events import TranscriptionEventEmitter
from .transcription_session import Transcription<PERSON>ession<PERSON>anager
from .transcription_inference import TranscriptionInferenceEngine
from .realtime_segment_manager import RealtimeSegmentManager

__version__ = "1.0.0"
__all__ = [
    # Core engine
    'TranscriptionEngine',
    
    # Types and enums
    'ProcessingMode',
    'TranscriptionResult',
    
    # Main components (exposed for advanced usage)
    'TranscriptionEventEmitter',
    'TranscriptionSessionManager', 
    'TranscriptionInferenceEngine',
    'RealtimeSegmentManager'
]

# Note: Most users should only need TranscriptionEngine, ProcessingMode, and TranscriptionResult.
# The other components are exposed for advanced usage, testing, and debugging.

# Additional helper modules will be added as they are implemented:
# - VocabularySystem
# - WhisperPromptGenerator  
# - TextProcessingPipeline 