import { ipcMain, shell } from 'electron';
import { AUTH_URLS, IS_DEV } from '../../constants';
import { AuthService } from './service';
import { 
  registerProtocolHandler as registerProtocol, 
  handleStartupProtocolUrl as handleStartupUrl 
} from './protocol-handler';

// Global auth service instance
let authService: AuthService;

/**
 * Sets up all app-related IPC handlers including authentication
 * Should be called during app initialization
 */
export function setupAppIPCHandlers(): void {
  // Initialize auth service
  authService = new AuthService();
  authService.initialize();
  
  // Register protocol handlers
  registerProtocol();
  
  // Handle any protocol URLs passed during app launch
  const startupUrlHandled = handleStartupUrl();
  if (startupUrlHandled) {
    console.log('Handled startup protocol URL');
  }

  // Handle opening external URLs (for auth flows)
  ipcMain.handle('open-external-url', async (_event, url: string) => {
    // Allow http URLs in development mode, but require https in production
    const isSecureUrl = IS_DEV 
      ? (url.startsWith('http://') || url.startsWith('https://'))
      : url.startsWith('https://');
      
    if (isSecureUrl) {
      await shell.openExternal(url);
      return true;
    }
    return false;
  });
  
  // Provide auth URLs to the renderer process
  ipcMain.handle('get-auth-urls', () => {
    return AUTH_URLS;
  });

  // === AUTHENTICATION IPC HANDLERS ===

  // Get authentication status
  ipcMain.handle('auth:getStatus', () => {
    try {
      return authService.isAuthenticated();
    } catch (error) {
      console.error('Error getting auth status:', error);
      return { isAuthenticated: false };
    }
  });

  // Get current user's access token
  ipcMain.handle('auth:getAccessToken', () => {
    try {
      return authService.getAccessToken();
    } catch (error) {
      console.error('Error getting access token:', error);
      return null;
    }
  });

  // Logout current user
  ipcMain.handle('auth:logout', () => {
    try {
      return authService.logout();
    } catch (error) {
      console.error('Error during logout:', error);
      return false;
    }
  });

  // Update user activity
  ipcMain.handle('auth:updateActivity', () => {
    try {
      return authService.updateActivity();
    } catch (error) {
      console.error('Error updating activity:', error);
      return false;
    }
  });

  // Get current user
  ipcMain.handle('auth:getCurrentUser', () => {
    try {
      return authService.getCurrentUser();
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  });

  // Get user settings
  ipcMain.handle('auth:getUserSettings', (_event, userId?: string) => {
    try {
      return authService.getUserSettings(userId);
    } catch (error) {
      console.error('Error getting user settings:', error);
      return null;
    }
  });

  // Handle authentication callback (called when protocol URL is processed)
  ipcMain.on('auth:processCallback', async (_event, callbackData) => {
    try {
      await authService.processOAuthCallback(callbackData);
    } catch (error) {
      console.error('Error processing auth callback:', error);
    }
  });
} 