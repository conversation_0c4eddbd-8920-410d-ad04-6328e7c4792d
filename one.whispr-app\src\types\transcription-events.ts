/**
 * TypeScript interfaces for transcription events from the Python backend.
 * 
 * These events are broadcast in real-time during transcription sessions
 * and can be received via the electron.onBackendMessage() API.
 */

export type TranscriptionEventType = 
  | 'transcription.result'
  | 'transcription.partial' 
  | 'transcription.final'
  | 'transcription.speaker'
  | 'transcription.error'
  | 'transcription.status'
  | 'transcription.session_start'
  | 'transcription.session_end'
  | 'transcription.vocabulary_correction'
  | 'transcription.text_replacement';

export interface TranscriptionEvent {
  event_type: TranscriptionEventType;
  session_id: string;
  timestamp: number;
  iso_timestamp: string;
  event_id: string;
  sequence_number: number;
  speaker_id?: string;
  confidence?: number;
  processing_time_ms?: number;
  data: Record<string, any>;
}

export interface TranscriptionEventMessage {
  type: 'transcription_event';
  event: TranscriptionEvent;
}

export interface TranscriptionEventBatchMessage {
  type: 'transcription_event_batch';
  events: TranscriptionEvent[];
  batch_size: number;
}

export type TranscriptionMessage = TranscriptionEventMessage | TranscriptionEventBatchMessage;

/**
 * Specific event data interfaces for different event types
 */

export interface TranscriptionResultData {
  text: string;
  is_final: boolean;
  start_time?: number;
  end_time?: number;
  word_count: number;
  vocabulary_corrections?: VocabularyCorrection[];
  text_replacements?: TextReplacement[];
}

export interface SpeakerEventData {
  speaker_info: {
    name?: string;
    confidence?: number;
    [key: string]: any;
  };
  action: 'identified' | 'updated' | 'removed';
}

export interface ErrorEventData {
  error_message: string;
  error_code?: string;
  recoverable: boolean;
}

export interface StatusEventData {
  processing_time?: number;
  buffer_size?: number;
  performance_metrics?: Record<string, any>;
  [key: string]: any;
}

export interface SessionStartEventData {
  session_id: string;
  mode: string;
  mode_id?: string;
  configuration: Record<string, any>;
  listen_to_speaker: boolean;
  vocabulary_enabled: boolean;
  diarization_enabled: boolean;
  text_processing_enabled: boolean;
}

export interface SessionEndEventData {
  session_id: string;
  duration_seconds: number;
  chunks_processed: number;
  words_transcribed: number;
  average_confidence: number;
  processing_time_ms: number;
}

export interface VocabularyCorrection {
  original_word: string;
  corrected_word: string;
  confidence: number;
  algorithm: string;
  position: number;
}

export interface VocabularyCorrectionEventData {
  original_text: string;
  corrections: VocabularyCorrection[];
  correction_count: number;
}

export interface TextReplacement {
  pattern: string;
  replacement: string;
  is_regex: boolean;
  [key: string]: any;
}

export interface TextReplacementEventData {
  original_text: string;
  replacements: TextReplacement[];
  replacement_count: number;
}

/**
 * Type guard functions to help identify event types
 */

export function isTranscriptionEvent(message: any): message is TranscriptionEventMessage {
  return message?.type === 'transcription_event' && message?.event;
}

export function isTranscriptionEventBatch(message: any): message is TranscriptionEventBatchMessage {
  return message?.type === 'transcription_event_batch' && Array.isArray(message?.events);
}

export function isTranscriptionMessage(message: any): message is TranscriptionMessage {
  return isTranscriptionEvent(message) || isTranscriptionEventBatch(message);
}

/**
 * Helper functions to extract typed data from events
 */

export function getResultData(event: TranscriptionEvent): TranscriptionResultData | null {
  if (event.event_type === 'transcription.partial' || 
      event.event_type === 'transcription.final' ||
      event.event_type === 'transcription.result') {
    return event.data as TranscriptionResultData;
  }
  return null;
}

export function getSpeakerData(event: TranscriptionEvent): SpeakerEventData | null {
  if (event.event_type === 'transcription.speaker') {
    return event.data as SpeakerEventData;
  }
  return null;
}

export function getErrorData(event: TranscriptionEvent): ErrorEventData | null {
  if (event.event_type === 'transcription.error') {
    return event.data as ErrorEventData;
  }
  return null;
}

export function getStatusData(event: TranscriptionEvent): StatusEventData | null {
  if (event.event_type === 'transcription.status') {
    return event.data as StatusEventData;
  }
  return null;
}

export function getSessionStartData(event: TranscriptionEvent): SessionStartEventData | null {
  if (event.event_type === 'transcription.session_start') {
    return event.data as SessionStartEventData;
  }
  return null;
}

export function getSessionEndData(event: TranscriptionEvent): SessionEndEventData | null {
  if (event.event_type === 'transcription.session_end') {
    return event.data as SessionEndEventData;
  }
  return null;
}

export function getVocabularyCorrectionData(event: TranscriptionEvent): VocabularyCorrectionEventData | null {
  if (event.event_type === 'transcription.vocabulary_correction') {
    return event.data as VocabularyCorrectionEventData;
  }
  return null;
}

export function getTextReplacementData(event: TranscriptionEvent): TextReplacementEventData | null {
  if (event.event_type === 'transcription.text_replacement') {
    return event.data as TextReplacementEventData;
  }
  return null;
}

/**
 * Example usage:
 * 
 * ```typescript
 * import { isTranscriptionEvent, getResultData } from '@/types/transcription-events';
 * 
 * window.electron.onBackendMessage((message) => {
 *   if (isTranscriptionEvent(message)) {
 *     const event = message.event;
 *     
 *     switch (event.event_type) {
 *       case 'transcription.final':
 *         const resultData = getResultData(event);
 *         if (resultData) {
 *           console.log('Final transcription:', resultData.text);
 *         }
 *         break;
 *         
 *       case 'transcription.error':
 *         const errorData = getErrorData(event);
 *         if (errorData) {
 *           console.error('Transcription error:', errorData.error_message);
 *         }
 *         break;
 *     }
 *   }
 * });
 * ```
 */ 