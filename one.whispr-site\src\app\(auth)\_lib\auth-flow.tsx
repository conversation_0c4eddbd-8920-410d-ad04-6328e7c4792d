'use client';

import { ReactNode } from 'react';
import { AuthStep } from '../_components/utils/auth-utils';

export interface AuthFlowStep {
  component: React.ComponentType<any>;
  props?: Record<string, any>;
  title?: string;
}

export interface AuthFlowSteps {
  [key: string]: AuthFlowStep;
}

export interface AuthFlowFooterContent {
  [key: string]: ReactNode;
}

interface AuthFlowControllerProps {
  steps: AuthFlowSteps;
  footerContent?: AuthFlowFooterContent;
  currentStep: string;
  title?: string;
  params: Record<string, any>;
  renderCard: (props: {
    title: string;
    children: ReactNode;
    footerContent?: ReactNode;
  }) => ReactNode;
}

/**
 * Controller component for multi-step authentication flows
 */
export function AuthFlowController({
  steps,
  footerContent = {},
  currentStep,
  title,
  params,
  renderCard,
}: AuthFlowControllerProps) {
  // Check if current step exists in steps
  if (!steps[currentStep]) {
    console.error(`Step ${currentStep} not found in steps configuration`);
    return null;
  }
  
  const { component: StepComponent, props: stepProps = {}, title: stepTitle } = steps[currentStep];
  
  // Get the title for the current step
  const displayTitle = stepTitle || title || 'Authentication';
  
  // Get footer content for the current step
  const currentFooterContent = footerContent[currentStep] || null;

  return renderCard({
    title: displayTitle,
    children: <StepComponent {...params} {...stepProps} />,
    footerContent: currentFooterContent,
  });
} 