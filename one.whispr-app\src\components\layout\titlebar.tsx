import { useEffect, useState, ReactNode } from 'react';
import { <PERSON>Minus, FiSquare, FiX } from 'react-icons/fi';
import { RiCheckboxMultipleBlankLine } from "react-icons/ri";
import iconWhite from "@src/assets/one.whispr-white.png";
import iconBlack from "@src/assets/one.whispr-black.png";

interface TitlebarProps {
  children: ReactNode;
}

/**
 * Main application layout component with titlebar
 */
export function Titlebar({ children }: TitlebarProps) {
  const [currentTheme, setCurrentTheme] = useState<'light' | 'dark'>('dark');
  const [isMaximized, setIsMaximized] = useState(false);
  
  // Detect actual theme from document classes
  useEffect(() => {
    const checkTheme = () => {
      const isDark = document.documentElement.classList.contains('dark');
      setCurrentTheme(isDark ? 'dark' : 'light');
    };
    
    checkTheme();
    const observer = new MutationObserver(mutations => {
      mutations.forEach(mutation => {
        if (mutation.attributeName === 'class') checkTheme();
      });
    });
    
    observer.observe(document.documentElement, { attributes: true });
    return () => observer.disconnect();
  }, []);

  // Detect window maximize state
  useEffect(() => {
    const handleResize = () => {
      const { innerWidth, innerHeight, screen } = window;
      const maximized = innerWidth >= screen.availWidth - 5 && innerHeight >= screen.availHeight - 5;
      setIsMaximized(maximized);
    };
    
    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div className="flex flex-col h-screen">
      {/* Title bar */}
      <div className="titlebar bg-sidebar flex items-center h-[33px] border-sidebar-border">
        {/* App icon section */}
        <div className="flex items-center pl-[18px] pt-[1px] flex-1">
          <img src={currentTheme === 'dark' ? iconWhite : iconBlack} alt="Whispr" className="size-5" />
        </div>
        
        {/* Active mode and window controls */}
        <div className="flex items-center">
          <div className="flex items-center">
            <div className="w-[46px] flex items-center justify-center">
              <FiMinus size={13} />
            </div>
            <div className="w-[44px] flex items-center justify-center">
              {isMaximized ? (
                <RiCheckboxMultipleBlankLine size={13} />
              ) : (
                <FiSquare size={13} />
              )}
            </div>
            <div className="w-[48px] flex items-center justify-center">
              <FiX size={18} />
            </div>
          </div>
        </div>
      </div>
      
      {/* Main content area */}
      <div className="flex flex-1 overflow-hidden">
        {children}
      </div>
    </div>
  );
}
