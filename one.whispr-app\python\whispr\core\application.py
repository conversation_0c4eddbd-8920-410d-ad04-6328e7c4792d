"""
Main application class for One.Whispr.

This module contains the Application class which serves as the entry point
for the Python backend, initializing and managing all services.
"""

import asyncio
import logging
import os
import io
import signal
import sys
from typing import Dict, Any, Optional, List

from whispr.core.base import ServiceContainer
from whispr.core.websocket_server import WebSocketServer
from whispr.core.database_manager import DatabaseManager
from whispr.core.handlers import CommandHandlers
from whispr.core.ipc_bridge import IPCBridge
from whispr.config.manager import ConfigurationManager
from whispr.services.shortcut_service import ShortcutService
from whispr.services.audio_service import AudioService
from whispr.services.model_service import ModelService
from whispr.services.transcription_service import TranscriptionService


class Application:
    """Main application class for One.Whispr."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize the application.
        
        Args:
            config_file: Path to configuration file
        """
        self.config_file = config_file
        self.running = False
        self.service_container = ServiceContainer()
        self.logger = None
        self.port_file = None
        self.main_task = None
        self.config_dependent_services_initialized = False
        
    def setup_logging(self, level: Optional[str] = None) -> None:
        """Setup logging configuration.
        
        Args:
            level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        if not level:
            level = os.environ.get("WHISPR_LOG_LEVEL", "INFO")
            
        numeric_level = getattr(logging, level.upper(), logging.INFO)
        
        # Create a text stream wrapper that handles Unicode
        text_stream = io.TextIOWrapper(
            sys.stdout.buffer, 
            encoding='utf-8', 
            errors='replace'
        )
        
        # Configure logging
        logging.basicConfig(
            level=numeric_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(text_stream)
            ]
        )
        
        # Reduce websockets library verbosity to prevent audio level spam
        websockets_logger = logging.getLogger('websockets')
        websockets_logger.setLevel(logging.WARNING)
        
        self.logger = logging.getLogger("whispr.Application")
        self.logger.info(f"Logging initialized at level {level}")
        self.logger.info("Websockets logging set to WARNING level to reduce verbosity")
    
    async def initialize(self) -> bool:
        """Initialize the application core services.
        
        This initializes only the essential services needed for communication.
        Config-dependent services are initialized later after database sync.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            self.logger.info("Initializing application core services")
            print("Initializing One.Whispr application core...", flush=True)
            
            # Initialize configuration manager
            config_manager = ConfigurationManager(self.config_file)
            config_manager.load()
            self.service_container.register("config", config_manager)

            # Register the application itself in the service container
            self.service_container.register("application", self)
            
            # Initialize database manager
            db_manager = DatabaseManager(self.service_container)
            self.service_container.register("db", db_manager)
            await db_manager.initialize()
            
            # Initialize WebSocket server (before IPC Bridge as it depends on it)
            ws_server = WebSocketServer(self.service_container)
            self.service_container.register("websocket", ws_server)
            await ws_server.initialize()
            
            # Initialize IPC Bridge
            ipc_bridge = IPCBridge(self.service_container)
            self.service_container.register("ipc", ipc_bridge)
            await ipc_bridge.initialize()
            
            # Initialize command handlers for core services
            command_handlers = CommandHandlers(self.service_container)
            self.service_container.register("commands", command_handlers)
            
            # Register core command handlers with WebSocket server
            command_handlers.initialize_websocket_handlers(ws_server)
            
            # Set up special handling for database.initial_sync
            # This ensures the database can sync even before any clients connect
            if hasattr(ws_server, 'startup_complete') and callable(ws_server.startup_complete):
                ws_server.startup_complete()
                self.logger.info("WebSocket server startup complete signal sent")
            
            self.logger.info("Application core services initialized successfully")
            print("Application core services initialized successfully", flush=True)
            return True
        except Exception as e:
            self.logger.error(f"Error initializing application core: {e}", exc_info=True)
            print(f"Error initializing application core: {e}", file=sys.stderr, flush=True)
            return False

    async def initialize_config_dependent_services(self) -> bool:
        """Initialize services that depend on configuration data.
        
        This should be called after database sync has been received.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            self.logger.info("Initializing config-dependent services")
            print("Initializing config-dependent services...", flush=True)
            
            # ML libraries are now pre-initialized early in main.py startup
            self.logger.info("ML libraries should already be initializing in background")
            
            # Initialize shortcut service (depends on config)
            shortcut_service = ShortcutService(self.service_container)
            self.service_container.register("shortcut", shortcut_service)
            await shortcut_service.initialize()
            
            # Initialize audio service
            audio_service = AudioService(self.service_container)
            self.service_container.register("audio", audio_service)
            await audio_service.initialize()
            
            # Initialize model service
            model_service = ModelService(self.service_container)
            self.service_container.register("models", model_service)
            await model_service.initialize()
            
            # Initialize transcription service
            transcription_service = TranscriptionService(self.service_container)
            self.service_container.register("transcription", transcription_service)
            await transcription_service.initialize()

            # We're now using IPC bridge for all events, so no need to register event broadcast handlers
            
            self.logger.info("Config-dependent services initialized successfully")
            print("Config-dependent services initialized successfully", flush=True)
            return True
        except Exception as e:
            self.logger.error(f"Error initializing config-dependent services: {e}", exc_info=True)
            print(f"Error initializing config-dependent services: {e}", file=sys.stderr, flush=True)
            return False
    
    async def start(self, port_file: Optional[str] = None) -> bool:
        """Start the application.
        
        Args:
            port_file: File to write the WebSocket port to
            
        Returns:
            True if startup was successful, False otherwise
        """
        if self.running:
            self.logger.warning("Application already running")
            return True
            
        try:
            self.port_file = port_file
            
            # Start WebSocket server
            ws_server = self.service_container.resolve("websocket")
            if not ws_server:
                self.logger.error("WebSocket server not available")
                return False
                
            port = await ws_server.start()
            if port is None:
                self.logger.error("Failed to start WebSocket server")
                return False
                
            # Write port to file if requested
            if port_file:
                try:
                    with open(port_file, 'w') as f:
                        f.write(str(port))
                    self.logger.info(f"WebSocket port {port} written to {port_file}")
                except Exception as e:
                    self.logger.error(f"Error writing port to file: {e}")
            
            self.running = True
            self.logger.info(f"Application started on port {port}")
            print(f"Application started successfully on port {port}", flush=True)
            
            # Setup signal handlers
            self._setup_signal_handlers()
            
            return True
        except Exception as e:
            self.logger.error(f"Error starting application: {e}", exc_info=True)
            return False
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        try:
            # Setup signal handlers for graceful shutdown
            for sig in (signal.SIGINT, signal.SIGTERM):
                asyncio.get_event_loop().add_signal_handler(
                    sig, lambda: asyncio.create_task(self.stop())
                )
            self.logger.debug("Signal handlers registered")
        except NotImplementedError:
            # Windows does not support signals properly
            self.logger.debug("Signal handlers not supported on this platform")
    
    async def run(self, port_file: Optional[str] = None) -> None:
        """Run the application and wait for termination.
        
        Args:
            port_file: File to write the WebSocket port to
        """
        if not await self.initialize():
            self.logger.error("Application initialization failed")
            return
            
        if not await self.start(port_file):
            self.logger.error("Application startup failed")
            return
            
        # Get the WebSocket server and wait for it to run
        ws_server = self.service_container.resolve("websocket")
        if not ws_server or not ws_server.server:
            self.logger.error("WebSocket server not available for running")
            return
            
        # Create a future that will be completed when we need to stop
        stop_future = asyncio.Future()
        self.main_task = stop_future
        
        # Wait for stop signal or server to close
        try:
            # Create a task for the server wait_closed coroutine
            server_close_task = asyncio.create_task(ws_server.server.wait_closed())
            
            # Wait for either the stop signal or the server to close
            done, pending = await asyncio.wait(
                [stop_future, server_close_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Cancel any pending tasks
            for task in pending:
                task.cancel()
                
        except asyncio.CancelledError:
            self.logger.info("Application run task cancelled")
        except Exception as e:
            self.logger.error(f"Error in application run: {e}", exc_info=True)
        finally:
            # Stop if we haven't already
            if self.running:
                await self.stop()
    
    async def stop(self) -> None:
        """Stop the application."""
        if not self.running:
            return
            
        self.logger.info("Stopping application")
        
        try:
            # Stop all services in reverse order
            services = self.service_container.get_services()
            for service_type in reversed(services):
                service = self.service_container.resolve(service_type)
                if hasattr(service, 'cleanup') and callable(service.cleanup):
                    try:
                        self.logger.debug(f"Stopping service: {service_type}")
                        await service.cleanup()
                    except Exception as e:
                        self.logger.error(f"Error stopping service {service_type}: {e}")
            
            # Clean up port file
            if self.port_file and os.path.exists(self.port_file):
                try:
                    os.remove(self.port_file)
                    self.logger.debug(f"Removed port file: {self.port_file}")
                except Exception as e:
                    self.logger.error(f"Error removing port file: {e}")
            
            # Complete the main task if it exists
            if self.main_task and not self.main_task.done():
                self.main_task.set_result(None)
                
            self.running = False
            self.logger.info("Application stopped")
        except Exception as e:
            self.logger.error(f"Error during application shutdown: {e}", exc_info=True)
    
    def get_status(self) -> Dict[str, Any]:
        """Get the application status.
        
        Returns:
            Dictionary with application status information
        """
        services = {}
        if self.service_container:
            for service_type in self.service_container.get_services():
                service = self.service_container.resolve(service_type)
                if service and hasattr(service, 'get_status'):
                    services[service_type] = service.get_status()
        
        return {
            "running": self.running,
            "services": services
        }

    def get_service_container(self) -> ServiceContainer:
        """Get the service container instance.
        
        Returns:
            The service container
        """
        return self.service_container

    async def trigger_config_dependent_initialization(self) -> bool:
        """Trigger initialization of config-dependent services.
        
        This is called when database sync is received and configuration is available.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        if self.config_dependent_services_initialized:
            self.logger.debug("Config-dependent services already initialized")
            return True
            
        success = await self.initialize_config_dependent_services()
        if success:
            self.config_dependent_services_initialized = True
            
        return success


def create_application(config_file: Optional[str] = None) -> Application:
    """Create and initialize an application instance.
    
    Args:
        config_file: Path to the configuration file
        
    Returns:
        Initialized Application instance
    """
    app = Application(config_file)
    app.setup_logging()
    return app 