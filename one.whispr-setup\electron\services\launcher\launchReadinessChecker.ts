import * as fs from 'fs-extra';
import { MainAppDownloader } from '../download/mainAppDownloader';
import { BackendDownloader } from '../download/backendDownloader';
import { MicrosoftStoreHandler } from '../microsoft/storeHandler';
import { LaunchPathResolver } from './launchPathResolver';
import { IS_MICROSOFT } from '../../constants';

export interface ReadinessResult {
  mainAppReady: boolean;
  backendReady: boolean;
  allReady: boolean;
  mainAppNeeded: boolean;
  backendNeeded: boolean;
  reason: string;
}

/**
 * Service for checking if everything is ready to launch
 */
export class LaunchReadinessChecker {
  private mainAppDownloader: MainAppDownloader;
  private backendDownloader: BackendDownloader;
  private microsoftStoreHandler: MicrosoftStoreHandler;
  private pathResolver: LaunchPathResolver;

  constructor(
    mainAppDownloader: MainAppDownloader,
    backendDownloader: BackendDownloader,
    microsoftStoreHandler: MicrosoftStoreHandler,
    pathResolver: LaunchPathResolver
  ) {
    this.mainAppDownloader = mainAppDownloader;
    this.backendDownloader = backendDownloader;
    this.microsoftStoreHandler = microsoftStoreHandler;
    this.pathResolver = pathResolver;
  }

  /**
   * Check if everything is ready to launch (main app + backend)
   */
  public async checkLaunchReady(): Promise<ReadinessResult> {
    try {
      const isDev = this.pathResolver.isDevelopmentMode();
      const forceDownload = this.pathResolver.isForceDownloadEnabled();

      console.log('[READINESS_CHECKER] Environment check - isDev:', isDev, 'forceDownload:', forceDownload);
      console.log('[READINESS_CHECKER] NODE_ENV:', process.env.NODE_ENV);

      // Check main app readiness
      const mainAppResult = await this.checkMainAppReadiness(isDev, forceDownload);
      
      // Check backend readiness
      const backendResult = await this.checkBackendReadiness();

      // Determine overall readiness
      const allReady = mainAppResult.ready && backendResult.ready && 
                      !mainAppResult.needed && !backendResult.needed;

      let reason = 'Ready to launch';
      if (!mainAppResult.ready || mainAppResult.needed) {
        reason = mainAppResult.reason || 'Main app needs download/update';
      } else if (!backendResult.ready || backendResult.needed) {
        reason = backendResult.reason;
      }

      const result: ReadinessResult = {
        mainAppReady: mainAppResult.ready,
        backendReady: backendResult.ready,
        allReady,
        mainAppNeeded: mainAppResult.needed,
        backendNeeded: backendResult.needed,
        reason
      };

      console.log('[READINESS_CHECKER] Final readiness result:', result);
      return result;
    } catch (error) {
      console.error('[READINESS_CHECKER] Error checking launch readiness:', error);
      return {
        mainAppReady: false,
        backendReady: false,
        allReady: false,
        mainAppNeeded: true,
        backendNeeded: true,
        reason: 'Error checking readiness'
      };
    }
  }

  /**
   * Check main app readiness based on build type and environment
   */
  private async checkMainAppReadiness(isDev: boolean, forceDownload: boolean): Promise<{
    ready: boolean;
    needed: boolean;
    reason: string;
  }> {
    const mainAppPath = this.pathResolver.getMainAppPath();

    if (IS_MICROSOFT) {
      return this.checkMicrosoftStoreReadiness(mainAppPath);
    } else if (isDev && !forceDownload) {
      return this.checkDevelopmentReadiness(mainAppPath);
    } else {
      return this.checkProductionReadiness();
    }
  }

  /**
   * Check Microsoft Store build readiness
   */
  private async checkMicrosoftStoreReadiness(mainAppPath: string): Promise<{
    ready: boolean;
    needed: boolean;
    reason: string;
  }> {
    console.log('[READINESS_CHECKER] Microsoft Store build detected');
    console.log('[READINESS_CHECKER] Checking first launch status...');
    
    const isFirstLaunch = await this.microsoftStoreHandler.isFirstLaunch();
    console.log('[READINESS_CHECKER] Is first launch:', isFirstLaunch);

    if (isFirstLaunch) {
      return {
        ready: false,
        needed: true,
        reason: 'Microsoft Store first launch - need to extract main app to AppData'
      };
    } else {
      console.log('[READINESS_CHECKER] Not first launch - checking if MainApp exists at:', mainAppPath);
      const ready = fs.existsSync(mainAppPath);
      console.log('[READINESS_CHECKER] MainApp exists check result:', ready);
      
      return {
        ready,
        needed: false,
        reason: ready ? 'Microsoft Store - main app ready in AppData' : 'Microsoft Store - main app missing from AppData'
      };
    }
  }

  /**
   * Check development mode readiness
   */
  private checkDevelopmentReadiness(mainAppPath: string): {
    ready: boolean;
    needed: boolean;
    reason: string;
  } {
    console.log('[READINESS_CHECKER] Development mode detected - skipping main app download checks');
    const ready = fs.existsSync(mainAppPath);
    
    return {
      ready,
      needed: false, // Don't download in dev mode
      reason: ready ? 'Development mode - using local files' : 'Main app not built yet'
    };
  }

  /**
   * Check production mode readiness
   */
  private async checkProductionReadiness(): Promise<{
    ready: boolean;
    needed: boolean;
    reason: string;
  }> {
    const mainAppCheck = await this.mainAppDownloader.checkDownloadNeeded();
    
    return {
      ready: !mainAppCheck.needed,
      needed: mainAppCheck.needed,
      reason: mainAppCheck.reason
    };
  }

  /**
   * Check backend readiness
   */
  private async checkBackendReadiness(): Promise<{
    ready: boolean;
    needed: boolean;
    reason: string;
  }> {
    const backendReady = await this.backendDownloader.isBackendReady();
    console.log('[READINESS_CHECKER] Backend ready:', backendReady);

    const backendCheck = await this.backendDownloader.checkBackendUpdate();
    console.log('[READINESS_CHECKER] Backend check result:', backendCheck);

    return {
      ready: backendReady,
      needed: backendCheck.runtimeNeeded || backendCheck.scriptsNeeded,
      reason: backendCheck.reason
    };
  }
}
