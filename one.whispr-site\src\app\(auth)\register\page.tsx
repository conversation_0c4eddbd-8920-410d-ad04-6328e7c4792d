'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { AuthCard } from '../_components/ui/AuthCard';
import { AuthFlowController, AuthFlowFooterContent } from '../_lib/auth-flow';
import { registerSteps } from '../_lib/auth-steps';
import { SearchParamsWrapper } from '@/components/suspense/SearchParamsWrapper';

// Content component that uses useSearchParams
function RegisterContent() {
  const searchParams = useSearchParams();
  const step = searchParams.get('step') || 'info';
  const email = searchParams.get('email') || '';
  const firstName = searchParams.get('firstName') || '';
  const lastName = searchParams.get('lastName') || '';
  const callbackUrl = searchParams.get('callbackUrl');
  const action = searchParams.get('action'); // Extract action parameter
  
  // Extract all search params
  const params: Record<string, string | null> = {};
  searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  // Generate footer content based on step
  const footerContent: AuthFlowFooterContent = {
    info: (
      <p className="text-base text-muted-foreground">
        Already have an account?{' '}
        <Link 
          href={callbackUrl ? `/login?callbackUrl=${encodeURIComponent(callbackUrl)}` : '/login'} 
          className="font-medium text-primary hover:underline underline-offset-4"
        >
          Sign In
        </Link>
      </p>
    ),
    password: (
      <p className="text-base text-muted-foreground">
        Already have an account?{' '}
        <Link 
          href={callbackUrl ? `/login?callbackUrl=${encodeURIComponent(callbackUrl)}` : '/login'} 
          className="font-medium text-primary hover:underline underline-offset-4"
        >
          Sign In
        </Link>
      </p>
    ),
    otp: (
      <p className="text-base text-muted-foreground">
        Can't find your email? Check your spam folder.
      </p>
    )
  };

  return (
    <AuthFlowController
      steps={registerSteps}
      currentStep={step}
      params={{ 
        email, 
        firstName, 
        lastName,
        callbackUrl,
        action,
      }}
      footerContent={footerContent}
      renderCard={({ title, children, footerContent }) => (
        <AuthCard title={title} footerContent={footerContent}>
          {children}
        </AuthCard>
      )}
    />
  );
}

// Main page component with the SearchParamsWrapper
export default function RegisterPage() {
  return (
    <SearchParamsWrapper>
      <RegisterContent />
    </SearchParamsWrapper>
  );
} 