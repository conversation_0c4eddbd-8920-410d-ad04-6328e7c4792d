"""
Performance monitoring and optimization command handlers.

This module provides WebSocket command handlers for performance profiling,
optimization, and testing operations in the transcription system.
"""

import logging
import json
import time
from typing import Dict, Any, Optional

from ..utils.performance_profiler import get_profiler
from ..utils.performance_optimizer import get_optimizer, OptimizationConfig

logger = logging.getLogger(__name__)


def handle_performance_start_monitoring(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Start performance monitoring.
    
    Command: performance.start_monitoring
    Data: {
        "enable_memory_tracing": bool (optional),
        "sample_interval": float (optional)
    }
    """
    try:
        enable_memory_tracing = data.get("enable_memory_tracing", True)
        sample_interval = data.get("sample_interval", 0.1)
        
        # Get profiler with custom configuration if provided
        profiler = get_profiler()
        profiler.enable_memory_tracing = enable_memory_tracing
        profiler.sample_interval = sample_interval
        
        # Start monitoring
        profiler.start_system_monitoring()
        
        return {
            "success": True,
            "message": "Performance monitoring started",
            "configuration": {
                "memory_tracing": enable_memory_tracing,
                "sample_interval": sample_interval
            }
        }
        
    except Exception as e:
        logger.error(f"Error starting performance monitoring: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_performance_stop_monitoring(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Stop performance monitoring.
    
    Command: performance.stop_monitoring
    Data: {}
    """
    try:
        profiler = get_profiler()
        profiler.stop_system_monitoring()
        
        return {
            "success": True,
            "message": "Performance monitoring stopped"
        }
        
    except Exception as e:
        logger.error(f"Error stopping performance monitoring: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_performance_get_report(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get comprehensive performance report.
    
    Command: performance.get_report
    Data: {}
    """
    try:
        profiler = get_profiler()
        report = profiler.generate_performance_report()
        
        return {
            "success": True,
            "report": report
        }
        
    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_performance_get_status(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get current performance status.
    
    Command: performance.get_status
    Data: {}
    """
    try:
        profiler = get_profiler()
        status = profiler.get_system_status()
        
        # Get operation stats
        operation_stats = profiler.get_all_operation_stats()
        
        return {
            "success": True,
            "status": status,
            "operation_stats": operation_stats
        }
        
    except Exception as e:
        logger.error(f"Error getting performance status: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_performance_reset_metrics(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Reset performance metrics.
    
    Command: performance.reset_metrics
    Data: {}
    """
    try:
        profiler = get_profiler()
        profiler.reset_metrics()
        
        return {
            "success": True,
            "message": "Performance metrics reset"
        }
        
    except Exception as e:
        logger.error(f"Error resetting performance metrics: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_optimization_start(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Start performance optimization.
    
    Command: optimization.start
    Data: {
        "config": {
            "enable_memory_optimization": bool (optional),
            "enable_adaptive_processing": bool (optional),
            "enable_caching": bool (optional),
            "enable_parallel_processing": bool (optional),
            "cache_size_limit": int (optional),
            "memory_threshold_mb": int (optional),
            "cpu_threshold_percent": float (optional)
        } (optional)
    }
    """
    try:
        config_data = data.get("config", {})
        
        # Create optimization configuration
        config = OptimizationConfig(
            enable_memory_optimization=config_data.get("enable_memory_optimization", True),
            enable_adaptive_processing=config_data.get("enable_adaptive_processing", True),
            enable_caching=config_data.get("enable_caching", True),
            enable_parallel_processing=config_data.get("enable_parallel_processing", True),
            cache_size_limit=config_data.get("cache_size_limit", 1000),
            memory_threshold_mb=config_data.get("memory_threshold_mb", 512),
            cpu_threshold_percent=config_data.get("cpu_threshold_percent", 80.0)
        )
        
        # Get optimizer with configuration
        optimizer = get_optimizer(config)
        optimizer.start_optimization()
        
        return {
            "success": True,
            "message": "Performance optimization started",
            "configuration": {
                "memory_optimization": config.enable_memory_optimization,
                "adaptive_processing": config.enable_adaptive_processing,
                "caching": config.enable_caching,
                "parallel_processing": config.enable_parallel_processing,
                "cache_size_limit": config.cache_size_limit,
                "memory_threshold_mb": config.memory_threshold_mb,
                "cpu_threshold_percent": config.cpu_threshold_percent
            }
        }
        
    except Exception as e:
        logger.error(f"Error starting optimization: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_optimization_stop(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Stop performance optimization.
    
    Command: optimization.stop
    Data: {}
    """
    try:
        optimizer = get_optimizer()
        optimizer.stop_optimization()
        
        return {
            "success": True,
            "message": "Performance optimization stopped"
        }
        
    except Exception as e:
        logger.error(f"Error stopping optimization: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_optimization_get_recommendations(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get performance optimization recommendations.
    
    Command: optimization.get_recommendations
    Data: {}
    """
    try:
        optimizer = get_optimizer()
        recommendations = optimizer.get_optimization_recommendations()
        
        return {
            "success": True,
            "recommendations": recommendations
        }
        
    except Exception as e:
        logger.error(f"Error getting optimization recommendations: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_optimization_apply_auto(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Apply automatic optimizations.
    
    Command: optimization.apply_auto
    Data: {}
    """
    try:
        optimizer = get_optimizer()
        result = optimizer.apply_auto_optimizations()
        
        return {
            "success": True,
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Error applying auto optimizations: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_optimization_optimize_memory(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Optimize memory usage.
    
    Command: optimization.optimize_memory
    Data: {
        "category": str (optional)
    }
    """
    try:
        category = data.get("category", "general")
        
        optimizer = get_optimizer()
        result = optimizer.optimize_memory_usage(category)
        
        return {
            "success": True,
            "result": result
        }
        
    except Exception as e:
        logger.error(f"Error optimizing memory: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def handle_optimization_get_summary(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Get performance optimization summary.
    
    Command: optimization.get_summary
    Data: {}
    """
    try:
        optimizer = get_optimizer()
        summary = optimizer.get_performance_summary()
        
        return {
            "success": True,
            "summary": summary
        }
        
    except Exception as e:
        logger.error(f"Error getting optimization summary: {e}")
        return {
            "success": False,
            "error": str(e)
        }


# Handler registry
PERFORMANCE_HANDLERS = {
    # Performance monitoring
    "performance.start_monitoring": handle_performance_start_monitoring,
    "performance.stop_monitoring": handle_performance_stop_monitoring,
    "performance.get_report": handle_performance_get_report,
    "performance.get_status": handle_performance_get_status,
    "performance.reset_metrics": handle_performance_reset_metrics,
    
    # Optimization
    "optimization.start": handle_optimization_start,
    "optimization.stop": handle_optimization_stop,
    "optimization.get_recommendations": handle_optimization_get_recommendations,
    "optimization.apply_auto": handle_optimization_apply_auto,
    "optimization.optimize_memory": handle_optimization_optimize_memory,
    "optimization.get_summary": handle_optimization_get_summary,
} 