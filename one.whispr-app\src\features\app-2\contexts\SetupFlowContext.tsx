/**
 * Setup Flow Context Foundation
 * Central coordinator for the setup flow with step navigation and lifecycle management
 * Combines the clean architecture from app-1 with improved patterns
 */

import React, { 
  createContext, 
  useContext, 
  useReducer, 
  useCallback, 
  useEffect,
  useRef,
  useMemo
} from 'react';

import { 
  SETUP_STEPS, 
  STEP_CONFIG 
} from '../types/constants';
import type { 
  SetupFlowState, 
  SetupFlowActions, 
  SetupFlowContextValue,
  StepConfig 
} from '../types/setup-flow';
import type { SetupStep } from '../types/core';

// ============================================================================
// ACTIONS
// ============================================================================

type SetupFlowAction =
  | { type: 'SET_CURRENT_STEP'; payload: SetupStep }
  | { type: 'COMPLETE_STEP'; payload: SetupStep }
  | { type: 'SET_CAN_PROCEED'; payload: boolean }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' };

// ============================================================================
// REDUCER
// ============================================================================

function setupFlowReducer(state: SetupFlowState, action: SetupFlowAction): SetupFlowState {
  switch (action.type) {
    case 'SET_CURRENT_STEP':
      return {
        ...state,
        currentStep: action.payload,
        steps: state.steps.map(step => ({
          ...step,
          isActive: step.id === action.payload,
        })),
        // Reset proceed state when changing steps
        canProceed: false,
        error: null,
      };

    case 'COMPLETE_STEP':
      const updatedCompletedSteps = new Set([...state.completedSteps, action.payload]);
      return {
        ...state,
        completedSteps: updatedCompletedSteps,
        steps: state.steps.map(step => ({
          ...step,
          isCompleted: step.id === action.payload ? true : step.isCompleted,
        })),
      };

    case 'SET_CAN_PROCEED':
      return { ...state, canProceed: action.payload };

    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };

    case 'CLEAR_ERROR':
      return { ...state, error: null };

    default:
      return state;
  }
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const createInitialState = (): SetupFlowState => ({
  currentStep: 'auth',
  completedSteps: new Set(),
  steps: SETUP_STEPS.map((stepId, index) => ({
    ...STEP_CONFIG[stepId],
    isActive: index === 0,
    isCompleted: false,
  })),
  canProceed: false,
  loading: false,
  error: null,
});

// ============================================================================
// CONTEXT
// ============================================================================

const SetupFlowContext = createContext<SetupFlowContextValue | null>(null);

// ============================================================================
// STEP LIFECYCLE HANDLERS
// ============================================================================

interface StepLifecycleHandlers {
  onEnter?: (step: SetupStep) => Promise<void>;
  onExit?: (step: SetupStep) => Promise<void>;
  onCleanup?: (step: SetupStep) => Promise<void>;
}

// Registry for step lifecycle handlers
const stepHandlers = new Map<SetupStep, StepLifecycleHandlers>();

// Allow other contexts to register lifecycle handlers
export function registerStepHandlers(step: SetupStep, handlers: StepLifecycleHandlers) {
  stepHandlers.set(step, handlers);
}

export function unregisterStepHandlers(step: SetupStep) {
  stepHandlers.delete(step);
}

// ============================================================================
// PROVIDER
// ============================================================================

interface SetupFlowProviderProps {
  children: React.ReactNode;
  onComplete?: () => void;
}

export function SetupFlowProvider({ children, onComplete }: SetupFlowProviderProps) {
  const [state, dispatch] = useReducer(setupFlowReducer, createInitialState());
  const currentStepRef = useRef<SetupStep>(state.currentStep);

  // Update ref when step changes
  useEffect(() => {
    currentStepRef.current = state.currentStep;
  }, [state.currentStep]);

  // ============================================================================
  // STEP LIFECYCLE MANAGEMENT
  // ============================================================================

  const handleStepEnter = useCallback(async (step: SetupStep) => {
    console.log(`🔄 Entering step: ${step}`);
    
    // Get step config to check if lifecycle is needed
    const stepConfig = STEP_CONFIG[step];
    if (!stepConfig.hasLifecycle) {
      console.log(`✓ Step ${step} has no lifecycle handlers`);
      return;
    }

    // Get registered handlers for this step
    const handlers = stepHandlers.get(step);
    if (!handlers?.onEnter) {
      console.log(`⚠️ Step ${step} requires lifecycle but no enter handler registered`);
      return;
    }

    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      await handlers.onEnter(step);
      console.log(`✓ Successfully entered step: ${step}`);
    } catch (error) {
      console.error(`❌ Failed to enter step ${step}:`, error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: `Failed to initialize ${STEP_CONFIG[step].title}. Please try again.` 
      });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const handleStepExit = useCallback(async (step: SetupStep) => {
    console.log(`🔄 Exiting step: ${step}`);
    
    // Get step config to check if lifecycle is needed
    const stepConfig = STEP_CONFIG[step];
    if (!stepConfig.hasLifecycle) {
      console.log(`✓ Step ${step} has no lifecycle handlers`);
      return;
    }

    // Get registered handlers for this step
    const handlers = stepHandlers.get(step);
    if (!handlers?.onExit) {
      console.log(`⚠️ Step ${step} requires lifecycle but no exit handler registered`);
      return;
    }

    try {
      await handlers.onExit(step);
      console.log(`✓ Successfully exited step: ${step}`);
    } catch (error) {
      console.error(`❌ Failed to exit step ${step}:`, error);
      // Don't block navigation on exit errors, just log them
    }
  }, []);

  const handleStepCleanup = useCallback(async (step: SetupStep) => {
    console.log(`🧹 Cleaning up step: ${step}`);
    
    const handlers = stepHandlers.get(step);
    if (!handlers?.onCleanup) {
      return;
    }

    try {
      await handlers.onCleanup(step);
      console.log(`✓ Successfully cleaned up step: ${step}`);
    } catch (error) {
      console.error(`❌ Failed to cleanup step ${step}:`, error);
      // Don't block on cleanup errors
    }
  }, []);



  // ============================================================================
  // NAVIGATION ACTIONS
  // ============================================================================

  const goToStep = useCallback(async (targetStep: SetupStep) => {
    const currentStep = currentStepRef.current;
    
    if (currentStep === targetStep) {
      console.log(`Already on step: ${targetStep}`);
      return;
    }
    
    console.log(`🔄 Navigating from ${currentStep} to ${targetStep}`);
    
    try {
      // Exit current step
      await handleStepExit(currentStep);
      
      // Update state
      dispatch({ type: 'SET_CURRENT_STEP', payload: targetStep });
      
      // Enter new step
      await handleStepEnter(targetStep);
      
    } catch (error) {
      console.error(`❌ Failed to navigate to step ${targetStep}:`, error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: `Failed to navigate to ${STEP_CONFIG[targetStep].title}. Please try again.` 
      });
    }
  }, [handleStepEnter, handleStepExit]);

  const goNext = useCallback(async () => {
    const currentStep = currentStepRef.current;
    const currentIndex = SETUP_STEPS.indexOf(currentStep);
    
    if (currentIndex >= 0 && currentIndex < SETUP_STEPS.length - 1) {
      const nextStep = SETUP_STEPS[currentIndex + 1];
      // Mark current step as completed before moving
      dispatch({ type: 'COMPLETE_STEP', payload: currentStep });
      await goToStep(nextStep);
    } else if (currentIndex === SETUP_STEPS.length - 1) {
      // Complete the final step and the entire flow
      dispatch({ type: 'COMPLETE_STEP', payload: currentStep });
      console.log('🎉 Setup flow completed successfully!');
      onComplete?.();
    }
  }, [goToStep, onComplete]);

  const goPrevious = useCallback(async () => {
    const currentStep = currentStepRef.current;
    const currentIndex = SETUP_STEPS.indexOf(currentStep);
    
    if (currentIndex > 0) {
      const previousStep = SETUP_STEPS[currentIndex - 1];
      await goToStep(previousStep);
    }
  }, [goToStep]);

  const markStepCompleted = useCallback((step: SetupStep) => {
    console.log(`✅ Marking step completed: ${step}`);
    dispatch({ type: 'COMPLETE_STEP', payload: step });
  }, []);

  // ============================================================================
  // STATE MANAGEMENT ACTIONS
  // ============================================================================

  const setCanProceed = useCallback((canProceed: boolean) => {
    dispatch({ type: 'SET_CAN_PROCEED', payload: canProceed });
  }, []);

  const setError = useCallback((error: string | null) => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // ============================================================================
  // UTILITY FUNCTIONS
  // ============================================================================

  const currentStepIndex = SETUP_STEPS.indexOf(state.currentStep);
  const totalSteps = SETUP_STEPS.length;
  const progress = ((currentStepIndex + 1) / totalSteps) * 100;
  const canGoNext = state.canProceed && currentStepIndex < totalSteps - 1;
  const canGoPrevious = currentStepIndex > 0;

  // ============================================================================
  // LIFECYCLE EFFECTS
  // ============================================================================

  // Cleanup all steps on unmount only
  useEffect(() => {
    console.log('🚀 Initializing SetupFlowProvider');
    
    // Cleanup all steps on unmount
    return () => {
      console.log('🧹 Cleaning up SetupFlowProvider');
      const currentStep = currentStepRef.current;
      
      // Cleanup current step and all registered steps
      Promise.all([
        handleStepCleanup(currentStep),
        ...SETUP_STEPS.map(step => handleStepCleanup(step))
      ]).catch(error => {
        console.error('Failed to cleanup setup flow:', error);
      });
    };
  }, []); // Only run on mount/unmount

  // ============================================================================
  // ACTIONS OBJECT
  // ============================================================================

  const actions: SetupFlowActions = useMemo(() => ({
    goToStep,
    goNext,
    goPrevious,
    markStepCompleted,
    setCanProceed,
    setError,
    clearError,
  }), [goToStep, goNext, goPrevious, markStepCompleted, setCanProceed, setError, clearError]);

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const contextValue: SetupFlowContextValue = useMemo(() => ({
    state,
    actions,
    currentStepIndex,
    totalSteps,
    progress,
    canGoNext,
    canGoPrevious,
  }), [state, actions, currentStepIndex, totalSteps, progress, canGoNext, canGoPrevious]);

  return (
    <SetupFlowContext.Provider value={contextValue}>
      {children}
    </SetupFlowContext.Provider>
  );
}

// ============================================================================
// HOOK
// ============================================================================

export function useSetupFlow(): SetupFlowContextValue {
  const context = useContext(SetupFlowContext);
  if (!context) {
    throw new Error('useSetupFlow must be used within a SetupFlowProvider');
  }
  return context;
}

// ============================================================================
// ADDITIONAL UTILITY HOOKS
// ============================================================================

/**
 * Hook for child contexts to register lifecycle handlers
 */
export function useStepLifecycle(
  step: SetupStep, 
  handlers: StepLifecycleHandlers,
  deps: React.DependencyList = []
) {
  useEffect(() => {
    registerStepHandlers(step, handlers);
    
    return () => {
      unregisterStepHandlers(step);
    };
  }, deps); // eslint-disable-line react-hooks/exhaustive-deps
}

/**
 * Hook to get current step configuration
 */
export function useCurrentStepConfig(): StepConfig {
  const { state } = useSetupFlow();
  return state.steps.find(step => step.id === state.currentStep) || state.steps[0];
} 