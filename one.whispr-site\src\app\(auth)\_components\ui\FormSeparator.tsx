'use client';

import { Separator } from '@/components/ui/separator';

interface FormSeparatorProps {
  text?: string;
}

/**
 * A component for separating form sections with a line and optional text
 */
export function FormSeparator({ text = 'OR' }: FormSeparatorProps) {
  return (
    <div className="relative flex items-center justify-center">
      <Separator className="absolute" />
      <span className="relative bg-card px-2 text-sm text-muted-foreground">
        {text}
      </span>
    </div>
  );
} 