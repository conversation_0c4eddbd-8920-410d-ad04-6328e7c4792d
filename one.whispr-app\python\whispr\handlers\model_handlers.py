"""
Model handlers for One.Whispr voice models.

This module provides IPC handlers for model management operations.
"""

import logging
from typing import Dict, Any, Optional, TYPE_CHECKING

from whispr.core.response_utils import success_response, error_response, ErrorCodes

if TYPE_CHECKING:
    from ..core.handlers import CommandHandlers

logger = logging.getLogger("whispr.handlers.model")


def _get_model_service(context: Dict[str, Any]):
    """Get the model service from the context.
    
    Args:
        context: Execution context
        
    Returns:
        ModelService instance or None if not available
    """
    service_container = context.get('service_container')
    if not service_container:
        return None
    return service_container.resolve("models")


async def handle_get_all_models(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get all available models.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with models list
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        models = model_service.get_all_models()
        return success_response(models, "Retrieved all models successfully")
    except Exception as e:
        logger.error(f"Error getting all models: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get a specific model by ID.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with model data
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return error_response("model_id is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        model = model_service.get_model(model_id)
        if model:
            return success_response({"model": model}, f"Retrieved model {model_id} successfully")
        else:
            return error_response(f"Model not found: {model_id}", ErrorCodes.RESOURCE_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error getting model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_downloaded_models(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get all downloaded models.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with downloaded models list
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        models = model_service.get_downloaded_models()
        return success_response(models, "Retrieved downloaded models successfully")
    except Exception as e:
        logger.error(f"Error getting downloaded models: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_recommended_models(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get recommended models for new users.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with recommended models list
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        models = model_service.get_recommended_models()
        return success_response(models, "Retrieved recommended models successfully")
    except Exception as e:
        logger.error(f"Error getting recommended models: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_delete_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Delete a downloaded model.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with deletion result
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return error_response("model_id is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = model_service.delete_model(model_id)
        if success:
            return success_response(None, f"Model {model_id} deleted successfully")
        else:
            return error_response(f"Failed to delete model: {model_id}", ErrorCodes.SERVICE_ERROR)
        
    except Exception as e:
        logger.error(f"Error deleting model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_set_active_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Set the active model.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with result
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return error_response("model_id is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = model_service.set_active_model(model_id)
        if success:
            return success_response(None, f"Active model set to {model_id}")
        else:
            return error_response(f"Failed to set active model: {model_id}", ErrorCodes.SERVICE_ERROR)
        
    except Exception as e:
        logger.error(f"Error setting active model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_active_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get the currently active model.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with active model info
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        active_model_id = model_service.get_active_model()
        active_model_info = model_service.get_active_model_info()
        
        return success_response({"active_model_id": active_model_id, "active_model": active_model_info}, "Retrieved active model successfully")
        
    except Exception as e:
        logger.error(f"Error getting active model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_storage_info(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get storage information.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with storage info
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        storage_info = model_service.get_storage_info()
        return success_response({"storage": storage_info}, "Retrieved storage info successfully")
    except Exception as e:
        logger.error(f"Error getting storage info: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_cleanup_storage(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Clean up empty directories in storage.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with cleanup result
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        model_service.cleanup_storage()
        return success_response(None, "Storage cleanup completed")
    except Exception as e:
        logger.error(f"Error during storage cleanup: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_download_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Download a model.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with download result
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return error_response("model_id is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = model_service.download_model(model_id)
        if success:
            return success_response(None, f"Download started for model: {model_id}")
        else:
            return error_response(f"Failed to download model: {model_id}", ErrorCodes.SERVICE_ERROR)
        
    except Exception as e:
        logger.error(f"Error downloading model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_cancel_download(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Cancel a model download.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with cancellation result
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return error_response("model_id is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = model_service.cancel_download(model_id)
        if success:
            return success_response(None, f"Download cancelled for model: {model_id}")
        else:
            return error_response(f"Failed to cancel download: {model_id}", ErrorCodes.SERVICE_ERROR)
        
    except Exception as e:
        logger.error(f"Error cancelling download: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_download_progress(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get download progress for a model.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with progress information
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return error_response("model_id is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        progress = model_service.get_download_progress(model_id)
        if progress:
            return success_response({"progress": progress}, "Retrieved download progress successfully")
        else:
            return error_response(f"Model not found: {model_id}", ErrorCodes.RESOURCE_NOT_FOUND)
            
    except Exception as e:
        logger.error(f"Error getting download progress: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_active_downloads(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get list of active downloads.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with active downloads list
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        active_downloads = model_service.get_active_downloads()
        return success_response({"active_downloads": active_downloads}, "Retrieved active downloads successfully")
    except Exception as e:
        logger.error(f"Error getting active downloads: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_validate_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle model validation request.
    
    Args:
        params: The message parameters containing model_id
        context: The execution context
        
    Returns:
        Validation report data
    """
    try:
        model_id = params.get('model_id')
        if not model_id:
            return error_response("model_id parameter is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        validation_report = await model_service.validate_model(model_id)
        return success_response({
            "model_id": validation_report.model_id,
            "status": validation_report.status.value,
            "is_valid": validation_report.is_valid,
            "has_errors": validation_report.has_errors,
            "errors": [
                {
                    "file_path": error.file_path,
                    "error_type": error.error_type.value,
                    "message": error.message,
                    "suggestion": error.suggestion
                }
                for error in validation_report.errors
            ],
            "validated_files": validation_report.validated_files,
            "missing_files": validation_report.missing_files,
            "format_info": validation_report.format_info,
            "total_size": validation_report.total_size
        }, "Retrieved validation report successfully")
    except Exception as e:
        logger.error(f"Error validating model {params.get('model_id', 'unknown')}: {e}")
        return error_response(f"Validation failed: {str(e)}", ErrorCodes.SERVICE_ERROR)


async def handle_validate_all_models(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle validation of all models request.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        Validation reports for all models
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        validation_reports = await model_service.validate_all_models()
        
        results = {}
        for model_id, report in validation_reports.items():
            results[model_id] = {
                "status": report.status.value,
                "is_valid": report.is_valid,
                "has_errors": report.has_errors,
                "error_count": len(report.errors),
                "validated_files_count": len(report.validated_files),
                "missing_files_count": len(report.missing_files),
                "total_size": report.total_size
            }
        
        return success_response({"validation_results": results}, "Retrieved validation results successfully")
    except Exception as e:
        logger.error(f"Error validating all models: {e}")
        return error_response(f"Validation failed: {str(e)}", ErrorCodes.SERVICE_ERROR)


async def handle_get_model_format_info(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle model format information request.
    
    Args:
        params: The message parameters containing model_id
        context: The execution context
        
    Returns:
        Model format information
    """
    try:
        model_id = params.get('model_id')
        if not model_id:
            return error_response("model_id parameter is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        format_info = model_service.get_model_format_info(model_id)
        return success_response({"model_id": model_id, "format_info": format_info}, "Retrieved format info successfully")
    except Exception as e:
        logger.error(f"Error getting format info for model {params.get('model_id', 'unknown')}: {e}")
        return error_response(f"Failed to get format info: {str(e)}", ErrorCodes.SERVICE_ERROR)


async def handle_load_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Load a model into memory for use.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with loading result
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return error_response("model_id is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = await model_service.load_model(model_id)
        if success:
            return success_response(None, f"Model loaded successfully: {model_id}")
        else:
            return error_response(f"Failed to load model: {model_id}", ErrorCodes.SERVICE_ERROR)
        
    except Exception as e:
        logger.error(f"Error loading model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_unload_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Unload the currently loaded model.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with unloading result
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = await model_service.unload_model()
        if success:
            return success_response(None, "Model unloaded successfully")
        else:
            return error_response("Failed to unload model", ErrorCodes.SERVICE_ERROR)
        
    except Exception as e:
        logger.error(f"Error unloading model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_switch_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Switch to a different model.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with result
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return error_response("model_id is required", ErrorCodes.PARAMETER_ERROR)
        
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = await model_service.switch_model(model_id)
        if success:
            return success_response(None, f"Switched to model: {model_id}")
        else:
            return error_response(f"Failed to switch to model: {model_id}", ErrorCodes.SERVICE_ERROR)
        
    except Exception as e:
        logger.error(f"Error switching model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_loaded_model(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get the currently loaded model.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with loaded model info
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        loaded_model = model_service.get_loaded_model()
        if loaded_model:
            return success_response({"loaded_model": loaded_model}, "Retrieved loaded model successfully")
        else:
            return success_response({"loaded_model": None}, "No model currently loaded")
        
    except Exception as e:
        logger.error(f"Error getting loaded model: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_loading_state(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get the current model loading state.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with loading state
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return error_response("ModelService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        loading_state = model_service.get_loading_state()
        return success_response({"loading_state": loading_state}, "Retrieved loading state successfully")
        
    except Exception as e:
        logger.error(f"Error getting loading state: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_is_model_loaded(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Check if a specific model is currently loaded.
    
    Args:
        params: Request parameters containing model_id
        context: Execution context
        
    Returns:
        Response with loading status
    """
    try:
        model_id = params.get("model_id")
        if not model_id:
            return {
                "success": False,
                "error": "model_id is required"
            }
        
        model_service = _get_model_service(context)
        if not model_service:
            return {
                "success": False,
                "error": "ModelService not available"
            }
        
        is_loaded = model_service.is_model_loaded(model_id)
        return {
            "success": True,
            "is_loaded": is_loaded
        }
        
    except Exception as e:
        logger.error(f"Error checking if model is loaded: {e}")
        return {
            "success": False,
            "error": str(e)
        }


async def handle_get_memory_info(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get current memory usage information.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with memory information
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return {
                "success": False,
                "error": "ModelService not available"
            }
        
        memory_info = model_service.get_memory_info()
        return {
            "success": True,
            "memory_info": memory_info
        }
        
    except Exception as e:
        logger.error(f"Error getting memory info: {e}")
        return {
            "success": False,
            "error": str(e)
        }


async def handle_get_hardware_info(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get hardware configuration information.
    
    Args:
        params: Request parameters (unused)
        context: Execution context
        
    Returns:
        Response with hardware information
    """
    try:
        model_service = _get_model_service(context)
        if not model_service:
            return {
                "success": False,
                "error": "ModelService not available"
            }
        
        hardware_info = model_service.get_hardware_info()
        return {
            "success": True,
            "hardware_info": hardware_info
        }
        
    except Exception as e:
        logger.error(f"Error getting hardware info: {e}")
        return {
            "success": False,
            "error": str(e)
        }


def register_handlers(command_handlers: 'CommandHandlers') -> None:
    """Register model handlers with the command handlers system.
    
    Args:
        command_handlers: The CommandHandlers instance to register with
    """
    logger.debug("Registering model handlers")
    
    # Register all model-related commands
    command_handlers.registry.register_function("models.get_all", handle_get_all_models)
    command_handlers.registry.register_function("models.get", handle_get_model)
    command_handlers.registry.register_function("models.get_downloaded", handle_get_downloaded_models)
    command_handlers.registry.register_function("models.get_recommended", handle_get_recommended_models)
    command_handlers.registry.register_function("models.delete", handle_delete_model)
    command_handlers.registry.register_function("models.set_active", handle_set_active_model)
    command_handlers.registry.register_function("models.get_active", handle_get_active_model)
    command_handlers.registry.register_function("models.storage.info", handle_get_storage_info)
    command_handlers.registry.register_function("models.storage.cleanup", handle_cleanup_storage)
    
    # Register download-related commands
    command_handlers.registry.register_function("models.download", handle_download_model)
    command_handlers.registry.register_function("models.download.cancel", handle_cancel_download)
    command_handlers.registry.register_function("models.download.progress", handle_get_download_progress)
    command_handlers.registry.register_function("models.download.active", handle_get_active_downloads)
    
    # Register validation-related commands
    command_handlers.registry.register_function("models.validate", handle_validate_model)
    command_handlers.registry.register_function("models.validate.all", handle_validate_all_models)
    command_handlers.registry.register_function("models.format_info", handle_get_model_format_info)
    
    # Register loading-related commands
    command_handlers.registry.register_function("models.load", handle_load_model)
    command_handlers.registry.register_function("models.unload", handle_unload_model)
    command_handlers.registry.register_function("models.switch", handle_switch_model)
    command_handlers.registry.register_function("models.get_loaded", handle_get_loaded_model)
    command_handlers.registry.register_function("models.loading_state", handle_get_loading_state)
    command_handlers.registry.register_function("models.is_loaded", handle_is_model_loaded)
    
    # Register system info commands
    command_handlers.registry.register_function("models.memory_info", handle_get_memory_info)
    command_handlers.registry.register_function("models.hardware_info", handle_get_hardware_info)
    
    logger.info("Model handlers registered successfully") 
