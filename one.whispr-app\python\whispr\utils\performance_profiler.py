"""
Performance profiler for transcription system components.

This module provides comprehensive performance monitoring and profiling
capabilities for the transcription system, including CPU usage, memory
consumption, processing latency, and system resource monitoring.
"""

import time
import psutil
import threading
import gc
import functools
import sys
import tracemalloc
from typing import Dict, Any, Optional, List, Callable, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from contextlib import contextmanager
import json
import logging
from pathlib import Path
import asyncio

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Detailed performance metrics for a specific operation."""
    operation_name: str
    start_time: float
    end_time: float
    duration: float
    cpu_percent: float
    memory_mb: float
    memory_peak_mb: float
    memory_allocated_mb: Optional[float] = None
    memory_deallocated_mb: Optional[float] = None
    thread_id: int = 0
    process_id: int = 0
    additional_data: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.additional_data is None:
            self.additional_data = {}


@dataclass
class SystemMetrics:
    """System-wide performance metrics."""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_available_mb: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_received_mb: float
    active_threads: int
    open_files: int
    gpu_utilization: Optional[float] = None
    gpu_memory_mb: Optional[float] = None


class PerformanceProfiler:
    """
    Advanced performance profiler for transcription system.
    
    Features:
    - Real-time CPU and memory monitoring
    - Operation-specific performance tracking
    - Memory leak detection
    - System resource monitoring
    - Performance regression detection
    - Automated optimization recommendations
    """
    
    def __init__(self, 
                 enable_memory_tracing: bool = True,
                 sample_interval: float = 0.1,
                 max_metrics_history: int = 10000):
        self.enable_memory_tracing = enable_memory_tracing
        self.sample_interval = sample_interval
        self.max_metrics_history = max_metrics_history
        
        # Metrics storage
        self.operation_metrics: List[PerformanceMetrics] = []
        self.system_metrics: deque = deque(maxlen=max_metrics_history)
        self.operation_stats: Dict[str, List[float]] = defaultdict(list)
        
        # System monitoring
        self.process = psutil.Process()
        self.system_monitor_active = False
        self.system_monitor_thread: Optional[threading.Thread] = None
        
        # Memory tracking
        self.memory_baseline: Optional[float] = None
        self.memory_snapshots: Dict[str, Any] = {}
        
        # Performance thresholds
        self.performance_thresholds = {
            'cpu_high': 80.0,
            'memory_high': 80.0,
            'latency_high': 1.0,  # seconds
            'memory_leak_mb': 100.0
        }
        
        # Initialize memory tracing if enabled
        if self.enable_memory_tracing:
            tracemalloc.start()
            
    def start_system_monitoring(self):
        """Start continuous system monitoring in background thread."""
        if self.system_monitor_active:
            return
            
        self.system_monitor_active = True
        self.system_monitor_thread = threading.Thread(
            target=self._system_monitor_loop,
            daemon=True
        )
        self.system_monitor_thread.start()
        logger.info("Performance system monitoring started")
        
    def stop_system_monitoring(self):
        """Stop system monitoring."""
        self.system_monitor_active = False
        if self.system_monitor_thread:
            self.system_monitor_thread.join(timeout=5.0)
        logger.info("Performance system monitoring stopped")
        
    def _system_monitor_loop(self):
        """Background system monitoring loop."""
        while self.system_monitor_active:
            try:
                metrics = self._collect_system_metrics()
                self.system_metrics.append(metrics)
                time.sleep(self.sample_interval)
            except Exception as e:
                logger.error(f"Error in system monitoring: {e}")
                time.sleep(1.0)
                
    def _collect_system_metrics(self) -> SystemMetrics:
        """Collect current system performance metrics."""
        # CPU and memory
        cpu_percent = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        
        # Disk I/O
        disk_io = psutil.disk_io_counters()
        disk_read_mb = disk_io.read_bytes / (1024 * 1024) if disk_io else 0
        disk_write_mb = disk_io.write_bytes / (1024 * 1024) if disk_io else 0
        
        # Network I/O
        network_io = psutil.net_io_counters()
        network_sent_mb = network_io.bytes_sent / (1024 * 1024) if network_io else 0
        network_recv_mb = network_io.bytes_recv / (1024 * 1024) if network_io else 0
        
        # Process info
        active_threads = threading.active_count()
        open_files = len(self.process.open_files())
        
        # GPU metrics (if available)
        gpu_utilization, gpu_memory_mb = self._get_gpu_metrics()
        
        return SystemMetrics(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_available_mb=memory.available / (1024 * 1024),
            memory_used_mb=memory.used / (1024 * 1024),
            disk_io_read_mb=disk_read_mb,
            disk_io_write_mb=disk_write_mb,
            network_sent_mb=network_sent_mb,
            network_received_mb=network_recv_mb,
            active_threads=active_threads,
            open_files=open_files,
            gpu_utilization=gpu_utilization,
            gpu_memory_mb=gpu_memory_mb
        )
        
    def _get_gpu_metrics(self) -> Tuple[Optional[float], Optional[float]]:
        """Get GPU utilization and memory usage if available."""
        try:
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            
            # GPU utilization
            utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
            gpu_percent = utilization.gpu
            
            # GPU memory
            memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
            gpu_memory_mb = memory_info.used / (1024 * 1024)
            
            return gpu_percent, gpu_memory_mb
        except:
            return None, None
            
    @contextmanager
    def profile_operation(self, operation_name: str, **additional_data):
        """
        Context manager for profiling a specific operation.
        
        Usage:
            with profiler.profile_operation("audio_processing"):
                # ... operation code ...
        """
        # Pre-operation measurements
        start_time = time.time()
        start_memory = self.process.memory_info().rss / (1024 * 1024)
        start_cpu = self.process.cpu_percent()
        
        # Memory snapshot if tracing enabled
        memory_start_snapshot = None
        if self.enable_memory_tracing:
            memory_start_snapshot = tracemalloc.take_snapshot()
        
        # Force garbage collection for accurate measurements
        gc.collect()
        
        try:
            yield
        finally:
            # Post-operation measurements
            end_time = time.time()
            end_memory = self.process.memory_info().rss / (1024 * 1024)
            end_cpu = self.process.cpu_percent()
            
            # Calculate memory differences
            memory_allocated_mb = None
            memory_deallocated_mb = None
            
            if self.enable_memory_tracing and memory_start_snapshot:
                memory_end_snapshot = tracemalloc.take_snapshot()
                top_stats = memory_end_snapshot.compare_to(
                    memory_start_snapshot, 'lineno'
                )
                
                total_allocated = sum(stat.size_diff for stat in top_stats if stat.size_diff > 0)
                total_deallocated = abs(sum(stat.size_diff for stat in top_stats if stat.size_diff < 0))
                
                memory_allocated_mb = total_allocated / (1024 * 1024)
                memory_deallocated_mb = total_deallocated / (1024 * 1024)
            
            # Create metrics
            duration = end_time - start_time
            metrics = PerformanceMetrics(
                operation_name=operation_name,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                cpu_percent=(start_cpu + end_cpu) / 2,
                memory_mb=start_memory,
                memory_peak_mb=max(start_memory, end_memory),
                memory_allocated_mb=memory_allocated_mb,
                memory_deallocated_mb=memory_deallocated_mb,
                thread_id=threading.get_ident(),
                process_id=self.process.pid,
                additional_data=additional_data
            )
            
            # Store metrics
            self.operation_metrics.append(metrics)
            self.operation_stats[operation_name].append(duration)
            
            # Keep metrics history bounded
            if len(self.operation_metrics) > self.max_metrics_history:
                self.operation_metrics = self.operation_metrics[-self.max_metrics_history:]
            
            # Check for performance issues
            self._check_performance_issues(metrics)
            
    def profile_function(self, operation_name: Optional[str] = None):
        """
        Decorator for profiling function execution.
        
        Usage:
            @profiler.profile_function("my_function")
            def my_function():
                # ... function code ...
        """
        def decorator(func: Callable):
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                name = operation_name or func.__name__
                with self.profile_operation(name):
                    return func(*args, **kwargs)
            return wrapper
        return decorator
        
    def profile_async_function(self, operation_name: Optional[str] = None):
        """
        Decorator for profiling async function execution.
        
        Usage:
            @profiler.profile_async_function("my_async_function")
            async def my_async_function():
                # ... async function code ...
        """
        def decorator(func: Callable):
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                name = operation_name or func.__name__
                with self.profile_operation(name):
                    return await func(*args, **kwargs)
            return wrapper
        return decorator
        
    def _check_performance_issues(self, metrics: PerformanceMetrics):
        """Check for performance issues and log warnings."""
        # High CPU usage
        if metrics.cpu_percent > self.performance_thresholds['cpu_high']:
            logger.warning(
                f"High CPU usage detected in {metrics.operation_name}: "
                f"{metrics.cpu_percent:.1f}%"
            )
        
        # High memory usage
        if metrics.memory_peak_mb > self.performance_thresholds['memory_high']:
            logger.warning(
                f"High memory usage detected in {metrics.operation_name}: "
                f"{metrics.memory_peak_mb:.1f}MB"
            )
        
        # High latency
        if metrics.duration > self.performance_thresholds['latency_high']:
            logger.warning(
                f"High latency detected in {metrics.operation_name}: "
                f"{metrics.duration:.3f}s"
            )
        
        # Memory leak detection
        if (metrics.memory_allocated_mb and 
            metrics.memory_deallocated_mb and
            (metrics.memory_allocated_mb - metrics.memory_deallocated_mb) > 
            self.performance_thresholds['memory_leak_mb']):
            logger.warning(
                f"Potential memory leak detected in {metrics.operation_name}: "
                f"Net allocation: {metrics.memory_allocated_mb - metrics.memory_deallocated_mb:.1f}MB"
            )
            
    def get_operation_stats(self, operation_name: str) -> Dict[str, float]:
        """Get statistical summary for a specific operation."""
        durations = self.operation_stats.get(operation_name, [])
        if not durations:
            return {}
        
        durations_sorted = sorted(durations)
        count = len(durations)
        
        return {
            'count': count,
            'total_time': sum(durations),
            'mean_time': sum(durations) / count,
            'min_time': min(durations),
            'max_time': max(durations),
            'median_time': durations_sorted[count // 2],
            'p95_time': durations_sorted[int(count * 0.95)] if count > 20 else max(durations),
            'p99_time': durations_sorted[int(count * 0.99)] if count > 100 else max(durations)
        }
        
    def get_all_operation_stats(self) -> Dict[str, Dict[str, float]]:
        """Get statistical summary for all operations."""
        return {
            operation: self.get_operation_stats(operation)
            for operation in self.operation_stats.keys()
        }
        
    def get_system_status(self) -> Dict[str, Any]:
        """Get current system performance status."""
        if not self.system_metrics:
            return {'status': 'no_data'}
        
        latest = self.system_metrics[-1]
        
        # Calculate trends if we have enough data
        trends = {}
        if len(self.system_metrics) >= 10:
            recent_metrics = list(self.system_metrics)[-10:]
            cpu_trend = (recent_metrics[-1].cpu_percent - recent_metrics[0].cpu_percent) / 10
            memory_trend = (recent_metrics[-1].memory_percent - recent_metrics[0].memory_percent) / 10
            
            trends = {
                'cpu_trend': cpu_trend,
                'memory_trend': memory_trend
            }
        
        return {
            'timestamp': latest.timestamp,
            'cpu_percent': latest.cpu_percent,
            'memory_percent': latest.memory_percent,
            'memory_available_mb': latest.memory_available_mb,
            'active_threads': latest.active_threads,
            'open_files': latest.open_files,
            'gpu_utilization': latest.gpu_utilization,
            'gpu_memory_mb': latest.gpu_memory_mb,
            'trends': trends
        }
        
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report."""
        report = {
            'generation_time': time.time(),
            'system_status': self.get_system_status(),
            'operation_statistics': self.get_all_operation_stats(),
            'performance_issues': self._analyze_performance_issues(),
            'optimization_recommendations': self._generate_optimization_recommendations(),
            'memory_analysis': self._analyze_memory_usage(),
            'configuration': {
                'memory_tracing_enabled': self.enable_memory_tracing,
                'sample_interval': self.sample_interval,
                'max_metrics_history': self.max_metrics_history,
                'thresholds': self.performance_thresholds
            }
        }
        
        return report
        
    def _analyze_performance_issues(self) -> List[Dict[str, Any]]:
        """Analyze collected metrics for performance issues."""
        issues = []
        
        # Analyze operation metrics
        for operation, stats in self.get_all_operation_stats().items():
            if not stats:
                continue
                
            # High latency operations
            if stats['mean_time'] > self.performance_thresholds['latency_high']:
                issues.append({
                    'type': 'high_latency',
                    'operation': operation,
                    'mean_time': stats['mean_time'],
                    'max_time': stats['max_time'],
                    'count': stats['count']
                })
            
            # Operations with high variability
            if stats['max_time'] > stats['mean_time'] * 5:
                issues.append({
                    'type': 'high_variability',
                    'operation': operation,
                    'mean_time': stats['mean_time'],
                    'max_time': stats['max_time'],
                    'variability_ratio': stats['max_time'] / stats['mean_time']
                })
        
        # Analyze system metrics trends
        if len(self.system_metrics) >= 100:
            recent_metrics = list(self.system_metrics)[-100:]
            
            # CPU trend analysis
            cpu_values = [m.cpu_percent for m in recent_metrics]
            cpu_increasing = sum(1 for i in range(1, len(cpu_values)) 
                               if cpu_values[i] > cpu_values[i-1]) > len(cpu_values) * 0.7
            
            if cpu_increasing and recent_metrics[-1].cpu_percent > 70:
                issues.append({
                    'type': 'cpu_trend_increasing',
                    'current_cpu': recent_metrics[-1].cpu_percent,
                    'trend': 'increasing'
                })
            
            # Memory trend analysis
            memory_values = [m.memory_percent for m in recent_metrics]
            memory_increasing = sum(1 for i in range(1, len(memory_values))
                                  if memory_values[i] > memory_values[i-1]) > len(memory_values) * 0.7
            
            if memory_increasing and recent_metrics[-1].memory_percent > 70:
                issues.append({
                    'type': 'memory_trend_increasing',
                    'current_memory': recent_metrics[-1].memory_percent,
                    'trend': 'increasing'
                })
        
        return issues
        
    def _generate_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Generate optimization recommendations based on performance analysis."""
        recommendations = []
        
        operation_stats = self.get_all_operation_stats()
        
        # Find slowest operations
        slow_operations = [
            (op, stats) for op, stats in operation_stats.items()
            if stats and stats['mean_time'] > 0.1
        ]
        slow_operations.sort(key=lambda x: x[1]['mean_time'], reverse=True)
        
        for operation, stats in slow_operations[:3]:
            recommendations.append({
                'type': 'optimize_operation',
                'operation': operation,
                'current_mean_time': stats['mean_time'],
                'suggestion': f"Consider optimizing {operation} - high mean execution time",
                'priority': 'high' if stats['mean_time'] > 1.0 else 'medium'
            })
        
        # Memory usage recommendations
        if self.system_metrics:
            avg_memory = sum(m.memory_percent for m in self.system_metrics) / len(self.system_metrics)
            if avg_memory > 70:
                recommendations.append({
                    'type': 'memory_optimization',
                    'current_usage': avg_memory,
                    'suggestion': "Consider implementing memory optimization strategies",
                    'priority': 'high'
                })
        
        # Threading recommendations
        if self.system_metrics:
            avg_threads = sum(m.active_threads for m in self.system_metrics) / len(self.system_metrics)
            if avg_threads > 50:
                recommendations.append({
                    'type': 'thread_optimization',
                    'current_threads': avg_threads,
                    'suggestion': "Consider optimizing thread usage - high thread count detected",
                    'priority': 'medium'
                })
        
        return recommendations
        
    def _analyze_memory_usage(self) -> Dict[str, Any]:
        """Analyze memory usage patterns."""
        if not self.operation_metrics:
            return {'status': 'no_data'}
        
        memory_metrics = [m for m in self.operation_metrics if m.memory_allocated_mb is not None]
        
        if not memory_metrics:
            return {'status': 'no_memory_tracing'}
        
        total_allocated = sum(m.memory_allocated_mb or 0 for m in memory_metrics)
        total_deallocated = sum(m.memory_deallocated_mb or 0 for m in memory_metrics)
        net_allocation = total_allocated - total_deallocated
        
        # Find operations with highest memory allocation
        operation_memory = defaultdict(float)
        for metric in memory_metrics:
            operation_memory[metric.operation_name] += metric.memory_allocated_mb or 0
        
        top_memory_operations = sorted(
            operation_memory.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]
        
        return {
            'total_allocated_mb': total_allocated,
            'total_deallocated_mb': total_deallocated,
            'net_allocation_mb': net_allocation,
            'top_memory_operations': top_memory_operations,
            'potential_leaks': net_allocation > self.performance_thresholds['memory_leak_mb']
        }
        
    def save_report(self, filepath: str):
        """Save performance report to file."""
        report = self.generate_performance_report()
        
        with open(filepath, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        logger.info(f"Performance report saved to: {filepath}")
        
    def reset_metrics(self):
        """Reset all collected metrics."""
        self.operation_metrics.clear()
        self.operation_stats.clear()
        self.system_metrics.clear()
        self.memory_snapshots.clear()
        
        if self.enable_memory_tracing:
            tracemalloc.clear_traces()
        
        logger.info("Performance metrics reset")


# Global profiler instance
_global_profiler: Optional[PerformanceProfiler] = None


def get_profiler() -> PerformanceProfiler:
    """Get or create global profiler instance."""
    global _global_profiler
    if _global_profiler is None:
        _global_profiler = PerformanceProfiler()
    return _global_profiler


def profile_operation(operation_name: str, **additional_data):
    """Convenience function for profiling operations."""
    return get_profiler().profile_operation(operation_name, **additional_data)


def profile_function(operation_name: Optional[str] = None):
    """Convenience decorator for profiling functions."""
    return get_profiler().profile_function(operation_name)


def profile_async_function(operation_name: Optional[str] = None):
    """Convenience decorator for profiling async functions."""
    return get_profiler().profile_async_function(operation_name) 