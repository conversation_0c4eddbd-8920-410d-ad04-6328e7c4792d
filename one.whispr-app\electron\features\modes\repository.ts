import { BaseRepository } from '../../database/repositories/base';
import { SchemaWithMigrations } from '../../database/schemas/manager';
import { Mode, DEFAULT_MODES } from './types';

/**
 * Repository for mode configurations
 */
export class ModeRepository extends BaseRepository<Mode> {
  constructor() {
    super('modes');
    this.initializeDefaults();
  }

  /**
   * Ensure the table exists with proper schema
   */
  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS modes (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Initialize with default modes if empty
   */
  private initializeDefaults(): void {
    try {
      const existing = this.findAll();
      if (existing.length === 0) {
        // Create default modes
        DEFAULT_MODES.forEach(mode => {
          this.save(mode);
        });

        console.log('Initialized default modes');
      }
    } catch (error) {
      console.error('Error initializing default modes:', error);
    }
  }

  /**
   * Get active modes ordered by order_index
   */
  getActiveModes(): Mode[] {
    try {
      return this.findAll()
        .filter(mode => mode.isActive)
        .sort((a, b) => a.order_index - b.order_index);
    } catch (error) {
      console.error('Error getting active modes:', error);
      return [];
    }
  }

  /**
   * Get modes by voice model
   */
  findByVoiceModel(voiceModelId: string): Mode[] {
    try {
      return this.findAll().filter(mode => 
        mode.isActive && mode.voiceModel === voiceModelId
      );
    } catch (error) {
      console.error('Error finding modes by voice model:', error);
      return [];
    }
  }

  /**
   * Get modes by AI model
   */
  findByAIModel(aiModelId: string): Mode[] {
    try {
      return this.findAll().filter(mode => 
        mode.isActive && mode.aiModel === aiModelId
      );
    } catch (error) {
      console.error('Error finding modes by AI model:', error);
      return [];
    }
  }

  /**
   * Search modes by name or description
   */
  searchModes(searchTerm: string): Mode[] {
    try {
      const term = searchTerm.toLowerCase();
      return this.findAll().filter(mode => 
        mode.isActive && 
        (mode.name.toLowerCase().includes(term) ||
         mode.description.toLowerCase().includes(term))
      );
    } catch (error) {
      console.error('Error searching modes:', error);
      return [];
    }
  }

  /**
   * Update mode order
   */
  updateOrder(modeId: string, newOrderIndex: number): Mode | null {
    try {
      return this.update(modeId, { order_index: newOrderIndex });
    } catch (error) {
      console.error('Error updating mode order:', error);
      return null;
    }
  }

  /**
   * Bulk update mode orders
   */
  bulkUpdateOrder(orderUpdates: { id: string; order_index: number }[]): boolean {
    try {
      for (const update of orderUpdates) {
        this.update(update.id, { order_index: update.order_index });
      }
      return true;
    } catch (error) {
      console.error('Error bulk updating mode orders:', error);
      return false;
    }
  }

  /**
   * Get mode statistics
   */
  getStatistics(): {
    total: number;
    active: number;
    inactive: number;
    byVoiceModel: Record<string, number>;
    byAIModel: Record<string, number>;
  } {
    try {
      const allModes = this.findAll();
      const active = allModes.filter(mode => mode.isActive);
      const inactive = allModes.filter(mode => !mode.isActive);

      // Count by voice model
      const byVoiceModel: Record<string, number> = {};
      active.forEach(mode => {
        byVoiceModel[mode.voiceModel] = (byVoiceModel[mode.voiceModel] || 0) + 1;
      });

      // Count by AI model
      const byAIModel: Record<string, number> = {};
      active.forEach(mode => {
        if (mode.aiModel) {
          byAIModel[mode.aiModel] = (byAIModel[mode.aiModel] || 0) + 1;
        }
      });

      return {
        total: allModes.length,
        active: active.length,
        inactive: inactive.length,
        byVoiceModel,
        byAIModel
      };
    } catch (error) {
      console.error('Error getting mode statistics:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        byVoiceModel: {},
        byAIModel: {}
      };
    }
  }

  /**
   * Get schema definition for this repository
   */
  static getSchema(): SchemaWithMigrations {
    return {
      name: 'modes',
      createStatement: `
        CREATE TABLE IF NOT EXISTS modes (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            -- Add index for faster lookups
            CREATE INDEX IF NOT EXISTS idx_modes_id ON modes(id);
          `,
          down: `
            DROP INDEX IF EXISTS idx_modes_id;
          `
        }
      ]
    };
  }
} 