/**
 * Audio Level Monitor
 * Real-time audio level visualization for microphone only
 * Shows microphone levels with vertical bar displays
 */

import { HiMicrophone } from 'react-icons/hi';
import type { AudioLevel } from '../../types/audio';

// ============================================================================
// TYPES
// ============================================================================

interface AudioLevelMonitorProps {
  audioLevel: AudioLevel;
  isMonitoring: boolean;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function AudioLevelMonitor({
  audioLevel,
  isMonitoring,
  className
}: AudioLevelMonitorProps) {
  // Apply frontend smoothing to reduce visual jitter
  const micLevel = Math.min(100, Math.max(0, audioLevel.microphone * 100));
  const micClipping = audioLevel.microphone > 0.95;

  const renderLevelColumns = (level: number, isClipping: boolean, label: string) => (
    <div className="space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <HiMicrophone className="w-4 h-4 text-primary" />
          <span className="text-sm font-medium">{label}</span>
        </div>
        {isClipping && (
          <span className="text-xs text-red-500 font-bold">CLIPPING!</span>
        )}
      </div>
      
      {/* Vertical column bars - stable rendering without transitions */}
      <div className="flex items-end justify-center h-16 gap-1 bg-muted/20 rounded-md p-3">
        {Array.from({ length: 20 }, (_, i) => {
          const threshold = (i + 1) * 5; // Each bar represents 5%
          const isActive = level >= threshold;
          const isWarning = threshold > 80;
          const isDanger = threshold > 95;
          
          return (
            <div
              key={i}
              className={`w-3 h-10 rounded-sm ${
                isActive
                  ? isDanger
                    ? 'bg-red-500'
                    : isWarning
                    ? 'bg-yellow-500'
                    : 'bg-primary'
                  : 'bg-muted'
              }`}
            />
          );
        })}
      </div>
    </div>
  );

  if (!isMonitoring) {
    return (
      <div className={`text-center py-8 bg-muted/20 rounded-lg border border-dashed ${className || ''}`}>
        <HiMicrophone className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-sm font-medium text-foreground mb-1">Audio monitoring stopped</h3>
        <p className="text-sm text-muted-foreground">
          Monitoring will start automatically when devices are selected
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Microphone Level */}
      {renderLevelColumns(micLevel, micClipping, 'Microphone')}

      {/* Instructions */}
      <div className="text-sm text-muted-foreground bg-muted/20 rounded-lg p-3">
        <p className="mb-1">
          <strong>Test your microphone:</strong> Speak normally and watch the levels.
        </p>
      </div>
    </div>
  );
}

export default AudioLevelMonitor; 