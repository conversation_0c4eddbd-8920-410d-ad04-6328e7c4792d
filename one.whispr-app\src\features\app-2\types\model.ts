/**
 * Model Types
 * Type definitions for voice models, download and loading progress
 */

// ============================================================================
// MODEL TYPES
// ============================================================================

export interface VoiceModel {
  id: string;
  name: string;
  size: string;
  sizeBytes: number;
  language: string;
  type: 'whisper' | 'distil-whisper';
  downloadUrl: string;
  isDownloaded: boolean;
  isLoaded: boolean;
  isRecommended?: boolean;
  description: string;
  isMultilingual?: boolean;
}

export interface DownloadProgress {
  modelId: string;
  progress: number;
  status: 'downloading' | 'completed' | 'failed' | 'cancelled';
  bytesDownloaded?: number;
  totalBytes?: number;
  speed?: string;
}

export interface LoadingProgress {
  modelId: string;
  status: 'loading' | 'completed' | 'loaded' | 'failed';
  stage?: string;
  progress?: number;
}

// ============================================================================
// STATUS TYPES
// ============================================================================

export type ModelStatus = 
  | 'not-downloaded'
  | 'downloading'
  | 'downloaded'
  | 'loading'
  | 'ready'
  | 'error';

// ============================================================================
// EVENT TYPES
// ============================================================================

export interface ModelEvent {
  type: string;
  data: any;
}

export interface ModelDownloadEvent extends ModelEvent {
  type: 'model.download.progress' | 'model.download.completed' | 'model.download.failed';
  data: DownloadProgress;
}

export interface ModelLoadingEvent extends ModelEvent {
  type: 'model.loading.progress' | 'model.loading.completed' | 'model.loading.failed';
  data: LoadingProgress;
}

export interface ModelState {
  models: VoiceModel[];
  selectedModel: VoiceModel | null;
  loadedModel: string | null;
  downloadProgress: DownloadProgress | null;
  loadingProgress: LoadingProgress | null;
  loading: boolean;
  refreshing: boolean;
  error: string | null;
}

export interface ModelActions {
  loadModels: () => Promise<void>;
  selectModel: (model: VoiceModel) => void;
  downloadModel: (model: VoiceModel) => Promise<void>;
  loadModel: (model: VoiceModel, userInitiated?: boolean) => Promise<void>;
  cancelDownload: () => Promise<void>;
  refreshModels: () => Promise<void>;
  clearError: () => void;
}

export interface ModelContextValue {
  state: ModelState;
  actions: ModelActions;
  canProceedToNext: boolean;
  isModelReady: boolean;
} 