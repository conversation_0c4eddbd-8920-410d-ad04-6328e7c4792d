/**
 * Floating Progress
 * Floating progress bar at the top matching app-latest design
 * Shows step indicators with clickable navigation and progress tracking
 */

import React from 'react';
import type { SetupStep } from '../../types/core';

// ============================================================================
// TYPES
// ============================================================================

interface FloatingProgressProps {
  currentStep: SetupStep;
  completedSteps: Set<SetupStep>;
  steps: SetupStep[];
  onStepClick?: (step: SetupStep) => void;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function FloatingProgress({
  currentStep,
  completedSteps,
  steps,
  onStepClick,
  className
}: FloatingProgressProps) {
  const currentStepIndex = steps.indexOf(currentStep);

  const handleStepClick = (step: SetupStep, index: number) => {
    // Allow navigation to any step (matching app-latest behavior)
    if (onStepClick) {
      onStepClick(step);
    }
  };

  return (
    <div className={`flex items-center justify-center w-full ${className || ''}`}>
      <div className="flex items-center space-x-4 max-w-lg">
        {steps.map((step, index) => (
          <React.Fragment key={step}>
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-semibold transition-all duration-200 ${
                onStepClick ? 'cursor-pointer hover:scale-110' : ''
              } ${
                index < currentStepIndex || completedSteps.has(step)
                  ? 'bg-primary text-primary-foreground shadow-md'
                  : index === currentStepIndex
                  ? 'bg-primary text-primary-foreground shadow-lg ring-2 ring-primary/20'
                  : 'bg-muted text-muted-foreground hover:bg-muted/80'
              }`}
              onClick={() => handleStepClick(step, index)}
              title={`Step ${index + 1}: ${step}`}
            >
              {index < currentStepIndex || completedSteps.has(step) ? '✓' : index + 1}
            </div>
            {index < steps.length - 1 && (
              <div
                className={`w-12 h-1 rounded-full transition-all duration-300 ${
                  index < currentStepIndex || completedSteps.has(step)
                    ? 'bg-primary shadow-sm'
                    : 'bg-muted'
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}

export default FloatingProgress; 