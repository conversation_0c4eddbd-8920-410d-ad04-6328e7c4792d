'use server';

import { z } from 'zod';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';

// Add rate limiting constants
const OTP_RATE_LIMIT_SECONDS = 60; // 1 minute between attempts
const OTP_RATE_LIMIT_COOKIE = 'otp_last_sent';
const OTP_VERIFY_LIMIT_SECONDS = 30; // 30 seconds between verification attempts
const OTP_VERIFY_LIMIT_COOKIE = 'otp_last_verify';
const OTP_VERIFY_MAX_ATTEMPTS = 5; // Max verification attempts before longer cooldown
const OTP_VERIFY_ATTEMPTS_COOKIE = 'otp_verify_attempts';

// Validation schemas
const emailSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

const passwordSchema = z.object({
  email: z.string().email({ message: 'Invalid email format' }),
  password: z.string().min(1, { message: 'Password is required' }),
});

const otpSchema = z.object({
  email: z.string().email({ message: 'Invalid email format' }),
  otp: z.string().length(6, { message: 'OTP must be 6 digits' }),
});

// Define the AuthResponse interface
export interface AuthResponse {
  success: boolean;
  error?: string;
  email?: string;
  nextStep?: string;
  message?: string;
}

/**
 * Verify email and prepare for login
 */
export async function verifyEmail(formData: FormData): Promise<AuthResponse> {
  // Extract and validate email
  const email = formData.get('email') as string;
  
  const result = emailSchema.safeParse({ email });
  if (!result.success) {
    return { 
      success: false, 
      error: result.error.errors[0]?.message || 'Invalid email'
    };
  }
  
  try {
    const supabase = await createClient();
    
    // Instead of sending an OTP, use a more direct way to check user existence
    // This Admin API endpoint would be ideal, but we don't have access to it in client code
    // So we'll use a different approach
    
    // Use an alternative approach that doesn't trigger emails
    // Try to get user by email with a direct postgres query
    // This is done via RLS policy that only permits checking existence without leaking data
    const { data, error } = await supabase
      .from('auth_email_check')
      .select('exists')
      .eq('email', email)
      .single();
    
    if (error) {
      console.error('Error checking user:', error);
      return { 
        success: false, 
        error: 'Failed to verify email' 
      };
    }
    
    // If user doesn't exist in our check table
    if (!data || !data.exists) {
      return { 
        success: false, 
        error: 'Email not found' 
      };
    }
    
    // User exists, proceed to password step
    return { 
      success: true, 
      email,
      nextStep: 'password' 
    };
  } catch (error) {
    console.error('Email verification error:', error);
    return { 
      success: false, 
      error: 'An error occurred during email verification' 
    };
  }
}

/**
 * Authenticate user with password
 */
export async function authenticateWithPassword(formData: FormData): Promise<AuthResponse> {
  try {
    // Extract form data
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    // Validate form data
    const result = passwordSchema.safeParse({ email, password });
    if (!result.success) {
      return {
        success: false,
        error: result.error.message,
      };
    }

    // Authenticate with Supabase
    const supabase = await createClient();
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      return {
        success: false,
        error: error.message,
      };
    }

    // Successfully authenticated
    return {
      success: true,
      email,
    };
  } catch (error) {
    console.error('Authentication error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred during authentication.',
    };
  }
}

/**
 * Generate OTP for passwordless login
 */
export async function generateLoginOTP(formData: FormData): Promise<AuthResponse> {
  const email = formData.get('email') as string;
  
  const result = emailSchema.safeParse({ email });
  if (!result.success) {
    return { 
      success: false, 
      error: result.error.errors[0]?.message || 'Invalid email'
    };
  }
  
  try {
    // Check rate limiting using cookies
    const cookieStore = await cookies();
    const lastSentCookie = cookieStore.get(OTP_RATE_LIMIT_COOKIE);
    
    if (lastSentCookie) {
      try {
        const lastSentData = JSON.parse(lastSentCookie.value);
        const lastSentTime = new Date(lastSentData.timestamp);
        const currentTime = new Date();
        const timeDiffSeconds = Math.floor((currentTime.getTime() - lastSentTime.getTime()) / 1000);
        
        // If the email matches and not enough time has passed
        if (lastSentData.email === email && timeDiffSeconds < OTP_RATE_LIMIT_SECONDS) {
          const timeLeftSeconds = OTP_RATE_LIMIT_SECONDS - timeDiffSeconds;
          return {
            success: false,
            error: `Please wait ${timeLeftSeconds} seconds before requesting another code`
          };
        }
      } catch (e) {
        // Invalid cookie, ignore and proceed
        console.error('Invalid rate limit cookie:', e);
      }
    }
    
    const supabase = await createClient();
    
    // Create an options object with only the necessary properties
    // to prevent duplicate OTP codes
    const options: any = {
      shouldCreateUser: false // Don't create a new user if not found
    };
    
    // Add email redirect only when needed
    // This creates a magic link that will redirect to the success page
    options.emailRedirectTo = `${process.env.NEXT_PUBLIC_SITE_URL}/success`;
    
    // Send magic link email with OTP
    const { error } = await supabase.auth.signInWithOtp({
      email,
      options
    });
    
    if (error) {
      return {
        success: false,
        error: error.message
      };
    }
    
    // Set rate limiting cookie
    const rateLimitData = {
      email,
      timestamp: new Date().toISOString()
    };
    
    cookieStore.set(OTP_RATE_LIMIT_COOKIE, JSON.stringify(rateLimitData), {
      path: '/',
      maxAge: 60 * 60, // 1 hour (longer than the rate limit to ensure it's present)
      httpOnly: true,
      sameSite: 'lax',
    });
    
    return { 
      success: true,
      email,
      message: 'Check your email for the login link' 
    };
  } catch (error) {
    console.error('OTP generation error:', error);
    return { 
      success: false, 
      error: 'Failed to send login email' 
    };
  }
}

/**
 * Verify OTP for login
 */
export async function verifyLoginOTP(formData: FormData): Promise<AuthResponse> {
  try {
    // Extract form data
    const email = formData.get('email') as string;
    const otp = formData.get('otp') as string;

    // Validate form data
    const result = otpSchema.safeParse({ email, otp });
    if (!result.success) {
      return {
        success: false,
        error: result.error.message,
      };
    }

    // Add a small delay to prevent rapid successive requests
    await new Promise(resolve => setTimeout(resolve, 500));

    // Check rate limiting for verification attempts
    const cookieStore = await cookies();
    const lastVerifyCookie = cookieStore.get(OTP_VERIFY_LIMIT_COOKIE);
    const attemptsCookie = cookieStore.get(OTP_VERIFY_ATTEMPTS_COOKIE);
    
    let attempts = 0;
    if (attemptsCookie) {
      try {
        const attemptsData = JSON.parse(attemptsCookie.value);
        if (attemptsData.email === email) {
          attempts = attemptsData.count || 0;
        }
      } catch (e) {
        console.error('Invalid attempts cookie:', e);
      }
    }

    if (lastVerifyCookie && attempts >= OTP_VERIFY_MAX_ATTEMPTS) {
      try {
        const lastVerifyData = JSON.parse(lastVerifyCookie.value);
        const lastVerifyTime = new Date(lastVerifyData.timestamp);
        const currentTime = new Date();
        const timeDiffSeconds = Math.floor((currentTime.getTime() - lastVerifyTime.getTime()) / 1000);
        
        // If the email matches and not enough time has passed (longer cooldown after max attempts)
        if (lastVerifyData.email === email && timeDiffSeconds < OTP_VERIFY_LIMIT_SECONDS * 2) {
          const timeLeftSeconds = (OTP_VERIFY_LIMIT_SECONDS * 2) - timeDiffSeconds;
          return {
            success: false,
            error: `Too many attempts. Please wait ${timeLeftSeconds} seconds before trying again.`
          };
        }
      } catch (e) {
        console.error('Invalid rate limit cookie:', e);
      }
    } else if (lastVerifyCookie) {
      try {
        const lastVerifyData = JSON.parse(lastVerifyCookie.value);
        const lastVerifyTime = new Date(lastVerifyData.timestamp);
        const currentTime = new Date();
        const timeDiffSeconds = Math.floor((currentTime.getTime() - lastVerifyTime.getTime()) / 1000);
        
        // If the email matches and not enough time has passed
        if (lastVerifyData.email === email && timeDiffSeconds < OTP_VERIFY_LIMIT_SECONDS) {
          const timeLeftSeconds = OTP_VERIFY_LIMIT_SECONDS - timeDiffSeconds;
          return {
            success: false,
            error: `Please wait ${timeLeftSeconds} seconds between verification attempts`
          };
        }
      } catch (e) {
        console.error('Invalid rate limit cookie:', e);
      }
    }
    
    // Update the verification attempt timestamp
    cookieStore.set(OTP_VERIFY_LIMIT_COOKIE, JSON.stringify({
      email,
      timestamp: new Date().toISOString()
    }), {
      path: '/',
      maxAge: 60 * 60, // 1 hour
      httpOnly: true,
      sameSite: 'lax',
    });
    
    // Update attempts count
    cookieStore.set(OTP_VERIFY_ATTEMPTS_COOKIE, JSON.stringify({
      email,
      count: attempts + 1
    }), {
      path: '/',
      maxAge: 60 * 10, // 10 minutes
      httpOnly: true,
      sameSite: 'lax',
    });

    // Verify OTP with Supabase
    const supabase = await createClient();
    
    try {
      const { data, error } = await supabase.auth.verifyOtp({
        email,
        token: otp,
        type: 'email',
      });

      if (error) {
        console.error('OTP verification error:', error.message);
        
        // Check for specific error types with more precise handling
        if (error.message.toLowerCase().includes('token is invalid')) {
          return {
            success: false,
            error: 'Verification code is invalid. Please check and try again.',
          };
        }
        
        if (error.message.toLowerCase().includes('token has expired')) {
          // Reset the attempts counter on expired token
          cookieStore.set(OTP_VERIFY_ATTEMPTS_COOKIE, JSON.stringify({
            email,
            count: 0
          }), {
            path: '/',
            maxAge: 60 * 10,
            httpOnly: true,
            sameSite: 'lax',
          });
          
          return {
            success: false,
            error: 'Your verification code has expired. Please request a new one.',
          };
        }
        
        // General case for invalid tokens
        if (error.message.toLowerCase().includes('invalid') || error.message.toLowerCase().includes('expired')) {
          return {
            success: false,
            error: 'Verification code is no longer valid. Please request a new one.',
          };
        }
        
        return {
          success: false,
          error: error.message,
        };
      }

      // Check if authentication was actually successful
      const { data: sessionData } = await supabase.auth.getSession();
      if (!sessionData.session) {
        return {
          success: false,
          error: 'Failed to create a session. Please try again.',
        };
      }

      // Successfully authenticated - reset attempts
      cookieStore.set(OTP_VERIFY_ATTEMPTS_COOKIE, JSON.stringify({
        email,
        count: 0
      }), {
        path: '/',
        maxAge: 60 * 10,
        httpOnly: true,
        sameSite: 'lax',
      });

      // Successfully authenticated
      return {
        success: true,
        email,
      };
    } catch (supabaseError) {
      console.error('Supabase OTP verification error:', supabaseError);
      return {
        success: false,
        error: 'Verification service unavailable. Please try again later.',
      };
    }
  } catch (error) {
    console.error('OTP verification error:', error);
    return {
      success: false,
      error: 'An unexpected error occurred during verification.',
    };
  }
}

/**
 * Handle successful login
 */
export async function handleSuccessfulLogin(callbackUrl?: string): Promise<void> {
  // Redirect to the callback URL or the default URL
  if (callbackUrl) {
    redirect(callbackUrl);
  } else {
    redirect('/');
  }
}

/**
 * Sign out the user
 */
export async function signOut(): Promise<void> {
  const supabase = await createClient();
  await supabase.auth.signOut();
  redirect('/login');
} 