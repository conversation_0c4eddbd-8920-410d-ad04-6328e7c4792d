import { BaseEntity } from '../../database/repositories/base';
import { nanoid } from 'nanoid';

export interface TextReplacement extends BaseEntity {
  id: string;
  original: string;
  replacement: string;
  isActive: boolean;
  modes: string[]; // Mode IDs where this applies
  isRegex?: boolean; // Whether the original is a regex pattern
  caseSensitive?: boolean; // Whether replacement is case sensitive
  wordBoundary?: boolean; // Whether to respect word boundaries ( true = ) 
  order_index: number; // Order for rule application (lower = applied first)
  frequency?: number; // How often this rule has been applied
  lastUsed?: number; // Timestamp when this rule was last applied
}

// ☑ Match complete words/phrases only
//☐ Match anywhere in text

// Default text replacements
export const DEFAULT_TEXT_REPLACEMENTS: TextReplacement[] = [
  {
    id: nanoid(),
    original: 'link my email',
    replacement: '<EMAIL>',
    isActive: true,
    modes: [], // Apply to all modes
    isRegex: false,
    caseSensitive: false,
    wordBoundary: true,
    order_index: 1,
    frequency: 0,
    lastUsed: 0
  },
  {
    id: nanoid(),
    original: '\\b(um|uh|like)\\b',
    replacement: '',
    isActive: true,
    modes: [],
    isRegex: true,
    caseSensitive: false,
    wordBoundary: false,
    order_index: 2,
    frequency: 0,
    lastUsed: 0
  }
]; 