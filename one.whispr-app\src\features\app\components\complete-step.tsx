import { <PERSON>, CardHeader, CardTitle, CardDescription, Card<PERSON>ontent, CardFooter } from '@src/components/ui/card';
import { Button } from '@src/components/ui/button';
import { RiKeyboardLine, RiCheckboxCircleFill, RiArrowRightSLine } from 'react-icons/ri';
import { useShortcuts, useAudio } from '../context';
import { useNavigate } from 'react-router-dom';

export function CompleteStep() {
  const { 
    recordingMode,
    shortcuts
  } = useShortcuts();
  
  const {
    transcribedText
  } = useAudio();
  
  const navigate = useNavigate();
  
  const handleStart = () => {
    // Navigate to the main application (modes page)
    navigate('/modes');
  };
  
  // Format shortcut for display
  const formatShortcut = (keys: string[]) => {
    return keys.join(' + ');
  };
  
  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center space-y-2">
        <div className="flex justify-center mb-1">
          <div className="w-14 h-14 rounded-full bg-green-100 flex items-center justify-center text-green-600">
            <RiCheckboxCircleFill size={32} />
          </div>
        </div>
        <CardTitle className="text-2xl">Final Test</CardTitle>
        <CardDescription className="mt-1">
          Try out your voice-to-text settings before you start
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6 pt-2">
        <div className="space-y-3">
          <div className="border rounded-md p-4 min-h-[120px]">
            {transcribedText ? (
              <p className="text-sm">{transcribedText}</p>
            ) : (
              <p className="text-sm text-muted-foreground italic">
                Your transcription will appear here...
              </p>
            )}
          </div>
          
          <div className="bg-muted/50 rounded-md p-3 flex items-start gap-2.5">
            <RiKeyboardLine size={18} className="text-primary flex-shrink-0 mt-0.5" />
            {recordingMode === 'pushToTalk' ? (
              <div>
                <p className="text-sm text-foreground font-medium mb-1">Push-to-Talk Mode</p>
                <p className="text-xs text-muted-foreground">
                  Press and hold <span className="font-mono bg-muted/80 px-1.5 py-0.5 rounded text-xs">{formatShortcut(shortcuts.pushToTalk)}</span> to record, and release to stop recording.
                </p>
              </div>
            ) : (
              <div>
                <p className="text-sm text-foreground font-medium mb-1">Toggle Mode</p>
                <p className="text-xs text-muted-foreground">
                  Press <span className="font-mono bg-muted/80 px-1.5 py-0.5 rounded text-xs">{formatShortcut(shortcuts.toggle)}</span> to start recording, and press again to stop. 
                  Use <span className="font-mono bg-muted/80 px-1.5 py-0.5 rounded text-xs">{formatShortcut(shortcuts.cancel)}</span> to cancel.
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-4">
        <Button className="w-full font-medium gap-2" onClick={handleStart}>
          Start Using One Whispr <RiArrowRightSLine size={16} />
        </Button>
      </CardFooter>
    </Card>
  );
} 