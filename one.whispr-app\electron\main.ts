
import { app } from 'electron';
import { resetStore, createSettingsWindow } from './windows';
import { resetDatabase } from './database/core/connection';
import { initializeFeatures, setupInitialDatabaseSync, setupBackendEventForwarding } from './features';
import { setupTray } from './tray';

const RESET_ON_START = true;

// Request a single instance lock to prevent multiple instances
const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  // Quit if we're the second instance
  app.quit();
} else {
  app.whenReady().then(async () => {
    if (RESET_ON_START) {
      resetStore();
      resetDatabase();
    }

    // setup tray
    setupTray();

    // 1. Initialize all application features (database, backend, IPC)
    await initializeFeatures();
    console.log('[MAIN] Features initialized successfully');

    // 2. Create the settings window but don't show it yet
    createSettingsWindow();
    console.log('[MAIN] Settings window created');

    // 3. Set up backend event forwarding to the renderer now that window exists
    setupBackendEventForwarding();
    console.log('[MAIN] Backend event forwarding set up');

    // 4. Wait for initial database sync to complete
    await setupInitialDatabaseSync();
    console.log('[MAIN] Initial database sync completed');
  });
}
