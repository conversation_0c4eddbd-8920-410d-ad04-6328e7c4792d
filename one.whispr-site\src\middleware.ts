import { type NextRequest } from 'next/server'
import { updateSession } from '@/utils/supabase/middleware'
import { NextResponse } from 'next/server'
import { createServerClient } from '@supabase/ssr'

export async function middleware(request: NextRequest) {
  const url = request.nextUrl.clone()
  
  // Get the supabase response with updated session
  const supabaseResponse = await updateSession(request)
  
  // Check if this is a success page - if so, don't redirect 
  // This ensures we don't interfere with the desktop app redirect
  if (url.pathname === '/success') {
    return supabaseResponse;
  }
  
  // Check if this is a login or register page
  const isAuthPage = url.pathname === '/login' || url.pathname === '/register'
  
  if (isAuthPage) {
    // Create server client to check authentication
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          getAll() {
            return request.cookies.getAll()
          },
          setAll(cookiesToSet) {
            // We don't need to set cookies here for the auth check
          },
        },
      }
    )
    
    // Check if user is already authenticated
    const { data: { session } } = await supabase.auth.getSession()
    
    if (session) {
      // User is already authenticated, redirect appropriately
      
      // Check if there's a callbackUrl for the desktop app
      const callbackUrl = url.searchParams.get('callbackUrl')
      
      if (callbackUrl && callbackUrl.startsWith('whispr://')) {
        // If there's a callbackUrl for the desktop app, redirect to success
        const redirectUrl = new URL('/success', request.url)
        redirectUrl.searchParams.set('callbackUrl', callbackUrl)
        redirectUrl.searchParams.set('action', url.pathname === '/register' ? 'register' : 'login')
        
        // Copy cookies from supabaseResponse to maintain the session
        const response = NextResponse.redirect(redirectUrl)
        
        // Copy all cookies from supabaseResponse
        for (const [name, value] of Object.entries(supabaseResponse.cookies.getAll())) {
          response.cookies.set(name, value.value, {
            domain: value.domain,
            expires: value.expires,
            httpOnly: value.httpOnly,
            maxAge: value.maxAge,
            path: value.path,
            sameSite: value.sameSite,
            secure: value.secure,
          })
        }
        
        return response
      } else {
        // If no callbackUrl, redirect to account page
        const response = NextResponse.redirect(new URL('/account', request.url))
        
        // Copy all cookies from supabaseResponse
        for (const [name, value] of Object.entries(supabaseResponse.cookies.getAll())) {
          response.cookies.set(name, value.value, {
            domain: value.domain,
            expires: value.expires,
            httpOnly: value.httpOnly,
            maxAge: value.maxAge,
            path: value.path,
            sameSite: value.sameSite,
            secure: value.secure,
          })
        }
        
        return response
      }
    }
  }
  
  // Only intercept the forgot-password route
  if (url.pathname === '/forgot-password') {
    const email = url.searchParams.get('email')
    const step = url.searchParams.get('step')
    const trigger = url.searchParams.get('trigger')
    
    // If we have an email, no step (or email step), and trigger=login
    // Then redirect directly to the OTP step
    if (email && (!step || step === 'email') && trigger === 'login') {
      // Create a new URL with the OTP step
      const redirectUrl = new URL('/forgot-password', request.url)
      redirectUrl.searchParams.set('step', 'otp')
      redirectUrl.searchParams.set('email', email)
      redirectUrl.searchParams.set('autoSubmitted', 'true')
      redirectUrl.searchParams.set('trigger', trigger)
      
      // The actual password reset request will be handled client-side
      // We just want to skip showing the email form
      return NextResponse.redirect(redirectUrl)
    }
  }
  
  // For all other routes, return the supabaseResponse
  return supabaseResponse
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * Feel free to modify this pattern to include more paths.
     */
    '/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
}