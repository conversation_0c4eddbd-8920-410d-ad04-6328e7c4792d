import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription, Card<PERSON>ontent, CardFooter } from '@src/components/ui/card';
import { Button } from '@src/components/ui/button';
import { useSetup } from '../context';
import { RiSoundModuleLine, RiHeadphoneLine, RiKeyboardLine, RiArrowRightSLine, RiVolumeUpLine } from 'react-icons/ri';

export function WelcomeStep() {
  const { goToNextStep } = useSetup();
  
  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center space-y-1">
        <CardTitle className="text-2xl">Let's Setup One Whispr</CardTitle>
        <CardDescription>Your AI-powered voice transcription tool</CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-start gap-3">
            <div className="mt-0.5 p-2 bg-primary/10 text-primary rounded-md flex items-center justify-center w-9 h-9">
              <RiSoundModuleLine size={20} />
            </div>
            <div>
              <h3 className="text-sm font-medium">Choose a Voice Model</h3>
              <p className="text-sm text-muted-foreground">
                Select from various sizes and language options to best fit your needs
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="mt-0.5 p-2 bg-primary/10 text-primary rounded-md flex items-center justify-center w-9 h-9">
              <RiVolumeUpLine size={20} />
            </div>
            <div>
              <h3 className="text-sm font-medium">Configure Audio</h3>
              <p className="text-sm text-muted-foreground">
                Set up your microphone and test audio input levels
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="mt-0.5 p-2 bg-primary/10 text-primary rounded-md flex items-center justify-center w-9 h-9">
              <RiHeadphoneLine size={20} />
            </div>
            <div>
              <h3 className="text-sm font-medium">Set Recording Mode</h3>
              <p className="text-sm text-muted-foreground">
                Choose between Push-To-Talk or Toggle recording for your convenience
              </p>
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="mt-0.5 p-2 bg-primary/10 text-primary rounded-md flex items-center justify-center w-9 h-9">
              <RiKeyboardLine size={20} />
            </div>
            <div>
              <h3 className="text-sm font-medium">Configure Shortcuts</h3>
              <p className="text-sm text-muted-foreground">
                Customize your keyboard shortcuts for quick access
              </p>
            </div>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="pt-2">
        <Button className="w-full gap-1 font-medium" onClick={goToNextStep}>
          Get Started <RiArrowRightSLine size={16} />
        </Button>
      </CardFooter>
    </Card>
  );
} 