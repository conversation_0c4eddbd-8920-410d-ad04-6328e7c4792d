/**
 * Step Header
 * Consistent header component for setup flow steps
 * Matches app-latest design with title, description, and optional icon
 */

import React from 'react';
import { cn } from '@src/lib/utils';

// ============================================================================
// TYPES
// ============================================================================

interface StepHeaderProps {
  title: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  className?: string;
  centered?: boolean;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function StepHeader({
  title,
  description,
  icon: Icon,
  className,
  centered = true
}: StepHeaderProps) {
  return (
    <div className={cn(
      'space-y-2',
      centered && 'text-center',
      className
    )}>
      {Icon && (
        <div className={cn(
          'mb-3',
          centered && 'flex justify-center'
        )}>
          <Icon className="w-8 h-8 text-primary" />
        </div>
      )}
      
      <h2 className="text-2xl font-semibold text-foreground">
        {title}
      </h2>
      
      {description && (
        <p className="text-muted-foreground">
          {description}
        </p>
      )}
    </div>
  );
}

export default StepHeader; 