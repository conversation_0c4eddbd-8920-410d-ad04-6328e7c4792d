"""
Database management for One.Whispr.

This module contains the DatabaseManager class that handles database operations,
synchronization with the Electron frontend, and configuration management.
"""

import asyncio
import hashlib
import json
import logging
from typing import Dict, List, Any, Optional, Set, Callable

from whispr.core.base import BaseService, ServiceContainer, Event


class DatabaseManager(BaseService):
    """Manages database operations and synchronization."""
    
    # Define configuration change event types - now matches table names directly
    # All events are simply "{table_name}_changed"
    
    def __init__(self, service_container: Optional[ServiceContainer] = None):
        """Initialize the database manager.
        
        Args:
            service_container: The service container for dependency resolution
        """
        super().__init__(service_container)
        self.tables: Dict[str, List[Dict[str, Any]]] = {}
        self.table_schemas: Dict[str, Dict[str, Any]] = {}
        self.table_hashes: Dict[str, str] = {}
        self.sync_task = None
        self.synced = False
        self.table_observers: Dict[str, List[Callable[[str, List[Dict[str, Any]]], None]]] = {}
        self.sync_observers: List[Callable[[bool], None]] = []
    
    async def initialize(self) -> bool:
        """Initialize the database manager.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Call parent initialization
            result = await super().initialize()
            
            # Don't start sync task immediately - wait for data to be synced first
            # The sync task will be started when the first sync happens
            self.logger.info("DatabaseManager initialized successfully")
            
            return result
        except Exception as e:
            self.logger.error(f"Error initializing DatabaseManager: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """Clean up resources.
        
        Returns:
            True if cleanup was successful, False otherwise
        """
        # Cancel background sync task
        self._stop_sync_task()
            
        return await super().cleanup()
    
    def _start_sync_task(self) -> None:
        """Start the background sync task."""
        if self.sync_task is None:
            self.sync_task = asyncio.create_task(self._sync_loop())
            self.logger.debug("Started background sync task")
    
    def _stop_sync_task(self) -> None:
        """Stop the background sync task."""
        if self.sync_task:
            self.sync_task.cancel()
            self.sync_task = None
            self.logger.debug("Stopped background sync task")
    
    async def _sync_loop(self) -> None:
        """Background task for future sync functionality.
        
        Note: Currently database sync is push-based from frontend via
        process_initial_sync() and process_changes(). This method is
        reserved for future pull-based sync functionality if needed.
        """
        try:
            while True:
                # Future implementation would go here
                await asyncio.sleep(30)  # Placeholder sleep
                pass
        except asyncio.CancelledError:
            self.logger.debug("Sync loop cancelled")
        except Exception as e:
            self.logger.error(f"Error in sync loop: {e}")
            # Restart the task on error
            asyncio.create_task(self._restart_sync_task())
    
    async def _restart_sync_task(self) -> None:
        """Restart the sync task after a delay."""
        await asyncio.sleep(5.0)  # Wait 5 seconds before restarting
        self._start_sync_task()
    
    async def process_initial_sync(self, database_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process initial database sync from frontend.
        
        Args:
            database_data: The database data containing tables and schemas
            
        Returns:
            Response with sync status
        """
        try:
            tables = database_data.get("tables", [])
            schemas = database_data.get("schemas", {})
            
            # Store tables and schemas
            self.tables = {}
            self.table_schemas = schemas
            
            for table_data in tables:
                table_name = table_data.get("name")
                records = table_data.get("records", [])
                
                if table_name:
                    self.tables[table_name] = records
                    # Generate hash for change detection
                    self.table_hashes[table_name] = self._hash_data(records)
                    
                    # Notify observers
                    self._notify_table_observers(table_name, records)
            
            # Mark as synced
            self.synced = True
            self._notify_sync_observers(True)
            
            self.logger.info(f"Initial database sync completed, received {len(tables)} tables")
            
            # Start sync task now that we have initial data
            config_manager = self.get_service("config")
            database_config = config_manager.get("database") if config_manager else {}
            if database_config.get("sync_on_startup", True):
                self._start_sync_task()
            
            # Update service configurations based on database
            self.update_service_configs()
            
            # Trigger initialization of config-dependent services now that we have database data
            application = self.get_service("application")
            if application:
                try:
                    success = await application.trigger_config_dependent_initialization()
                    if success:
                        self.logger.info("Config-dependent services initialized successfully after database sync")
                    else:
                        self.logger.error("Failed to initialize config-dependent services after database sync")
                except Exception as e:
                    self.logger.error(f"Error triggering config-dependent services initialization: {e}")
            else:
                self.logger.warning("Application instance not available for triggering config-dependent initialization")
            
            return {
                "success": True,
                "message": f"Successfully synced {len(tables)} tables",
                "tables": list(self.tables.keys())
            }
        except Exception as e:
            self.logger.error(f"Error in initial database sync: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def process_changes(self, changes: Dict[str, Any]) -> Dict[str, Any]:
        """Process database changes from frontend.
        
        Args:
            changes: The database changes grouped by table
            
        Returns:
            Response with changes status
        """
        try:
            updates_made = False
            affected_tables = set()
            total_upserts = 0
            total_deletes = 0
            
            # Process changes by table
            for table_name, table_changes in changes.items():
                if not table_changes:
                    continue
                    
                # Ensure table exists in our data
                if table_name not in self.tables:
                    self.tables[table_name] = []
                
                # Process upserts (inserts/updates)
                if 'upserts' in table_changes and table_changes['upserts']:
                    for record in table_changes['upserts']:
                        record_id = record.get('id')
                        if not record_id:
                            self.logger.warning(f"Skipping record without ID in table {table_name}")
                            continue
                            
                        # Parse the data field which contains the actual record data
                        if 'data' in record and isinstance(record['data'], str):
                            try:
                                # For Electron database format, parse the JSON data field
                                record_data = json.loads(record['data'])
                                record_with_id = {"id": record_id, **record_data}
                            except json.JSONDecodeError:
                                self.logger.error(f"Error parsing JSON data for record {record_id} in table {table_name}")
                                continue
                        else:
                            # Direct record format
                            record_with_id = record
                            
                        # Update or add the record in our list format
                        existing_index = None
                        for i, existing_record in enumerate(self.tables[table_name]):
                            if existing_record.get('id') == record_id:
                                existing_index = i
                                break
                                
                        if existing_index is not None:
                            self.tables[table_name][existing_index] = record_with_id
                        else:
                            self.tables[table_name].append(record_with_id)
                            
                        affected_tables.add(table_name)
                        updates_made = True
                        total_upserts += 1
                
                # Process deletes
                if 'deletes' in table_changes and table_changes['deletes']:
                    for record_id in table_changes['deletes']:
                        # Find and remove the record
                        for i, existing_record in enumerate(self.tables[table_name]):
                            if existing_record.get('id') == record_id:
                                self.tables[table_name].pop(i)
                                affected_tables.add(table_name)
                                updates_made = True
                                total_deletes += 1
                                break
            
            # Update hashes and notify observers
            for table in affected_tables:
                # Update hash
                self.table_hashes[table] = self._hash_data(self.tables[table])
                
                # Notify observers
                self._notify_table_observers(table, self.tables[table])
            
            # Update service configurations if needed
            if updates_made:
                self.update_service_configs(affected_tables)
            
            return {
                "success": True,
                "message": f"Processed {total_upserts} upserts, {total_deletes} deletes",
                "affectedTables": list(affected_tables)
            }
        except Exception as e:
            self.logger.error(f"Error processing database changes: {e}")
            return {
                "success": False,
                "error": str(e)
            }
            
    def update_service_configs(self, affected_tables: Optional[Set[str]] = None) -> bool:
        """Update service configurations based on database changes.
        
        Args:
            affected_tables: Set of affected tables or None for all tables
            
        Returns:
            True if updates were made, False otherwise
        """
        try:
            updates_made = False
            events_emitted = []
            
            # Safety check: ensure tables dict exists and has content
            if not self.tables:
                self.logger.debug("No tables available, skipping service config updates")
                return False
                
            tables_to_check = affected_tables if affected_tables is not None else set(self.tables.keys())
            config_manager = self.get_service("config")
            
            if not config_manager:
                self.logger.error("ConfigManager not available for service config updates")
                return False
            
            # Process each table with a 1-to-1 mapping to config sections
            
            # Settings table - store entire settings in the config
            if "settings" in tables_to_check and "settings" in self.tables and self.tables["settings"]:
                settings_record = self.tables["settings"][0]
                self.logger.debug(f"Processing settings record: {settings_record.keys()}")
                
                # Update the config with the entire settings object
                config_manager._update_from_database("settings", "data", settings_record)
                updates_made = True
                
                # Emit a single event for settings changes
                self._emit_event("settings_changed", {
                    "settings": settings_record
                })
                events_emitted.append("settings_changed")
            
            # Modes table - direct mapping
            if "modes" in tables_to_check and "modes" in self.tables:
                modes_list = self.tables["modes"]
                # Convert modes list to dict for easier access
                modes_config = {mode["id"]: mode for mode in modes_list}
                config_manager._update_from_database("modes", "data", modes_config)
                updates_made = True
                
                # Emit event for modes change
                self._emit_event("modes_changed", {
                    "modes": modes_config
                })
                events_emitted.append("modes_changed")
            
            # Vocabulary table - direct mapping
            if "vocabulary" in tables_to_check and "vocabulary" in self.tables:
                vocabulary_data = self.tables["vocabulary"]
                config_manager._update_from_database("vocabulary", "data", vocabulary_data)
                updates_made = True
                
                # Emit event for vocabulary change
                self._emit_event("vocabulary_changed", {
                    "vocabulary": vocabulary_data
                })
                events_emitted.append("vocabulary_changed")
            
            # Text replacements table - direct mapping
            if "text_replacements" in tables_to_check and "text_replacements" in self.tables:
                text_replacements_data = self.tables["text_replacements"]
                config_manager._update_from_database("text_replacements", "data", text_replacements_data)
                updates_made = True
                
                # Emit event for text replacements change
                self._emit_event("text_replacements_changed", {
                    "text_replacements": text_replacements_data
                })
                events_emitted.append("text_replacements_changed")
            
            # Model settings table - direct mapping
            if "modelSettings" in tables_to_check and "modelSettings" in self.tables and self.tables["modelSettings"]:
                model_settings = self.tables["modelSettings"][0]
                config_manager._update_from_database("model_settings", "data", model_settings)
                updates_made = True
                
                # Emit event for model settings change
                self._emit_event("model_settings_changed", {
                    "model_settings": model_settings
                })
                events_emitted.append("model_settings_changed")
            
            if updates_made:
                self.logger.info(f"Service configurations updated successfully, events emitted: {events_emitted}")
                
                # Emit general config changed event
                self._emit_event("config_changed", {
                    "affected_tables": list(tables_to_check),
                    "specific_events": events_emitted
                })
            
            return updates_made
        except Exception as e:
            self.logger.error(f"Error updating service configurations: {e}")
            return False
    
    def _emit_event(self, event_type: str, data: Dict[str, Any]) -> bool:
        """Emit an event using the IPC bridge.
        
        Args:
            event_type: The type of event to emit
            data: Event data
            
        Returns:
            True if event was emitted, False otherwise
        """
        try:
            # Use IPC bridge instead of event_broadcaster
            ipc_bridge = self.get_service("ipc")
            if ipc_bridge:
                event_data = {
                    "type": "event",
                    "event": event_type,
                    "data": data
                }
                # Use sync method since this might be called from non-async context
                return ipc_bridge.send_message_sync(event_data)
            # Return success even if there's no IPC bridge
            # This helps avoid timeout issues during initialization
            return True
        except Exception:
            # Silent fail - this is expected with the removal of event_broadcaster
            # The ConfigManager updates will still happen correctly
            return True
    
    def register_table_observer(self, table: str, observer: Callable[[str, List[Dict[str, Any]]], None]) -> None:
        """Register an observer for a table.
        
        Args:
            table: The table to observe
            observer: The observer function
        """
        if table not in self.table_observers:
            self.table_observers[table] = []
        
        self.table_observers[table].append(observer)
        self.logger.debug(f"Registered observer for table: {table}")
        
        # Notify immediately if table exists
        if table in self.tables:
            observer(table, self.tables[table])
    
    def unregister_table_observer(self, table: str, observer: Callable) -> bool:
        """Unregister a table observer.
        
        Args:
            table: The table to unobserve
            observer: The observer function
            
        Returns:
            True if the observer was found and removed, False otherwise
        """
        if table not in self.table_observers:
            return False
            
        if observer in self.table_observers[table]:
            self.table_observers[table].remove(observer)
            self.logger.debug(f"Unregistered observer for table: {table}")
            return True
            
        return False
    
    def register_sync_observer(self, observer: Callable[[bool], None]) -> None:
        """Register an observer for sync state changes.
        
        Args:
            observer: The observer function
        """
        self.sync_observers.append(observer)
        self.logger.debug("Registered sync observer")
        
        # Notify immediately with current state
        observer(self.synced)
    
    def unregister_sync_observer(self, observer: Callable) -> bool:
        """Unregister a sync observer.
        
        Args:
            observer: The observer function
            
        Returns:
            True if the observer was found and removed, False otherwise
        """
        if observer in self.sync_observers:
            self.sync_observers.remove(observer)
            self.logger.debug("Unregistered sync observer")
            return True
            
        return False
    
    def get_table(self, table: str) -> List[Dict[str, Any]]:
        """Get all records from a table.
        
        Args:
            table: The table name
            
        Returns:
            List of records or empty list if table not found
        """
        return self.tables.get(table, [])
    
    def get_table_schema(self, table: str) -> Optional[Dict[str, Any]]:
        """Get schema for a table.
        
        Args:
            table: The table name
            
        Returns:
            Table schema or None if not found
        """
        return self.table_schemas.get(table)
    
    def _notify_table_observers(self, table: str, records: List[Dict[str, Any]]) -> None:
        """Notify observers of table changes.
        
        Args:
            table: The table that changed
            records: The current records in the table
        """
        if table in self.table_observers:
            for observer in self.table_observers[table]:
                try:
                    observer(table, records)
                except Exception as e:
                    self.logger.error(f"Error in table observer for {table}: {e}")
    
    def _notify_sync_observers(self, synced: bool) -> None:
        """Notify observers of sync state changes.
        
        Args:
            synced: The current sync state
        """
        for observer in self.sync_observers:
            try:
                observer(synced)
            except Exception as e:
                self.logger.error(f"Error in sync observer: {e}")
    
    def _hash_data(self, data) -> str:
        """Create a hash of data for comparison.
        
        Args:
            data: Any serializable data
            
        Returns:
            str: Hash string for comparison
        """
        if data is None:
            return "null"  # Return a string instead of None
            
        try:
            serialized = json.dumps(data, sort_keys=True)
            return hashlib.md5(serialized.encode('utf-8')).hexdigest()
        except:
            # If the data can't be serialized, use its string representation
            return hashlib.md5(str(data).encode('utf-8')).hexdigest()
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current service status.
        
        Returns:
            A dictionary containing status information
        """
        status = super().get_status()
        status.update({
            "synced": self.synced,
            "table_count": len(self.tables),
            "tables": list(self.tables.keys()),
            "record_counts": {table: len(records) for table, records in self.tables.items()}
        })
        return status 

    def load_models(self) -> List[Dict[str, Any]]:
        """Load models from the database.
        
        Returns:
            List of models or empty list if no models found
        """
        try:
            models = self.tables.get("models", [])
            self.logger.info(f"Found {len(models)} models in database")
            
            return models
            
        except Exception as e:
            self.logger.error(f"Error loading models from database: {e}")
            return [] 