/**
 * Recording Mode Selector
 * Component for selecting between push-to-talk and toggle recording modes
 * Matches app-latest design with grid layout and radio button styling
 */

import type { RecordingMode } from '../../types/core';

// ============================================================================
// TYPES
// ============================================================================

interface RecordingModeSelectorProps {
  selectedMode: RecordingMode;
  onModeChange: (mode: RecordingMode) => void;
  disabled?: boolean;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function RecordingModeSelector({
  selectedMode,
  onModeChange,
  disabled = false,
  className
}: RecordingModeSelectorProps) {
  return (
    <div className={`space-y-4 ${className || ''}`}>
      <label className="text-base font-medium block">Recording Mode</label>
      <div className="grid grid-cols-2 gap-4">
        {/* Push-to-Talk Mode */}
        <div 
          className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
            disabled
              ? 'opacity-50 cursor-not-allowed border-muted bg-muted/20'
              : selectedMode === 'pushToTalk' 
                ? 'border-primary bg-primary/5' 
                : 'border-border hover:border-primary/50'
          }`}
          onClick={() => !disabled && onModeChange('pushToTalk')}
        >
          <div className="flex items-center gap-2 mb-2">
            <div className={`w-3 h-3 rounded-full border-2 ${
              selectedMode === 'pushToTalk' 
                ? 'border-primary bg-primary' 
                : 'border-border'
            }`} />
            <span className="font-medium">Push-to-Talk</span>
          </div>
          <p className="text-xs text-muted-foreground">
            Hold to record, release to stop
          </p>
        </div>
        
        {/* Toggle Mode */}
        <div 
          className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
            disabled
              ? 'opacity-50 cursor-not-allowed border-muted bg-muted/20'
              : selectedMode === 'toggle' 
                ? 'border-primary bg-primary/5' 
                : 'border-border hover:border-primary/50'
          }`}
          onClick={() => !disabled && onModeChange('toggle')}
        >
          <div className="flex items-center gap-2 mb-2">
            <div className={`w-3 h-3 rounded-full border-2 ${
              selectedMode === 'toggle' 
                ? 'border-primary bg-primary' 
                : 'border-border'
            }`} />
            <span className="font-medium">Toggle Mode</span>
          </div>
          <p className="text-xs text-muted-foreground">
            Press once to start, again to stop
          </p>
        </div>
      </div>
    </div>
  );
}

export default RecordingModeSelector; 