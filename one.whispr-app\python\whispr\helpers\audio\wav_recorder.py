"""
WAV Recorder

Simple WAV file recorder for 100ms audio chunks.
Handles session-based recording with proper WAV file formatting.
"""

import logging
import wave
import time
import threading
from pathlib import Path
from typing import Dict, Any, Optional
import numpy as np

from .audio_manager import AudioChunk

logger = logging.getLogger(__name__)


class WavRecorder:
    """Simple WAV file recorder for audio chunks."""
    
    def __init__(self):
        """Initialize the WAV recorder."""
        self.is_recording = False
        self.current_session_id = None
        self.wav_file = None
        self.output_path = None
        
        # Audio settings
        self.sample_rate = 16000
        self.channels = 1
        self.sample_width = 2  # 16-bit
        
        # Recording metadata
        self.start_time = None
        self.total_frames = 0
        
        # Thread safety
        self.lock = threading.RLock()
        
        logger.debug("WavRecorder initialized")
    
    def start_recording(self, session_id: str, output_path: Path) -> bool:
        """Start recording to a WAV file.
        
        Args:
            session_id: Unique identifier for this recording session
            output_path: Path where the WAV file will be saved
            
        Returns:
            True if recording started successfully
        """
        with self.lock:
            try:
                if self.is_recording:
                    logger.warning(f"Already recording session {self.current_session_id}")
                    return False
                
                # Ensure output directory exists
                output_path.parent.mkdir(parents=True, exist_ok=True)
                
                # Open WAV file for writing
                self.wav_file = wave.open(str(output_path), 'wb')
                self.wav_file.setnchannels(self.channels)
                self.wav_file.setsampwidth(self.sample_width)
                self.wav_file.setframerate(self.sample_rate)
                
                # Set recording state
                self.is_recording = True
                self.current_session_id = session_id
                self.output_path = output_path
                self.start_time = time.time()
                self.total_frames = 0
                
                logger.info(f"Started WAV recording: {session_id} -> {output_path}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to start WAV recording: {e}")
                self._cleanup_file()
                return False
    
    def write_chunk(self, audio_chunk: AudioChunk) -> None:
        """Write an audio chunk to the WAV file.
        
        Args:
            audio_chunk: AudioChunk dataclass containing audio data
        """
        with self.lock:
            try:
                if not self.is_recording or not self.wav_file:
                    return
                
                # Extract audio data from AudioChunk
                audio_data = audio_chunk.data
                if audio_data is None:
                    logger.warning("Audio chunk missing 'data' field")
                    return
                
                # Convert float32 to int16 for WAV format
                if audio_data.dtype == np.float32:
                    # Clamp to [-1, 1] range and convert to int16
                    audio_data = np.clip(audio_data, -1.0, 1.0)
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                else:
                    audio_int16 = audio_data.astype(np.int16)
                
                # Write to WAV file
                self.wav_file.writeframes(audio_int16.tobytes())
                self.total_frames += len(audio_int16)
                
            except Exception as e:
                logger.error(f"Failed to write audio chunk: {e}")
    
    def stop_recording(self) -> Optional[Dict[str, Any]]:
        """Stop recording and return session metadata.
        
        Returns:
            Dictionary with recording metadata or None if not recording
        """
        with self.lock:
            try:
                if not self.is_recording:
                    logger.warning("Not currently recording")
                    return None
                
                # Calculate recording duration
                end_time = time.time()
                duration = end_time - self.start_time if self.start_time else 0
                
                # Prepare metadata
                metadata = {
                    "session_id": self.current_session_id,
                    "output_path": str(self.output_path) if self.output_path else None,
                    "duration": duration,
                    "total_frames": self.total_frames,
                    "sample_rate": self.sample_rate,
                    "channels": self.channels,
                    "start_time": self.start_time,
                    "end_time": end_time
                }
                
                # Close WAV file
                self._cleanup_file()
                
                # Reset state
                self.is_recording = False
                self.current_session_id = None
                self.output_path = None
                self.start_time = None
                self.total_frames = 0
                
                logger.info(f"Stopped WAV recording: {metadata['session_id']}, duration: {duration:.2f}s")
                return metadata
                
            except Exception as e:
                logger.error(f"Failed to stop WAV recording: {e}")
                self._cleanup_file()
                self.is_recording = False
                return None
    
    def cancel_recording(self) -> bool:
        """Cancel current recording and delete the file.
        
        Returns:
            True if recording was cancelled successfully
        """
        with self.lock:
            try:
                if not self.is_recording:
                    logger.warning("Not currently recording")
                    return True
                
                output_path = self.output_path
                session_id = self.current_session_id
                
                # Close WAV file
                self._cleanup_file()
                
                # Delete the file if it exists
                if output_path and output_path.exists():
                    output_path.unlink()
                    logger.info(f"Deleted cancelled recording: {output_path}")
                
                # Reset state
                self.is_recording = False
                self.current_session_id = None
                self.output_path = None
                self.start_time = None
                self.total_frames = 0
                
                logger.info(f"Cancelled WAV recording: {session_id}")
                return True
                
            except Exception as e:
                logger.error(f"Failed to cancel WAV recording: {e}")
                return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get current recording status.
        
        Returns:
            Dictionary with current recording status
        """
        with self.lock:
            status = {
                "is_recording": self.is_recording,
                "session_id": self.current_session_id,
                "output_path": str(self.output_path) if self.output_path else None,
                "total_frames": self.total_frames,
                "sample_rate": self.sample_rate,
                "channels": self.channels
            }
            
            # Add duration if recording
            if self.is_recording and self.start_time:
                status["duration"] = time.time() - self.start_time
            
            return status
    
    def cleanup(self) -> None:
        """Cleanup recorder resources."""
        with self.lock:
            try:
                if self.is_recording:
                    logger.info("Cleaning up active recording")
                    self.stop_recording()
                
                self._cleanup_file()
                logger.debug("WavRecorder cleanup completed")
                
            except Exception as e:
                logger.error(f"Error during WavRecorder cleanup: {e}")
    
    def _cleanup_file(self) -> None:
        """Internal method to cleanup WAV file resources."""
        try:
            if self.wav_file:
                self.wav_file.close()
                self.wav_file = None
        except Exception as e:
            logger.error(f"Error closing WAV file: {e}") 