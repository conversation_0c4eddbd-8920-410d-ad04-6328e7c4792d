# Create self-signed certificate for local APPX testing
# This script creates a certificate that matches the publisher in electron-builder-microsoft.json

Write-Host "Creating self-signed certificate for One Whispr APPX testing..." -ForegroundColor Green

# Certificate details (must match electron-builder-microsoft.json)
$publisherName = "CN=IjazSadiqBasha"
$certName = "OneWhispr-Dev-Certificate"
$outputPath = ".\dev-certificate"

# Create output directory
if (!(Test-Path $outputPath)) {
    New-Item -ItemType Directory -Path $outputPath -Force
}

# Define cert output paths
$certCerPath = Join-Path $outputPath "OneWhispr-Dev.cer"
$certPfxPath = Join-Path $outputPath "OneWhispr-Dev.pfx"
$password = ConvertTo-SecureString -String "onewhispr-dev" -Force -AsPlainText

# Create self-signed certificate
$cert = New-SelfSignedCertificate `
    -Type Custom `
    -Subject $publisherName `
    -KeyUsage DigitalSignature `
    -FriendlyName $certName `
    -KeyExportPolicy Exportable `
    -CertStoreLocation "Cert:\LocalMachine\My" `
    -NotAfter (Get-Date).AddYears(1) `
    -TextExtension @(
        "*********={text}*******.*******.3", # Code Signing EKU
        "*********={text}" # Basic Constraints
    )

Write-Host "Certificate created with thumbprint: $($cert.Thumbprint)" -ForegroundColor Yellow

# Export certificate (public key)
Export-Certificate -Cert $cert -FilePath $certCerPath -Type CERT

# Export private key
Export-PfxCertificate -Cert $cert -FilePath $certPfxPath -Password $password

# Import to Trusted Root Certification Authorities
Import-Certificate -FilePath $certCerPath -CertStoreLocation "Cert:\LocalMachine\Root"

Write-Host "`nCertificate exported to:" -ForegroundColor Green
Write-Host "  Public: $certCerPath" -ForegroundColor White
Write-Host "  Private: $certPfxPath" -ForegroundColor White
Write-Host "  Password: onewhispr-dev" -ForegroundColor White

Write-Host "`nCertificate successfully installed to Trusted Root store." -ForegroundColor Cyan

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Use this .pfx path in your electron-builder config:" -ForegroundColor White
Write-Host "   $certPfxPath" -ForegroundColor Gray
Write-Host "2. Rebuild your APPX" -ForegroundColor White
Write-Host "3. The publisher should now appear correctly in the installer." -ForegroundColor White

Write-Host "`nCertificate is valid for 1 year from today." -ForegroundColor Yellow

#& "C:\Program Files (x86)\Windows Kits\10\bin\10.0.19041.0\x64\signtool.exe" sign /f "C:\Users\<USER>\source\one.whispr\one.whispr-setup\scripts\dev-certificate\OneWhispr-Dev.pfx" /p onewhispr-dev /fd sha256 /d "One Whispr" "C:\Users\<USER>\source\one.whispr\one.whispr-setup\.release-microsoft\OneWhisprSetup.appx"