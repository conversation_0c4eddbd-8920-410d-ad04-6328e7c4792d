import { ipcMain } from 'electron';
import { VoiceModelRepository, ModelSettingsRepository } from './repository';
import { VoiceModel, ModelSettings } from './types';

const voiceModelRepository = new VoiceModelRepository();
const modelSettingsRepository = new ModelSettingsRepository();

/**
 * Sets up voice model and model settings IPC handlers
 * (Simplified to match Python backend reality)
 */
export function setupAIModelIPCHandlers(): void {
  
  // Voice Models (these are the only ones actually implemented in Python)
  ipcMain.handle('voice-models:get-all', async (): Promise<VoiceModel[]> => {
    try {
      return voiceModelRepository.findAll();
    } catch (error) {
      console.error('Error getting all voice models:', error);
      return [];
    }
  });

  ipcMain.handle('voice-models:get-by-id', async (_, id: string): Promise<VoiceModel | null> => {
    try {
      return voiceModelRepository.findById(id);
    } catch (error) {
      console.error('Error getting voice model by id:', error);
      return null;
    }
  });

  ipcMain.handle('voice-models:get-available', async (): Promise<VoiceModel[]> => {
    try {
      return voiceModelRepository.getAvailableModels();
    } catch (error) {
      console.error('Error getting available voice models:', error);
      return [];
    }
  });

  ipcMain.handle('voice-models:get-downloaded', async (): Promise<VoiceModel[]> => {
    try {
      return voiceModelRepository.getDownloadedModels();
    } catch (error) {
      console.error('Error getting downloaded voice models:', error);
      return [];
    }
  });

  ipcMain.handle('voice-models:get-by-provider', async (_, provider: string): Promise<VoiceModel[]> => {
    try {
      return voiceModelRepository.findByProvider(provider);
    } catch (error) {
      console.error('Error getting voice models by provider:', error);
      return [];
    }
  });

  ipcMain.handle('voice-models:update-status', async (_, id: string, status: Partial<VoiceModel['status']>): Promise<VoiceModel | null> => {
    try {
      return voiceModelRepository.updateStatus(id, status);
    } catch (error) {
      console.error('Error updating voice model status:', error);
      return null;
    }
  });

  ipcMain.handle('voice-models:get-statistics', async () => {
    try {
      return voiceModelRepository.getStatistics();
    } catch (error) {
      console.error('Error getting voice model statistics:', error);
      return {
        total: 0,
        available: 0,
        downloaded: 0,
        byProvider: {}
      };
    }
  });

  // NEW: Sync voice models from Python backend
  ipcMain.handle('voice-models:sync-from-python', async (): Promise<{ success: boolean; count: number; error?: string }> => {
    try {
      // TODO: Call Python backend to get all models
      // This would typically be a call to the Python WebSocket or HTTP API
      // For now, return a placeholder until the Python integration is set up
      
      // Example of what this would look like:
      // const pythonResponse = await callPythonBackend('models:get-all');
      // if (pythonResponse.success) {
      //   const success = voiceModelRepository.syncFromPython(pythonResponse.models);
      //   return { success, count: pythonResponse.models.length };
      // }
      
      console.log('voice-models:sync-from-python called - Python integration needed');
      return { 
        success: false, 
        count: 0, 
        error: 'Python backend integration not yet implemented for model sync' 
      };
    } catch (error) {
      console.error('Error syncing voice models from Python:', error);
      return { 
        success: false, 
        count: 0, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      };
    }
  });

  // Model Settings
  ipcMain.handle('model-settings:get', async (): Promise<ModelSettings | null> => {
    try {
      return modelSettingsRepository.getModelSettings();
    } catch (error) {
      console.error('Error getting model settings:', error);
      return null;
    }
  });

  ipcMain.handle('model-settings:update', async (_, updates: Partial<Omit<ModelSettings, 'id'>>): Promise<ModelSettings | null> => {
    try {
      return modelSettingsRepository.updateModelSettings(updates);
    } catch (error) {
      console.error('Error updating model settings:', error);
      return null;
    }
  });

  ipcMain.handle('model-settings:set-active-voice-model', async (_, modelId: string): Promise<ModelSettings | null> => {
    try {
      return modelSettingsRepository.setActiveVoiceModel(modelId);
    } catch (error) {
      console.error('Error setting active voice model:', error);
      return null;
    }
  });

  ipcMain.handle('model-settings:sync-available-voice-models', async (_, models: VoiceModel[]): Promise<ModelSettings | null> => {
    try {
      return modelSettingsRepository.syncAvailableVoiceModels(models);
    } catch (error) {
      console.error('Error syncing available voice models to settings:', error);
      return null;
    }
  });

  console.log('Voice Model IPC handlers registered (simplified for Python backend reality)');
} 