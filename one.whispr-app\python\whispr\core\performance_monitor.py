"""
Performance monitoring and optimization system for <PERSON>.Whispr.

This module provides comprehensive performance monitoring, profiling, and optimization
capabilities for the transcription system components.
"""

import asyncio
import gc
import logging
import psutil
import threading
import time
import tracemalloc
from collections import defaultdict, deque
from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List, Callable, Tuple
from contextlib import contextmanager
import weakref

from whispr.core.base import BaseService, ServiceContainer


@dataclass
class PerformanceMetrics:
    """Performance metrics for a specific operation or service."""
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration_ms: Optional[float] = None
    memory_start_mb: Optional[float] = None
    memory_end_mb: Optional[float] = None
    memory_peak_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    thread_count: Optional[int] = None
    context: Dict[str, Any] = field(default_factory=dict)
    
    def finalize(self, end_time: Optional[float] = None) -> None:
        """Finalize the metrics calculation."""
        if end_time is None:
            end_time = time.time()
        self.end_time = end_time
        self.duration_ms = (self.end_time - self.start_time) * 1000.0


@dataclass
class SystemMetrics:
    """System-wide performance metrics."""
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_available_mb: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_io_sent_mb: float
    network_io_recv_mb: float
    process_memory_mb: float
    process_cpu_percent: float
    thread_count: int
    gc_collections: Dict[int, int] = field(default_factory=dict)


class PerformanceProfiler:
    """Context manager for profiling performance of code blocks."""
    
    def __init__(self, monitor: 'PerformanceMonitor', operation_name: str, 
                 context: Optional[Dict[str, Any]] = None):
        """Initialize profiler.
        
        Args:
            monitor: Performance monitor instance
            operation_name: Name of the operation being profiled
            context: Additional context information
        """
        self.monitor = monitor
        self.operation_name = operation_name
        self.context = context or {}
        self.metrics: Optional[PerformanceMetrics] = None
        
    def __enter__(self) -> PerformanceMetrics:
        """Start profiling."""
        return self.start()
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        """End profiling."""
        self.stop()
        
    def start(self) -> PerformanceMetrics:
        """Start profiling and return metrics object."""
        start_time = time.time()
        
        # Get initial memory usage
        memory_start = None
        try:
            process = psutil.Process()
            memory_start = process.memory_info().rss / 1024 / 1024  # MB
        except Exception:
            pass
        
        self.metrics = PerformanceMetrics(
            operation_name=self.operation_name,
            start_time=start_time,
            memory_start_mb=memory_start,
            context=self.context.copy()
        )
        
        return self.metrics
        
    def stop(self) -> PerformanceMetrics:
        """Stop profiling and finalize metrics."""
        if not self.metrics:
            raise RuntimeError("Profiler not started")
            
        end_time = time.time()
        
        # Get final memory usage and CPU
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            self.metrics.memory_end_mb = memory_info.rss / 1024 / 1024  # MB
            self.metrics.cpu_usage_percent = process.cpu_percent()
            self.metrics.thread_count = process.num_threads()
        except Exception:
            pass
            
        self.metrics.finalize(end_time)
        
        # Report to monitor
        self.monitor.record_metrics(self.metrics)
        
        return self.metrics


class PerformanceMonitor(BaseService):
    """Comprehensive performance monitoring system."""
    
    def __init__(self, service_container: Optional[ServiceContainer] = None):
        """Initialize performance monitor.
        
        Args:
            service_container: Service container for dependency resolution
        """
        super().__init__(service_container)
        
        # Metrics storage
        self.operation_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.system_metrics_history: deque = deque(maxlen=1000)
        
        # Performance tracking
        self.operation_stats: Dict[str, Dict[str, Any]] = defaultdict(dict)
        self.performance_alerts: List[Dict[str, Any]] = []
        
        # Configuration
        self.enable_memory_tracking = True
        self.enable_system_monitoring = True
        self.system_monitor_interval = 5.0  # seconds
        self.memory_alert_threshold_mb = 1000  # MB
        self.cpu_alert_threshold_percent = 80.0
        self.latency_alert_threshold_ms = 500.0
        
        # Monitoring state
        self.monitoring_active = False
        self.system_monitor_task: Optional[asyncio.Task] = None
        self.last_disk_io = None
        self.last_network_io = None
        
        # Memory tracking
        self.memory_tracking_enabled = False
        
        # Weak references to avoid memory leaks
        self._profiler_registry: weakref.WeakSet = weakref.WeakSet()
    
    async def initialize(self) -> bool:
        """Initialize the performance monitor.
        
        Returns:
            True if initialization was successful
        """
        try:
            # Load configuration
            config_manager = self.get_service("config")
            if config_manager:
                perf_config = config_manager.get("performance", {})
                self.enable_memory_tracking = perf_config.get("enable_memory_tracking", True)
                self.enable_system_monitoring = perf_config.get("enable_system_monitoring", True)
                self.system_monitor_interval = perf_config.get("system_monitor_interval", 5.0)
                self.memory_alert_threshold_mb = perf_config.get("memory_alert_threshold_mb", 1000)
                self.cpu_alert_threshold_percent = perf_config.get("cpu_alert_threshold_percent", 80.0)
                self.latency_alert_threshold_ms = perf_config.get("latency_alert_threshold_ms", 500.0)
            
            # Start memory tracking if enabled
            if self.enable_memory_tracking and not self.memory_tracking_enabled:
                try:
                    tracemalloc.start()
                    self.memory_tracking_enabled = True
                    self.logger.info("Memory tracking enabled")
                except Exception as e:
                    self.logger.warning(f"Failed to enable memory tracking: {e}")
            
            # Start system monitoring
            if self.enable_system_monitoring:
                await self.start_system_monitoring()
            
            self.monitoring_active = True
            self.logger.info("Performance monitor initialized successfully")
            return await super().initialize()
            
        except Exception as e:
            self.logger.error(f"Error initializing performance monitor: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """Clean up performance monitor resources.
        
        Returns:
            True if cleanup was successful
        """
        try:
            self.monitoring_active = False
            
            # Stop system monitoring
            if self.system_monitor_task:
                self.system_monitor_task.cancel()
                try:
                    await self.system_monitor_task
                except asyncio.CancelledError:
                    pass
            
            # Stop memory tracking
            if self.memory_tracking_enabled:
                try:
                    tracemalloc.stop()
                    self.memory_tracking_enabled = False
                except Exception:
                    pass
            
            self.logger.info("Performance monitor cleaned up successfully")
            return await super().cleanup()
            
        except Exception as e:
            self.logger.error(f"Error cleaning up performance monitor: {e}")
            return False
    
    def profile_operation(self, operation_name: str, 
                         context: Optional[Dict[str, Any]] = None) -> PerformanceProfiler:
        """Create a performance profiler for an operation.
        
        Args:
            operation_name: Name of the operation to profile
            context: Additional context information
            
        Returns:
            Performance profiler context manager
        """
        profiler = PerformanceProfiler(self, operation_name, context)
        self._profiler_registry.add(profiler)
        return profiler
    
    @contextmanager
    def profile(self, operation_name: str, context: Optional[Dict[str, Any]] = None):
        """Context manager for profiling operations.
        
        Args:
            operation_name: Name of the operation to profile
            context: Additional context information
            
        Yields:
            PerformanceMetrics object
        """
        with self.profile_operation(operation_name, context) as metrics:
            yield metrics
    
    def record_metrics(self, metrics: PerformanceMetrics) -> None:
        """Record performance metrics.
        
        Args:
            metrics: Performance metrics to record
        """
        try:
            # Store metrics
            self.operation_metrics[metrics.operation_name].append(metrics)
            
            # Update statistics
            self._update_operation_stats(metrics)
            
            # Check for performance alerts
            self._check_performance_alerts(metrics)
            
        except Exception as e:
            self.logger.error(f"Error recording metrics: {e}")
    
    def _update_operation_stats(self, metrics: PerformanceMetrics) -> None:
        """Update operation statistics."""
        stats = self.operation_stats[metrics.operation_name]
        
        # Initialize stats if needed
        if 'count' not in stats:
            stats.update({
                'count': 0,
                'total_duration_ms': 0.0,
                'min_duration_ms': float('inf'),
                'max_duration_ms': 0.0,
                'avg_duration_ms': 0.0,
                'total_memory_mb': 0.0,
                'avg_memory_mb': 0.0,
                'last_updated': time.time()
            })
        
        # Update stats
        if metrics.duration_ms is not None:
            stats['count'] += 1
            stats['total_duration_ms'] += metrics.duration_ms
            stats['min_duration_ms'] = min(stats['min_duration_ms'], metrics.duration_ms)
            stats['max_duration_ms'] = max(stats['max_duration_ms'], metrics.duration_ms)
            stats['avg_duration_ms'] = stats['total_duration_ms'] / stats['count']
        
        if metrics.memory_end_mb is not None:
            stats['total_memory_mb'] += metrics.memory_end_mb
            stats['avg_memory_mb'] = stats['total_memory_mb'] / stats['count']
        
        stats['last_updated'] = time.time()
    
    def _check_performance_alerts(self, metrics: PerformanceMetrics) -> None:
        """Check for performance alerts."""
        alerts = []
        
        # Latency alert
        if (metrics.duration_ms is not None and 
            metrics.duration_ms > self.latency_alert_threshold_ms):
            alerts.append({
                'type': 'high_latency',
                'operation': metrics.operation_name,
                'value': metrics.duration_ms,
                'threshold': self.latency_alert_threshold_ms,
                'timestamp': time.time()
            })
        
        # Memory alert
        if (metrics.memory_end_mb is not None and 
            metrics.memory_end_mb > self.memory_alert_threshold_mb):
            alerts.append({
                'type': 'high_memory',
                'operation': metrics.operation_name,
                'value': metrics.memory_end_mb,
                'threshold': self.memory_alert_threshold_mb,
                'timestamp': time.time()
            })
        
        # CPU alert
        if (metrics.cpu_usage_percent is not None and 
            metrics.cpu_usage_percent > self.cpu_alert_threshold_percent):
            alerts.append({
                'type': 'high_cpu',
                'operation': metrics.operation_name,
                'value': metrics.cpu_usage_percent,
                'threshold': self.cpu_alert_threshold_percent,
                'timestamp': time.time()
            })
        
        # Store alerts and log warnings
        for alert in alerts:
            self.performance_alerts.append(alert)
            self.logger.warning(
                f"Performance alert: {alert['type']} for {alert['operation']} "
                f"({alert['value']:.2f} > {alert['threshold']})"
            )
            
            # Keep only recent alerts
            if len(self.performance_alerts) > 100:
                self.performance_alerts = self.performance_alerts[-50:]
    
    async def start_system_monitoring(self) -> None:
        """Start system-wide performance monitoring."""
        if self.system_monitor_task:
            return
            
        self.system_monitor_task = asyncio.create_task(self._system_monitor_loop())
        self.logger.info("System monitoring started")
    
    async def _system_monitor_loop(self) -> None:
        """System monitoring loop."""
        try:
            while self.monitoring_active:
                try:
                    # Collect system metrics
                    metrics = self._collect_system_metrics()
                    if metrics:
                        self.system_metrics_history.append(metrics)
                    
                    await asyncio.sleep(self.system_monitor_interval)
                    
                except Exception as e:
                    self.logger.error(f"Error in system monitoring: {e}")
                    await asyncio.sleep(self.system_monitor_interval)
                    
        except asyncio.CancelledError:
            self.logger.debug("System monitoring cancelled")
        except Exception as e:
            self.logger.error(f"System monitoring loop error: {e}")
    
    def _collect_system_metrics(self) -> Optional[SystemMetrics]:
        """Collect current system metrics."""
        try:
            # CPU and memory
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            
            # Disk I/O
            disk_io = psutil.disk_io_counters()
            disk_read_mb = 0.0
            disk_write_mb = 0.0
            if disk_io and self.last_disk_io:
                disk_read_mb = (disk_io.read_bytes - self.last_disk_io.read_bytes) / 1024 / 1024
                disk_write_mb = (disk_io.write_bytes - self.last_disk_io.write_bytes) / 1024 / 1024
            self.last_disk_io = disk_io
            
            # Network I/O
            network_io = psutil.net_io_counters()
            network_sent_mb = 0.0
            network_recv_mb = 0.0
            if network_io and self.last_network_io:
                network_sent_mb = (network_io.bytes_sent - self.last_network_io.bytes_sent) / 1024 / 1024
                network_recv_mb = (network_io.bytes_recv - self.last_network_io.bytes_recv) / 1024 / 1024
            self.last_network_io = network_io
            
            # Process-specific metrics
            process = psutil.Process()
            process_memory_mb = process.memory_info().rss / 1024 / 1024
            process_cpu_percent = process.cpu_percent()
            thread_count = process.num_threads()
            
            # Garbage collection stats
            gc_collections = {}
            for generation in range(3):
                gc_collections[generation] = gc.get_count()[generation]
            
            return SystemMetrics(
                timestamp=time.time(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_available_mb=memory.available / 1024 / 1024,
                memory_used_mb=memory.used / 1024 / 1024,
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_io_sent_mb=network_sent_mb,
                network_io_recv_mb=network_recv_mb,
                process_memory_mb=process_memory_mb,
                process_cpu_percent=process_cpu_percent,
                thread_count=thread_count,
                gc_collections=gc_collections
            )
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
            return None
    
    def get_operation_summary(self, operation_name: str) -> Dict[str, Any]:
        """Get performance summary for an operation.
        
        Args:
            operation_name: Name of the operation
            
        Returns:
            Performance summary dictionary
        """
        stats = self.operation_stats.get(operation_name, {})
        metrics_list = list(self.operation_metrics.get(operation_name, []))
        
        summary = {
            'operation_name': operation_name,
            'statistics': stats.copy(),
            'recent_metrics_count': len(metrics_list),
            'last_execution': None
        }
        
        if metrics_list:
            latest = metrics_list[-1]
            summary['last_execution'] = {
                'duration_ms': latest.duration_ms,
                'memory_mb': latest.memory_end_mb,
                'cpu_percent': latest.cpu_usage_percent,
                'timestamp': latest.start_time
            }
        
        return summary
    
    def get_all_operations_summary(self) -> Dict[str, Any]:
        """Get performance summary for all operations.
        
        Returns:
            Complete performance summary
        """
        summaries = {}
        for operation_name in self.operation_stats.keys():
            summaries[operation_name] = self.get_operation_summary(operation_name)
        
        return {
            'operations': summaries,
            'system_metrics_count': len(self.system_metrics_history),
            'alerts_count': len(self.performance_alerts),
            'memory_tracking_enabled': self.memory_tracking_enabled,
            'system_monitoring_active': self.system_monitor_task is not None and not self.system_monitor_task.done()
        }
    
    def get_performance_alerts(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent performance alerts.
        
        Args:
            limit: Maximum number of alerts to return
            
        Returns:
            List of performance alerts
        """
        return self.performance_alerts[-limit:] if self.performance_alerts else []
    
    def get_system_metrics_summary(self) -> Dict[str, Any]:
        """Get system metrics summary.
        
        Returns:
            System metrics summary
        """
        if not self.system_metrics_history:
            return {'error': 'No system metrics available'}
        
        recent_metrics = list(self.system_metrics_history)[-100:]  # Last 100 samples
        
        # Calculate averages
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_process_memory = sum(m.process_memory_mb for m in recent_metrics) / len(recent_metrics)
        avg_process_cpu = sum(m.process_cpu_percent for m in recent_metrics) / len(recent_metrics)
        
        latest = recent_metrics[-1]
        
        return {
            'current': {
                'cpu_percent': latest.cpu_percent,
                'memory_percent': latest.memory_percent,
                'memory_available_mb': latest.memory_available_mb,
                'process_memory_mb': latest.process_memory_mb,
                'process_cpu_percent': latest.process_cpu_percent,
                'thread_count': latest.thread_count,
                'timestamp': latest.timestamp
            },
            'averages': {
                'cpu_percent': avg_cpu,
                'memory_percent': avg_memory,
                'process_memory_mb': avg_process_memory,
                'process_cpu_percent': avg_process_cpu
            },
            'samples_count': len(recent_metrics),
            'monitoring_interval': self.system_monitor_interval
        }
    
    def force_garbage_collection(self) -> Dict[str, Any]:
        """Force garbage collection and return statistics.
        
        Returns:
            Garbage collection statistics
        """
        try:
            # Get pre-GC stats
            pre_gc_counts = gc.get_count()
            pre_memory = None
            
            try:
                process = psutil.Process()
                pre_memory = process.memory_info().rss / 1024 / 1024
            except Exception:
                pass
            
            # Force collection
            collected = [gc.collect(generation) for generation in range(3)]
            
            # Get post-GC stats
            post_gc_counts = gc.get_count()
            post_memory = None
            
            try:
                process = psutil.Process()
                post_memory = process.memory_info().rss / 1024 / 1024
            except Exception:
                pass
            
            return {
                'objects_collected': sum(collected),
                'collections_per_generation': collected,
                'pre_gc_counts': pre_gc_counts,
                'post_gc_counts': post_gc_counts,
                'memory_before_mb': pre_memory,
                'memory_after_mb': post_memory,
                'memory_freed_mb': (pre_memory - post_memory) if pre_memory and post_memory else None,
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"Error during garbage collection: {e}")
            return {'error': str(e)}
    
    def get_memory_usage_snapshot(self) -> Dict[str, Any]:
        """Get detailed memory usage snapshot.
        
        Returns:
            Memory usage information
        """
        try:
            snapshot = {}
            
            # Process memory
            try:
                process = psutil.Process()
                memory_info = process.memory_info()
                snapshot['process'] = {
                    'rss_mb': memory_info.rss / 1024 / 1024,
                    'vms_mb': memory_info.vms / 1024 / 1024,
                    'percent': process.memory_percent(),
                    'available_system_mb': psutil.virtual_memory().available / 1024 / 1024
                }
            except Exception as e:
                snapshot['process_error'] = str(e)
            
            # Tracemalloc snapshot if enabled
            if self.memory_tracking_enabled:
                try:
                    current, peak = tracemalloc.get_traced_memory()
                    snapshot['tracemalloc'] = {
                        'current_mb': current / 1024 / 1024,
                        'peak_mb': peak / 1024 / 1024
                    }
                    
                    # Top memory allocations
                    snapshot_obj = tracemalloc.take_snapshot()
                    top_stats = snapshot_obj.statistics('lineno')[:10]
                    snapshot['top_allocations'] = [
                        {
                            'filename': stat.traceback.format()[0],
                            'size_mb': stat.size / 1024 / 1024,
                            'count': stat.count
                        }
                        for stat in top_stats
                    ]
                except Exception as e:
                    snapshot['tracemalloc_error'] = str(e)
            
            # Garbage collection stats
            try:
                snapshot['garbage_collection'] = {
                    'counts': gc.get_count(),
                    'thresholds': gc.get_threshold(),
                    'disabled': gc.isenabled() == False
                }
            except Exception as e:
                snapshot['gc_error'] = str(e)
            
            snapshot['timestamp'] = time.time()
            return snapshot
            
        except Exception as e:
            self.logger.error(f"Error getting memory snapshot: {e}")
            return {'error': str(e)}


# Decorator for automatic performance profiling
def profile_performance(operation_name: Optional[str] = None):
    """Decorator for automatic performance profiling.
    
    Args:
        operation_name: Name of the operation (defaults to function name)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Try to get performance monitor from service container
            monitor = None
            if hasattr(args[0], 'get_service'):
                monitor = args[0].get_service('performance')
            
            if monitor and hasattr(monitor, 'profile'):
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                with monitor.profile(op_name):
                    return func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Async decorator variant
def profile_async_performance(operation_name: Optional[str] = None):
    """Decorator for automatic async performance profiling.
    
    Args:
        operation_name: Name of the operation (defaults to function name)
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Try to get performance monitor from service container
            monitor = None
            if hasattr(args[0], 'get_service'):
                monitor = args[0].get_service('performance')
            
            if monitor and hasattr(monitor, 'profile'):
                op_name = operation_name or f"{func.__module__}.{func.__name__}"
                with monitor.profile(op_name):
                    return await func(*args, **kwargs)
            else:
                return await func(*args, **kwargs)
        return wrapper
    return decorator 