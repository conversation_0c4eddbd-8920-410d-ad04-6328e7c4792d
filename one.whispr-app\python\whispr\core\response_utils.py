"""
Response utility functions for One.Whispr.

This module contains utility functions for creating standardized responses 
for handlers across the application.
"""

import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


def success_response(data: Any = None, message: Optional[str] = None) -> Dict[str, Any]:
    """Create a standardized success response.
    
    Args:
        data: The response data (optional)
        message: A success message (optional)
        
    Returns:
        A dictionary with the success response format
    """
    response = {
        "success": True,
        "error": None
    }
    
    if data is not None:
        response["data"] = data
    
    if message:
        response["message"] = message
    
    return response


def error_response(error: str, code: Optional[str] = None, data: Any = None) -> Dict[str, Any]:
    """Create a standardized error response.
    
    Args:
        error: The error message
        code: An error code for categorization (optional)
        data: Additional data to include in the response (optional)
        
    Returns:
        A dictionary with the error response format
    """
    response = {
        "success": False,
        "error": error,
        "data": data
    }
    
    if code:
        response["error_code"] = code
    
    return response


# Common error codes
class ErrorCodes:
    """Common error codes for standardized error responses."""
    
    # Generic errors
    UNKNOWN_ERROR = "UNKNOWN_ERROR"
    PARAMETER_ERROR = "PARAMETER_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    BAD_REQUEST = "BAD_REQUEST"
    
    # Service errors
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    SERVICE_ERROR = "SERVICE_ERROR"
    
    # Resource errors
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    RESOURCE_EXISTS = "RESOURCE_EXISTS"
    
    # Permission errors
    PERMISSION_DENIED = "PERMISSION_DENIED"
    
    # Device errors
    DEVICE_ERROR = "DEVICE_ERROR"
    DEVICE_NOT_FOUND = "DEVICE_NOT_FOUND"
    
    # Capture errors
    CAPTURE_ERROR = "CAPTURE_ERROR"
    
    # Configuration errors
    CONFIGURATION_ERROR = "CONFIGURATION_ERROR" 