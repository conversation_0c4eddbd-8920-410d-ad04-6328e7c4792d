export interface Speaker {
  id: string;
  sessionId: string;
  originalLabel: string; // e.g., "Speaker_0", "Speaker_1"
  customName?: string; // User-assigned name
  color?: string;
  confidence?: number;
}

export interface TranscriptionSegment {
  id: string;
  sessionId: string;
  speakerId?: string;
  text: string;
  startTime: number;
  endTime: number;
  confidence?: number;
}

export interface HistorySession {
  id: string;
  startTime: string;
  endTime?: string;
  duration?: number;
  
  // Context
  modeId: string;
  modelId: string;
  
  // Recording info
  audioFile?: string;
  audioLength?: number;
  
  // Transcription results (processing order)
  rawTranscription: string;
  afterTextReplacements?: string; // After text replacements
  afterAIProcessing?: string; // After AI processing
  finalText?: string;
  
  // Speaker diarization
  speakers?: Speaker[];
  segments?: TranscriptionSegment[];
  
  // Metadata
  confidence?: number;
  language?: string;
  wordCount?: number;
  
  // Actions taken
  actions: {
    wasPasted: boolean;
    wasCopied: boolean;
    wasAutoSelected: boolean;
  };
  
  // Quality metrics
  processingTime?: number;
  errorMessage?: string;
  
  // User management
  tags?: string[];
  notes?: string;
  isFavorite?: boolean;
}

export interface HistoryStats {
  id: string;
  totalSessions: number;
  totalDuration: number;
  totalWords: number;
  averageConfidence: number;
  mostUsedMode: string;
  mostUsedModel: string;
  lastUpdated: string;
} 