/**
 * Backend integration for One.Whispr.
 * 
 * This module handles launching and communication with the backend.
 */

// Re-export types
export { BackendOptions, IPCMessage } from './types';

// Re-export event emitter
export { backendEvents } from './websocket';

// Re-export request functions
export { sendRequest, sendEvent } from './requests';

// Import internal modules
import { BackendOptions } from './types';
import { startBackendProcess, cleanupBackendProcess, killExistingBackendProcesses } from './process';
import { connectToWebSocket, closeWebSocket, isWebSocketConnected, getWebSocketPort } from './websocket';
import { initializeRequestHandling, clearPendingRequests } from './requests';

// Retry configuration
const MAX_INIT_RETRIES = 3;
const RETRY_DELAY = 2000; // 2 seconds

/**
 * Initialize the backend with retry logic.
 * 
 * @param options - Options for backend process
 * @returns Promise that resolves when connected
 */
export async function initializeBackend(options: BackendOptions = {}): Promise<void> {
  let lastError: Error | null = null;
  
  for (let attempt = 1; attempt <= MAX_INIT_RETRIES; attempt++) {
    try {
      console.log(`Initializing backend (attempt ${attempt}/${MAX_INIT_RETRIES})...`);
      
      // Clean up any previous attempts
      terminateBackend();
      
      // On first attempt or after failure, kill any lingering backend processes
      if (attempt === 1 || attempt > 1) {
        console.log('Ensuring no conflicting backend processes are running...');
        await killExistingBackendProcesses();
      }
      
      // Wait a bit between retries
      if (attempt > 1) {
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
      }
      
      // Initialize request handling first
      initializeRequestHandling();
      
      // Start the backend process and get the port
      const port = await startBackendProcess(options);
      
      // Connect to the WebSocket server
      await connectToWebSocket(port);
      
      console.log(`Backend initialized successfully on attempt ${attempt}`);
      return;
    } catch (error) {
      console.error(`Error initializing backend (attempt ${attempt}/${MAX_INIT_RETRIES}):`, error);
      lastError = error instanceof Error ? error : new Error(String(error));
      
      // Clean up after failed attempt
      terminateBackend();
      
      // If this is the last attempt, throw the error
      if (attempt === MAX_INIT_RETRIES) {
        break;
      }
    }
  }
  
  // If we get here, all attempts failed
  console.error(`Failed to initialize backend after ${MAX_INIT_RETRIES} attempts`);
  throw lastError || new Error('Backend initialization failed');
}

/**
 * Terminate the backend.
 */
export function terminateBackend(): void {
  // Close WebSocket connection
  closeWebSocket();
  
  // Clean up backend process
  cleanupBackendProcess();
  
  // Clear pending requests
  clearPendingRequests();
}

/**
 * Check if connected to the backend.
 */
export function isBackendConnected(): boolean {
  return isWebSocketConnected();
}

/**
 * Get the WebSocket port.
 */
export function getBackendPort(): number | null {
  return getWebSocketPort();
}

/**
 * Check if the backend process is running.
 */
export { isBackendRunning } from './process'; 