"""
Transcription Session Manager

Handles session lifecycle, statistics tracking, and status management.
"""

import logging
import time
from typing import Dict, Any, Optional

from .transcription_types import ProcessingMode

logger = logging.getLogger(__name__)


class TranscriptionSessionManager:
    """Manages transcription session lifecycle and statistics."""
    
    def __init__(self):
        """Initialize the session manager."""
        # Session state
        self.is_processing = False
        self.current_session_id = None
        self.session_start_time = None
        
        # Processing statistics
        self.processed_chunks = 0
        self.total_processing_time = 0.0
        self.error_count = 0
        
        # Batch accumulation tracking
        self.batch_chunk_count = 0
        
        # Audio processing settings
        self.target_sample_rate = 16000
        self.min_audio_length_samples = 1600
        # No max audio length limit - Whisper can handle long audio
        
        logger.debug("TranscriptionSessionManager initialized")
    
    def start_session(self, session_id: Optional[str] = None, processing_mode: ProcessingMode = ProcessingMode.REAL_TIME) -> bool:
        """Start a new transcription session.
        
        Args:
            session_id: Optional session identifier
            processing_mode: Processing mode for this session
            
        Returns:
            True if session started successfully
        """
        try:
            if self.is_processing:
                logger.warning("TranscriptionSession already active")
                return True
            
            self.is_processing = True
            self.current_session_id = session_id or f"session_{int(time.time())}"
            self.session_start_time = time.time()
            
            # Reset statistics
            self.processed_chunks = 0
            self.total_processing_time = 0.0
            self.error_count = 0
            self.batch_chunk_count = 0
            
            logger.info(f"TranscriptionSession started (id: {self.current_session_id}, mode: {processing_mode.value})")
            return True
            
        except Exception as e:
            logger.error(f"Error starting transcription session: {e}")
            return False
    
    def stop_session(self, ended_by: str = "normal") -> Dict[str, Any]:
        """Stop the current session and return statistics.
        
        Args:
            ended_by: How the session ended ("normal", "cancelled", "end_of_stream")
            
        Returns:
            Dictionary with session statistics
        """
        try:
            if not self.is_processing:
                logger.warning("No active transcription session to stop")
                return {}
            
            self.is_processing = False
            session_duration = time.time() - self.session_start_time if self.session_start_time else 0
            
            stats = {
                "session_id": self.current_session_id,
                "session_duration_seconds": session_duration,
                "processed_chunks": self.processed_chunks,
                "total_processing_time_ms": self.total_processing_time,
                "average_processing_time_ms": (
                    self.total_processing_time / self.processed_chunks 
                    if self.processed_chunks > 0 else 0.0
                ),
                "error_count": self.error_count,
                "success_rate": (
                    (self.processed_chunks - self.error_count) / self.processed_chunks * 100
                    if self.processed_chunks > 0 else 100.0
                ),
                "accumulated_chunks": self.batch_chunk_count,
                "ended_by": ended_by,
                "end_time": time.time()
            }
            
            logger.info(f"TranscriptionSession stopped. Stats: {stats}")
            
            # Reset session state
            self.current_session_id = None
            self.session_start_time = None
            
            return stats
            
        except Exception as e:
            logger.error(f"Error stopping transcription session: {e}")
            return {}
    
    def update_chunk_stats(self, processing_time_ms: float, is_error: bool = False) -> None:
        """Update statistics for a processed chunk.
        
        Args:
            processing_time_ms: Processing time for this chunk
            is_error: Whether this chunk resulted in an error
        """
        self.processed_chunks += 1
        self.total_processing_time += processing_time_ms
        
        if is_error:
            self.error_count += 1
    
    def update_batch_stats(self, accumulated_chunks: int) -> None:
        """Update batch processing statistics.
        
        Args:
            accumulated_chunks: Number of chunks accumulated for batch processing
        """
        self.batch_chunk_count = accumulated_chunks
    
    def get_status(self, processing_mode: ProcessingMode) -> Dict[str, Any]:
        """Get current session status and statistics.
        
        Args:
            processing_mode: Current processing mode
            
        Returns:
            Dictionary with current status information
        """
        session_duration = time.time() - self.session_start_time if self.session_start_time else 0
        
        return {
            "is_processing": self.is_processing,
            "processing_mode": processing_mode.value,
            "current_session_id": self.current_session_id,
            "session_duration_seconds": session_duration,
            "processed_chunks": self.processed_chunks,
            "total_processing_time_ms": self.total_processing_time,
            "average_processing_time_ms": (
                self.total_processing_time / self.processed_chunks 
                if self.processed_chunks > 0 else 0.0
            ),
            "error_count": self.error_count,
            "success_rate": (
                (self.processed_chunks - self.error_count) / self.processed_chunks * 100
                if self.processed_chunks > 0 else 100.0
            ),
            "batch_chunk_count": self.batch_chunk_count,
            "audio_settings": {
                "target_sample_rate": self.target_sample_rate,
                "min_audio_length_samples": self.min_audio_length_samples,
                "max_audio_length": "unlimited"
            }
        }
    
    def get_performance_metrics(self) -> Dict[str, float]:
        """Get performance metrics for monitoring.
        
        Returns:
            Dictionary with performance metrics
        """
        session_duration = time.time() - self.session_start_time if self.session_start_time else 0
        
        return {
            "chunks_per_second": (
                self.processed_chunks / session_duration 
                if session_duration > 0 else 0.0
            ),
            "average_processing_time_ms": (
                self.total_processing_time / self.processed_chunks 
                if self.processed_chunks > 0 else 0.0
            ),
            "error_rate": (
                self.error_count / self.processed_chunks * 100
                if self.processed_chunks > 0 else 0.0
            ),
            "processing_efficiency": (
                (self.total_processing_time / 1000) / session_duration * 100
                if session_duration > 0 else 0.0
            )
        } 