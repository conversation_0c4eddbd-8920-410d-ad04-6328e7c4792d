-- This is a utility script to check if a specific user exists
-- Not meant to be run as a migration, but can be helpful for debugging

-- Check if the user exists in auth.users
SELECT id, email, last_sign_in_at, created_at, updated_at, is_sso_user
FROM auth.users 
WHERE email = 'ijaz<PERSON><PERSON><PERSON><EMAIL>';

-- Check if there's a profile for the user
SELECT p.id, p.full_name, p.first_name, p.last_name, p.created_at, p.updated_at
FROM auth.users u
LEFT JOIN public.profiles p ON u.id = p.id
WHERE u.email = '<EMAIL>';

-- Instructions:
-- 1. Run in Supabase SQL Editor
-- 2. If rows are returned, the user exists
-- 3. If you need to delete the user, use:
--    DELETE FROM auth.users WHERE email = '<EMAIL>';
--    (This will also delete the profile due to CASCADE constraint) 