"""
ShortcutRecorder class for recording keyboard shortcuts.

This module contains the ShortcutRecorder class which is responsible for
recording keyboard shortcuts using the keyboard library.
"""

import keyboard
import logging
from typing import Dict, Any, Set, List, Optional, Callable

from .shortcut_utils import ShortcutUtils

logger = logging.getLogger('whispr.shortcut')

class ShortcutRecorder:
    """Class for recording keyboard shortcuts."""
    
    def __init__(self, service_container=None, shortcuts=None):
        """Initialize the shortcut recorder.
        
        Args:
            service_container: Service container for callbacks
            shortcuts: Dictionary of existing shortcuts
        """
        self.service_container = service_container
        self.shortcuts = shortcuts or {}
        self.recording = False
        self.current_target = ""
        self.current_keys: Set[str] = set()
        self.max_combo: Set[str] = set()
        self._press_hook = None
        self._release_hook = None
        
        # Prevent sending duplicate updates
        self._last_sent_keys = set()
        self._update_complete = False
        
        # Callback for when recording completes
        self.on_recording_complete = None
    
    def set_service_container(self, service_container):
        """Set the service container reference.
        
        Args:
            service_container: The service container instance
        """
        self.service_container = service_container
    
    def set_shortcuts(self, shortcuts):
        """Set the shortcuts dictionary.
        
        Args:
            shortcuts: Dictionary of shortcuts
        """
        self.shortcuts = shortcuts
    
    def start_recording(self, target: str):
        """Start recording a shortcut.
        
        Args:
            target: The target action for the shortcut
        """
        # Clean up any existing state
        self._cleanup_hooks()
        
        # Reset state
        self.recording = True
        self.current_target = target
        self.current_keys.clear()
        self.max_combo.clear()
        self._last_sent_keys.clear()
        self._update_complete = False
        
        # Hook key events
        self._press_hook = keyboard.on_press(self._on_key_press, suppress=True)
        self._release_hook = keyboard.on_release(self._on_key_release, suppress=True)
    
    def stop_recording(self):
        """Stop recording shortcuts.
        
        Returns:
            List of recorded key names
        """
        if not self.recording:
            return []
            
        self.recording = False
        self._cleanup_hooks()
        
        # Get the recorded combo
        result = list(self.max_combo) if self.max_combo else []
        
        # Clear state
        self.current_keys.clear()
        self.max_combo.clear()
        self._last_sent_keys.clear()
        self._update_complete = False
        
        return result
    
    def _cleanup_hooks(self):
        """Clean up keyboard hooks."""
        if self._press_hook:
            try:
                keyboard.unhook(self._press_hook)
            except Exception as e:
                logger.debug(f"Error unhooking press key: {e}")
            self._press_hook = None
            
        if self._release_hook:
            try:
                keyboard.unhook(self._release_hook)
            except Exception as e:
                logger.debug(f"Error unhooking release key: {e}")
            self._release_hook = None
        
        # Release any potentially stuck modifier keys
        for key in ["ctrl", "shift", "alt", "windows"]:
            try:
                keyboard.release(key)
            except:
                pass
    
    def _on_key_press(self, event):
        """Handle key press events during shortcut recording.
        
        Args:
            event: The keyboard event
        """
        if not self.recording or self._update_complete:
            return
        
        # Convert key name
        key_str = ShortcutUtils.key_to_string(event.name)
        if not key_str:
            return
        
        if key_str not in self.current_keys:
            self.current_keys.add(key_str)
            if len(self.current_keys) > len(self.max_combo):
                self.max_combo = self.current_keys.copy()
            self._send_update(False)
    
    def _on_key_release(self, event):
        """Handle key release events during shortcut recording.
        
        Args:
            event: The keyboard event
        """
        if not self.recording:
            return
            
        # Convert key name
        key_str = ShortcutUtils.key_to_string(event.name)
        if not key_str:
            return
            
        # Check if the key is in our set (it should be)
        if key_str in self.current_keys:
            # First trigger an update (to highlight that keys are changing)
            self._send_update(False)
            
            # Remove the key from current keys
            self.current_keys.remove(key_str)
            
            # If all keys are released, update with complete flag
            if not self.current_keys:
                self._send_update(True)
            else:
                # Otherwise, just update without the complete flag
                self._send_update(False)
    
    def _send_update(self, is_complete: bool):
        """Send update about current state.
        
        Args:
            is_complete: Whether recording is complete
        """
        # Get a sorted list of keys for better UI presentation
        keys_to_send = ShortcutUtils.sort_keys(list(self.max_combo))
        
        # If we get the same keys again, no need to send update
        if set(keys_to_send) == self._last_sent_keys and is_complete == self._update_complete:
            return
            
        # Update tracking
        self._last_sent_keys = set(keys_to_send)
        self._update_complete = is_complete
        
        # Send update
        self._notify_shortcut_update(self.current_target, keys_to_send, is_complete)
        
        # If recording is complete, call completion callback
        if is_complete and self.on_recording_complete:
            try:
                self.on_recording_complete(self.current_target, keys_to_send)
            except Exception as e:
                logger.error(f"Error calling recording completion callback: {e}")
    
    def _notify_shortcut_update(self, target: str, keys: List[str], is_complete: bool):
        """Notify about shortcut update.
        
        Args:
            target: The target action
            keys: List of keys
            is_complete: Whether recording is complete
        """
        # Log the update
        logger.debug(f"Shortcut update: target={target}, keys={keys}, complete={is_complete}")
        
        if not self.service_container:
            return
            
        # Get IPC bridge from service container
        ipc_bridge = self.service_container.get_service("ipc")
        if not ipc_bridge:
            logger.warning("No IPC bridge available for shortcut update notification")
            return
            
        # Send shortcut update event
        # Create the event data
        event_data = {
            "type": "event",
            "event": "shortcutUpdate", 
            "data": {
                "target": target,
                "keys": keys,
                "complete": is_complete
            }
        }
        
        # Use sync send_message since we're in a background thread
        try:
            if hasattr(ipc_bridge, 'send_message_sync'):
                # Use sync send_message since we're in a thread without event loop
                ipc_bridge.send_message_sync(event_data)
            else:
                logger.warning("send_message_sync method not available on IPC bridge")
        except Exception as e:
            logger.error(f"Error sending shortcut update event: {e}")
            # Try alternative method if available
            try:
                # Queue the message for the main thread to send
                if hasattr(ipc_bridge, '_queue_message'):
                    ipc_bridge._queue_message(event_data)
                else:
                    logger.error("No available method to send shortcut update event")
            except Exception as e2:
                logger.error(f"Error with fallback message queueing: {e2}") 