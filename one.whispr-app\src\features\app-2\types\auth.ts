/**
 * Authentication Types
 * Types related to user authentication and auth state management
 */

// ============================================================================
// AUTH TYPES
// ============================================================================

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  avatarUrl?: string;
  provider: 'email' | 'google' | 'twitter';
}

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface AuthActions {
  signIn: (provider: 'email' | 'google' | 'twitter') => Promise<void>;
  signOut: () => Promise<void>;
  clearError: () => void;
}

export interface AuthContextValue {
  state: AuthState;
  actions: AuthActions;
} 