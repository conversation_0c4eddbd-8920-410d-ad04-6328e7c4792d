"""
Performance optimization utilities for transcription system.

This module provides automated performance optimization strategies,
memory management, adaptive processing, and system tuning capabilities.
"""

import gc
import threading
import time
import psutil
import numpy as np
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass
from collections import defaultdict, deque
import logging
from pathlib import Path
import pickle
import weakref
import sys
from functools import lru_cache, wraps
import asyncio
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

from .performance_profiler import PerformanceProfiler, get_profiler

logger = logging.getLogger(__name__)


@dataclass
class OptimizationConfig:
    """Configuration for performance optimizations."""
    enable_memory_optimization: bool = True
    enable_adaptive_processing: bool = True
    enable_caching: bool = True
    enable_parallel_processing: bool = True
    cache_size_limit: int = 1000
    memory_threshold_mb: int = 512
    cpu_threshold_percent: float = 80.0
    adaptive_batch_size: bool = True
    max_workers: int = None


@dataclass
class SystemConstraints:
    """Current system constraints and capabilities."""
    available_memory_mb: float
    cpu_cores: int
    cpu_percent: float
    memory_percent: float
    gpu_available: bool = False
    gpu_memory_mb: Optional[float] = None
    processing_power_score: float = 1.0


class MemoryManager:
    """Advanced memory management for transcription system."""
    
    def __init__(self, max_memory_mb: int = 1024):
        self.max_memory_mb = max_memory_mb
        self.memory_pools: Dict[str, List[Any]] = defaultdict(list)
        self.weak_refs: Dict[str, List[weakref.ref]] = defaultdict(list)
        self.memory_monitor_active = False
        self.memory_monitor_thread: Optional[threading.Thread] = None
        self._lock = threading.Lock()
        
    def start_monitoring(self, check_interval: float = 5.0):
        """Start memory monitoring in background thread."""
        if self.memory_monitor_active:
            return
            
        self.memory_monitor_active = True
        self.memory_monitor_thread = threading.Thread(
            target=self._memory_monitor_loop,
            args=(check_interval,),
            daemon=True
        )
        self.memory_monitor_thread.start()
        logger.info("Memory monitoring started")
        
    def stop_monitoring(self):
        """Stop memory monitoring."""
        self.memory_monitor_active = False
        if self.memory_monitor_thread:
            self.memory_monitor_thread.join(timeout=5.0)
        logger.info("Memory monitoring stopped")
        
    def _memory_monitor_loop(self, check_interval: float):
        """Background memory monitoring loop."""
        while self.memory_monitor_active:
            try:
                self._check_memory_pressure()
                time.sleep(check_interval)
            except Exception as e:
                logger.error(f"Error in memory monitoring: {e}")
                time.sleep(1.0)
                
    def _check_memory_pressure(self):
        """Check for memory pressure and trigger cleanup if needed."""
        memory_info = psutil.virtual_memory()
        memory_mb = memory_info.used / (1024 * 1024)
        
        if memory_mb > self.max_memory_mb or memory_info.percent > 85:
            logger.warning(f"Memory pressure detected: {memory_mb:.1f}MB ({memory_info.percent:.1f}%)")
            self.cleanup_memory()
            
    def register_object(self, obj: Any, category: str = "general") -> weakref.ref:
        """Register an object for memory management."""
        with self._lock:
            weak_ref = weakref.ref(obj, lambda ref: self._cleanup_weak_ref(ref, category))
            self.weak_refs[category].append(weak_ref)
            return weak_ref
            
    def _cleanup_weak_ref(self, ref: weakref.ref, category: str):
        """Clean up weak reference when object is garbage collected."""
        with self._lock:
            if ref in self.weak_refs[category]:
                self.weak_refs[category].remove(ref)
                
    def cleanup_memory(self, category: Optional[str] = None):
        """Force memory cleanup for specified category or all categories."""
        with self._lock:
            if category:
                categories = [category]
            else:
                categories = list(self.weak_refs.keys())
                
            for cat in categories:
                # Clean up dead weak references
                alive_refs = []
                for ref in self.weak_refs[cat]:
                    if ref() is not None:
                        alive_refs.append(ref)
                self.weak_refs[cat] = alive_refs
                
                # Clear memory pools
                if cat in self.memory_pools:
                    self.memory_pools[cat].clear()
                    
        # Force garbage collection
        gc.collect()
        
        # Log memory status
        memory_info = psutil.virtual_memory()
        logger.info(f"Memory cleanup completed. Current usage: {memory_info.percent:.1f}%")
        
    def get_memory_pool(self, category: str, factory: Callable[[], Any], max_size: int = 100) -> Any:
        """Get object from memory pool or create new one."""
        with self._lock:
            pool = self.memory_pools[category]
            
            if pool:
                return pool.pop()
            else:
                obj = factory()
                # Keep pool size manageable
                if len(pool) < max_size:
                    self.register_object(obj, category)
                return obj
                
    def return_to_pool(self, obj: Any, category: str):
        """Return object to memory pool for reuse."""
        with self._lock:
            pool = self.memory_pools[category]
            pool.append(obj)
            
    def get_memory_stats(self) -> Dict[str, Any]:
        """Get current memory statistics."""
        with self._lock:
            stats = {
                "categories": {},
                "total_weak_refs": 0,
                "pool_sizes": {}
            }
            
            for category, refs in self.weak_refs.items():
                alive_count = sum(1 for ref in refs if ref() is not None)
                stats["categories"][category] = alive_count
                stats["total_weak_refs"] += alive_count
                stats["pool_sizes"][category] = len(self.memory_pools[category])
                
            # System memory stats
            memory_info = psutil.virtual_memory()
            stats["system_memory_percent"] = memory_info.percent
            stats["system_memory_mb"] = memory_info.used / (1024 * 1024)
            
            return stats


class AdaptiveProcessor:
    """Adaptive processing that adjusts performance based on system load."""
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.current_batch_size = 1
        self.processing_times: deque = deque(maxlen=100)
        self.system_load_history: deque = deque(maxlen=50)
        self.adaptation_enabled = config.enable_adaptive_processing
        
    def get_optimal_batch_size(self, task_complexity: float = 1.0) -> int:
        """Calculate optimal batch size based on current system state."""
        if not self.adaptation_enabled:
            return self.current_batch_size
            
        # Get current system load
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        
        # Calculate system load score (0.0 = low load, 1.0 = high load)
        load_score = (cpu_percent / 100.0 + memory_percent / 100.0) / 2.0
        
        # Adjust batch size based on load and recent performance
        if load_score > 0.8:  # High load
            self.current_batch_size = max(1, self.current_batch_size - 1)
        elif load_score < 0.5 and self.processing_times:  # Low load and good performance
            avg_time = sum(self.processing_times) / len(self.processing_times)
            if avg_time < 0.1:  # Fast processing
                self.current_batch_size = min(20, self.current_batch_size + 1)
                
        # Adjust for task complexity
        adjusted_batch_size = max(1, int(self.current_batch_size / task_complexity))
        
        return adjusted_batch_size
        
    def record_processing_time(self, duration: float):
        """Record processing time for adaptive optimization."""
        self.processing_times.append(duration)
        
    def get_processing_strategy(self, task_count: int) -> Dict[str, Any]:
        """Get optimal processing strategy for given task count."""
        system_constraints = self._get_system_constraints()
        
        if task_count == 1:
            return {"strategy": "sequential", "batch_size": 1, "workers": 1}
        
        # Determine if parallel processing would be beneficial
        if (system_constraints.cpu_cores > 2 and 
            system_constraints.cpu_percent < self.config.cpu_threshold_percent and
            task_count > 2):
            
            max_workers = min(
                self.config.max_workers or system_constraints.cpu_cores,
                task_count,
                system_constraints.cpu_cores
            )
            
            return {
                "strategy": "parallel",
                "batch_size": self.get_optimal_batch_size(),
                "workers": max_workers
            }
        else:
            return {
                "strategy": "sequential",
                "batch_size": self.get_optimal_batch_size(),
                "workers": 1
            }
            
    def _get_system_constraints(self) -> SystemConstraints:
        """Get current system constraints."""
        memory_info = psutil.virtual_memory()
        
        # Try to detect GPU
        gpu_available = False
        gpu_memory_mb = None
        try:
            import pynvml
            pynvml.nvmlInit()
            gpu_available = True
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            memory_info_gpu = pynvml.nvmlDeviceGetMemoryInfo(handle)
            gpu_memory_mb = memory_info_gpu.total / (1024 * 1024)
        except:
            pass
            
        return SystemConstraints(
            available_memory_mb=memory_info.available / (1024 * 1024),
            cpu_cores=psutil.cpu_count(),
            cpu_percent=psutil.cpu_percent(),
            memory_percent=memory_info.percent,
            gpu_available=gpu_available,
            gpu_memory_mb=gpu_memory_mb
        )


class SmartCache:
    """Intelligent caching system with automatic eviction and optimization."""
    
    def __init__(self, max_size: int = 1000, max_memory_mb: int = 100):
        self.max_size = max_size
        self.max_memory_mb = max_memory_mb
        self.cache: Dict[str, Any] = {}
        self.access_times: Dict[str, float] = {}
        self.access_counts: Dict[str, int] = defaultdict(int)
        self.memory_usage: Dict[str, int] = {}
        self._lock = threading.RLock()
        
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache."""
        with self._lock:
            if key in self.cache:
                self.access_times[key] = time.time()
                self.access_counts[key] += 1
                return self.cache[key]
            return None
            
    def put(self, key: str, value: Any, memory_size: Optional[int] = None):
        """Put item in cache with automatic eviction."""
        with self._lock:
            # Estimate memory size if not provided
            if memory_size is None:
                memory_size = self._estimate_memory_size(value)
                
            # Check if we need to evict items
            self._evict_if_needed(memory_size)
            
            # Add to cache
            self.cache[key] = value
            self.access_times[key] = time.time()
            self.access_counts[key] = 1
            self.memory_usage[key] = memory_size
            
    def _estimate_memory_size(self, obj: Any) -> int:
        """Estimate memory size of object in bytes."""
        try:
            if isinstance(obj, (str, bytes)):
                return len(obj)
            elif isinstance(obj, (list, tuple)):
                return sum(self._estimate_memory_size(item) for item in obj)
            elif isinstance(obj, dict):
                return sum(self._estimate_memory_size(k) + self._estimate_memory_size(v) 
                          for k, v in obj.items())
            elif isinstance(obj, np.ndarray):
                return obj.nbytes
            else:
                return sys.getsizeof(obj)
        except:
            return 1024  # Default estimate
            
    def _evict_if_needed(self, incoming_size: int):
        """Evict items if cache is too full."""
        # Check size limit
        if len(self.cache) >= self.max_size:
            self._evict_lru(1)
            
        # Check memory limit
        current_memory = sum(self.memory_usage.values()) + incoming_size
        if current_memory > self.max_memory_mb * 1024 * 1024:
            # Evict items until under memory limit
            target_memory = self.max_memory_mb * 1024 * 1024 * 0.8  # 80% of limit
            while (sum(self.memory_usage.values()) > target_memory and 
                   len(self.cache) > 0):
                self._evict_lru(1)
                
    def _evict_lru(self, count: int):
        """Evict least recently used items."""
        if not self.cache:
            return
            
        # Sort by access time (oldest first)
        sorted_items = sorted(
            self.access_times.items(),
            key=lambda x: x[1]
        )
        
        for i in range(min(count, len(sorted_items))):
            key = sorted_items[i][0]
            self._remove_item(key)
            
    def _remove_item(self, key: str):
        """Remove item from cache."""
        if key in self.cache:
            del self.cache[key]
            del self.access_times[key]
            del self.access_counts[key]
            del self.memory_usage[key]
            
    def clear(self):
        """Clear all cache entries."""
        with self._lock:
            self.cache.clear()
            self.access_times.clear()
            self.access_counts.clear()
            self.memory_usage.clear()
            
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_memory = sum(self.memory_usage.values())
            return {
                "size": len(self.cache),
                "max_size": self.max_size,
                "memory_usage_mb": total_memory / (1024 * 1024),
                "max_memory_mb": self.max_memory_mb,
                "utilization": len(self.cache) / self.max_size,
                "memory_utilization": total_memory / (self.max_memory_mb * 1024 * 1024)
            }


class PerformanceOptimizer:
    """
    Main performance optimizer that coordinates all optimization strategies.
    
    Features:
    - Automatic memory management and cleanup
    - Adaptive processing based on system load
    - Intelligent caching with eviction policies
    - Performance monitoring and tuning recommendations
    - GPU acceleration detection and utilization
    """
    
    def __init__(self, config: OptimizationConfig = None):
        self.config = config or OptimizationConfig()
        self.profiler = get_profiler()
        
        # Initialize optimization components
        self.memory_manager = MemoryManager(self.config.memory_threshold_mb)
        self.adaptive_processor = AdaptiveProcessor(self.config)
        self.cache = SmartCache(
            max_size=self.config.cache_size_limit,
            max_memory_mb=self.config.memory_threshold_mb // 4
        )
        
        # Optimization state
        self.optimizations_applied: List[str] = []
        self.performance_baseline: Optional[Dict[str, float]] = None
        self.is_monitoring = False
        
    def start_optimization(self):
        """Start all optimization services."""
        if self.config.enable_memory_optimization:
            self.memory_manager.start_monitoring()
            
        self.profiler.start_system_monitoring()
        self.is_monitoring = True
        
        logger.info("Performance optimization started")
        
    def stop_optimization(self):
        """Stop all optimization services."""
        if self.config.enable_memory_optimization:
            self.memory_manager.stop_monitoring()
            
        self.profiler.stop_system_monitoring()
        self.is_monitoring = False
        
        logger.info("Performance optimization stopped")
        
    def optimize_function(self, cache_key: Optional[str] = None):
        """
        Decorator to optimize function performance.
        
        Usage:
            @optimizer.optimize_function("my_function_cache")
            def my_function(arg1, arg2):
                return expensive_computation(arg1, arg2)
        """
        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Generate cache key if caching enabled
                if self.config.enable_caching and cache_key:
                    full_cache_key = f"{cache_key}_{hash(str(args) + str(kwargs))}"
                    cached_result = self.cache.get(full_cache_key)
                    if cached_result is not None:
                        return cached_result
                
                # Profile and execute function
                with self.profiler.profile_operation(func.__name__):
                    start_time = time.time()
                    result = func(*args, **kwargs)
                    duration = time.time() - start_time
                    
                    # Record performance for adaptive processing
                    self.adaptive_processor.record_processing_time(duration)
                    
                    # Cache result if caching enabled
                    if self.config.enable_caching and cache_key:
                        self.cache.put(full_cache_key, result)
                    
                    return result
                    
            return wrapper
        return decorator
        
    def optimize_batch_processing(self, items: List[Any], 
                                processor: Callable[[Any], Any]) -> List[Any]:
        """
        Optimize batch processing with adaptive strategies.
        
        Args:
            items: List of items to process
            processor: Function to process each item
            
        Returns:
            List of processed results
        """
        if not items:
            return []
            
        strategy = self.adaptive_processor.get_processing_strategy(len(items))
        
        if strategy["strategy"] == "parallel" and self.config.enable_parallel_processing:
            return self._process_parallel(items, processor, strategy)
        else:
            return self._process_sequential(items, processor, strategy)
            
    def _process_sequential(self, items: List[Any], 
                          processor: Callable[[Any], Any],
                          strategy: Dict[str, Any]) -> List[Any]:
        """Process items sequentially with adaptive batching."""
        results = []
        batch_size = strategy["batch_size"]
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            start_time = time.time()
            batch_results = [processor(item) for item in batch]
            duration = time.time() - start_time
            
            results.extend(batch_results)
            self.adaptive_processor.record_processing_time(duration / len(batch))
            
        return results
        
    def _process_parallel(self, items: List[Any],
                         processor: Callable[[Any], Any],
                         strategy: Dict[str, Any]) -> List[Any]:
        """Process items in parallel with thread pool."""
        results = [None] * len(items)
        workers = strategy["workers"]
        
        with ThreadPoolExecutor(max_workers=workers) as executor:
            start_time = time.time()
            
            # Submit all tasks
            future_to_index = {
                executor.submit(processor, item): i
                for i, item in enumerate(items)
            }
            
            # Collect results
            for future in future_to_index:
                index = future_to_index[future]
                try:
                    results[index] = future.result()
                except Exception as e:
                    logger.error(f"Error processing item {index}: {e}")
                    results[index] = None
                    
            duration = time.time() - start_time
            self.adaptive_processor.record_processing_time(duration / len(items))
            
        return results
        
    def optimize_memory_usage(self, category: str = "general") -> Dict[str, Any]:
        """
        Optimize memory usage and return statistics.
        
        Args:
            category: Memory category to optimize
            
        Returns:
            Memory optimization statistics
        """
        # Get memory stats before optimization
        before_stats = self.memory_manager.get_memory_stats()
        
        # Perform memory cleanup
        self.memory_manager.cleanup_memory(category)
        
        # Clear cache if memory pressure is high
        if before_stats["system_memory_percent"] > 85:
            self.cache.clear()
            
        # Force garbage collection
        gc.collect()
        
        # Get memory stats after optimization
        after_stats = self.memory_manager.get_memory_stats()
        
        optimization_result = {
            "before": before_stats,
            "after": after_stats,
            "memory_freed_mb": (before_stats["system_memory_mb"] - 
                              after_stats["system_memory_mb"]),
            "percent_reduction": ((before_stats["system_memory_percent"] - 
                                 after_stats["system_memory_percent"]) / 
                                before_stats["system_memory_percent"] * 100
                                if before_stats["system_memory_percent"] > 0 else 0)
        }
        
        return optimization_result
        
    def get_optimization_recommendations(self) -> List[Dict[str, Any]]:
        """Get automated optimization recommendations."""
        recommendations = []
        
        # Get current system state
        constraints = self.adaptive_processor._get_system_constraints()
        performance_report = self.profiler.generate_performance_report()
        
        # Memory recommendations
        if constraints.memory_percent > 80:
            recommendations.append({
                "type": "memory",
                "priority": "high",
                "issue": f"High memory usage: {constraints.memory_percent:.1f}%",
                "recommendation": "Enable memory optimization and increase cleanup frequency",
                "action": "optimize_memory_usage"
            })
            
        # CPU recommendations
        if constraints.cpu_percent > 80:
            recommendations.append({
                "type": "cpu",
                "priority": "high",
                "issue": f"High CPU usage: {constraints.cpu_percent:.1f}%",
                "recommendation": "Reduce batch sizes and enable adaptive processing",
                "action": "reduce_processing_load"
            })
            
        # Cache recommendations
        cache_stats = self.cache.get_stats()
        if cache_stats["utilization"] < 0.3:
            recommendations.append({
                "type": "cache",
                "priority": "medium",
                "issue": f"Low cache utilization: {cache_stats['utilization']:.1%}",
                "recommendation": "Increase cache size or improve caching strategy",
                "action": "optimize_caching"
            })
            
        # Parallel processing recommendations
        if (constraints.cpu_cores > 2 and 
            constraints.cpu_percent < 50 and 
            not self.config.enable_parallel_processing):
            recommendations.append({
                "type": "parallelization",
                "priority": "medium",
                "issue": "Underutilized CPU cores available",
                "recommendation": "Enable parallel processing for better performance",
                "action": "enable_parallel_processing"
            })
            
        # GPU recommendations
        if constraints.gpu_available and not self._is_gpu_utilized():
            recommendations.append({
                "type": "gpu",
                "priority": "low",
                "issue": "GPU available but not utilized",
                "recommendation": "Consider GPU acceleration for supported operations",
                "action": "investigate_gpu_acceleration"
            })
            
        return recommendations
        
    def _is_gpu_utilized(self) -> bool:
        """Check if GPU is being utilized."""
        try:
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
            return utilization.gpu > 10  # More than 10% utilization
        except:
            return False
            
    def apply_auto_optimizations(self) -> Dict[str, Any]:
        """Apply automatic optimizations based on current system state."""
        applied_optimizations = []
        
        recommendations = self.get_optimization_recommendations()
        
        for rec in recommendations:
            if rec["priority"] == "high":
                if rec["action"] == "optimize_memory_usage":
                    result = self.optimize_memory_usage()
                    applied_optimizations.append({
                        "optimization": "memory_cleanup",
                        "result": result
                    })
                    
                elif rec["action"] == "reduce_processing_load":
                    # Reduce batch sizes
                    self.adaptive_processor.current_batch_size = max(
                        1, self.adaptive_processor.current_batch_size // 2
                    )
                    applied_optimizations.append({
                        "optimization": "reduced_batch_size",
                        "new_batch_size": self.adaptive_processor.current_batch_size
                    })
                    
        return {
            "optimizations_applied": applied_optimizations,
            "recommendations": recommendations
        }
        
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary."""
        # System constraints
        constraints = self.adaptive_processor._get_system_constraints()
        
        # Performance metrics
        performance_report = self.profiler.generate_performance_report()
        
        # Memory stats
        memory_stats = self.memory_manager.get_memory_stats()
        
        # Cache stats
        cache_stats = self.cache.get_stats()
        
        # Recommendations
        recommendations = self.get_optimization_recommendations()
        
        return {
            "timestamp": time.time(),
            "system_constraints": {
                "memory_percent": constraints.memory_percent,
                "cpu_percent": constraints.cpu_percent,
                "cpu_cores": constraints.cpu_cores,
                "gpu_available": constraints.gpu_available
            },
            "memory_management": memory_stats,
            "cache_performance": cache_stats,
            "performance_metrics": performance_report,
            "recommendations": recommendations,
            "optimization_config": {
                "memory_optimization": self.config.enable_memory_optimization,
                "adaptive_processing": self.config.enable_adaptive_processing,
                "caching": self.config.enable_caching,
                "parallel_processing": self.config.enable_parallel_processing
            }
        }


# Global optimizer instance
_global_optimizer: Optional[PerformanceOptimizer] = None


def get_optimizer(config: Optional[OptimizationConfig] = None) -> PerformanceOptimizer:
    """Get or create global optimizer instance."""
    global _global_optimizer
    if _global_optimizer is None:
        _global_optimizer = PerformanceOptimizer(config)
    return _global_optimizer


def optimize_function(cache_key: Optional[str] = None):
    """Convenience decorator for function optimization."""
    return get_optimizer().optimize_function(cache_key)


def optimize_batch_processing(items: List[Any], processor: Callable[[Any], Any]) -> List[Any]:
    """Convenience function for batch processing optimization."""
    return get_optimizer().optimize_batch_processing(items, processor)


def optimize_memory():
    """Convenience function for memory optimization."""
    return get_optimizer().optimize_memory_usage()


def get_performance_recommendations():
    """Convenience function to get performance recommendations."""
    return get_optimizer().get_optimization_recommendations()


# Decorators for common optimization patterns
def cached(cache_key: str):
    """Simple caching decorator."""
    def decorator(func):
        return get_optimizer().optimize_function(cache_key)(func)
    return decorator


def adaptive_batch(func):
    """Decorator for adaptive batch processing."""
    @wraps(func)
    def wrapper(items, *args, **kwargs):
        processor = lambda item: func(item, *args, **kwargs)
        return optimize_batch_processing(items, processor)
    return wrapper


def memory_managed(category: str = "general"):
    """Decorator for memory-managed functions."""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            optimizer = get_optimizer()
            
            # Register function execution
            with optimizer.profiler.profile_operation(f"memory_managed_{func.__name__}"):
                result = func(*args, **kwargs)
                
                # Check if memory optimization is needed
                memory_stats = optimizer.memory_manager.get_memory_stats()
                if memory_stats["system_memory_percent"] > 85:
                    optimizer.optimize_memory_usage(category)
                    
                return result
        return wrapper
    return decorator 