import { mkdirSync, existsSync, readFileSync, writeFileSync } from 'fs';
import { pipeline } from 'stream/promises';
import { extract } from 'tar';
import { createGunzip } from 'zlib';
import { Readable } from 'stream';
import path from 'path';
import { Octokit } from '@octokit/rest';
import ora from 'ora';
import * as fs from 'fs';
import {
  getPaths,
  runCommand,
  getInstalledPackages,
  parsePackageSpec,
  getTorchRequirements,
  REQUIREMENTS,
  REPO_INFO,
  PLATFORM_ASSETS,
  preserveSitePackages,
  restoreSitePackages,
  installPackagesWithUV,
  updatePackageManager
} from './backend-utils';

// Helper function to check if a package is installed with correct version
function isPackageInstalled(packageName: string, installedPackages: Map<string, string>): boolean {
  const { name, version, specifier } = parsePackageSpec(packageName);
  
  // Try multiple variations of the package name
  const variations = [
    name,
    name.replace(/[-_]/g, '-'),
    name.replace(/[-_]/g, '_'),
    name.toLowerCase(),
    name.toLowerCase().replace(/[-_]/g, '-'),
    name.toLowerCase().replace(/[-_]/g, '_')
  ];
  
  for (const variation of variations) {
    if (installedPackages.has(variation)) {
      const installedVersion = installedPackages.get(variation);
      
      // If we have a version requirement, check it matches
      if (version && specifier && installedVersion) {
        if (specifier === '==' && installedVersion !== version) {
          return false; // Version mismatch
        }
        // Add more version checks if needed (>=, >, <, etc.)
      }
      
      return true;
    }
  }
  
  return false;
}

// Downloads standalone Python distribution
async function downloadPython() {
  const spinner = ora('Preparing to download Python...').start();
  const { pythonDir, pythonExePath } = getPaths();
  
  try {
    // Check if Python is already installed
    if (existsSync(pythonExePath)) {
      // Verify Python works by running a simple command
      try {
        await runCommand(pythonExePath, ['--version']);
        spinner.succeed('✅ Python is already installed and working');
        return;
      } catch (error) {
        spinner.warn('⚠️ Python installation found but not working properly. Reinstalling...');
        // Continue with installation
      }
    }
    
    // Create directory if it doesn't exist
    if (!existsSync(pythonDir)) {
      mkdirSync(pythonDir, { recursive: true });
    } else {
      // Clean up existing directory to avoid conflicts
      spinner.text = 'Cleaning up existing Python directory...';
      try {
        // Simplified site-packages preservation
        const sitePackagesPath = getPaths().sitePackagesPath;
        const tempSitePackagesDir = preserveSitePackages(sitePackagesPath);
        
        // Clean Python directory
        fs.rmSync(pythonDir, { recursive: true, force: true });
        fs.mkdirSync(pythonDir, { recursive: true });
        
        // Restore site-packages
        restoreSitePackages(tempSitePackagesDir, sitePackagesPath);
      } catch (error: any) {
        spinner.warn(`⚠️ Could not clean up Python directory: ${error.message}`);
        // Continue with installation
      }
    }
    
    const platform = process.platform as 'win32' | 'darwin' | 'linux';
    const arch = process.arch as 'x64' | 'arm64';
    
    const assetName = PLATFORM_ASSETS[platform]?.[arch];
    if (!assetName) {
      spinner.fail(`❌ Unsupported platform: ${platform}-${arch}`);
      throw new Error(`Unsupported platform: ${platform}-${arch}`);
    }

    spinner.text = `🔍 Downloading Python for ${platform}-${arch}...`;
    
    const octokit = new Octokit();
    
    // Get release by tag
    const { data: release } = await octokit.repos.getReleaseByTag({
      ...REPO_INFO,
      tag: REPO_INFO.tag
    });

    // Find matching asset
    const asset = release.assets.find(asset => asset.name === assetName);
    if (!asset) {
      spinner.fail(`❌ Could not find asset: ${assetName}`);
      throw new Error(`Could not find asset: ${assetName}`);
    }

    spinner.text = `📥 Downloading ${asset.name} (${Math.round(asset.size / 1024 / 1024)}MB)...`;

    // Download asset
    const response = await octokit.request({
      url: asset.browser_download_url,
      headers: {
        'accept': 'application/octet-stream'
      },
      responseType: 'arraybuffer'
    });

    spinner.text = '📦 Extracting Python...';
    // Create a readable stream from the buffer
    const bufferStream = Readable.from(Buffer.from(response.data));
    
    await pipeline(
      bufferStream,
      createGunzip(),
      extract({ cwd: pythonDir, strip: 1 }) as unknown as NodeJS.WritableStream
    );

    // Verify installation
    if (existsSync(pythonExePath)) {
      try {
        const version = await runCommand(pythonExePath, ['--version']);
        spinner.succeed(`✅ Python installation complete! ${version.trim()}`);
      } catch (error) {
        spinner.succeed('✅ Python installation complete!');
      }
    } else {
      spinner.fail(`❌ Python executable not found at ${pythonExePath}`);
      throw new Error(`Python executable not found at ${pythonExePath}`);
    }
  } catch (error: any) {
    spinner.fail(`❌ Python installation failed: ${error.message}`);
    throw error;
  }
}

// Check if PyTorch is already installed with CUDA support
async function checkTorchInstallation(pythonExePath: string, sitePackagesPath: string): Promise<boolean> {
  try {
    const result = await runCommand(pythonExePath, [
      '-c', 
      'import torch; print(f"torch={torch.__version__}"); print(f"cuda={torch.cuda.is_available()}")'
    ], {
      env: {
        ...process.env,
        PYTHONPATH: sitePackagesPath
      }
    });
    
    const lines = result.trim().split('\n');
    const torchVersion = lines.find(line => line.startsWith('torch='))?.split('=')[1];
    const cudaAvailable = lines.find(line => line.startsWith('cuda='))?.split('=')[1] === 'True';
    
    return Boolean(torchVersion && cudaAvailable);
  } catch (error) {
    return false;
  }
}

// Install torch with CUDA support
async function installTorchWithCuda(pythonExePath: string, sitePackagesPath: string, spinner: ora.Ora, force: boolean = false) {
  // Check if PyTorch with CUDA is already installed
  if (!force) {
    spinner.text = '🔥 Checking existing PyTorch installation...';
    const torchInstalled = await checkTorchInstallation(pythonExePath, sitePackagesPath);
    
    if (torchInstalled) {
      spinner.succeed('✅ PyTorch with CUDA support already installed');
      return;
    }
  }
  
  try {
    const torchReqs = getTorchRequirements();
    spinner.text = '🔥 Installing PyTorch with CUDA support using UV (much faster!)...';

    // Stop spinner for better output visibility
    spinner.stop();
    console.log('🔥 Starting PyTorch CUDA installation with UV...');
    console.log('📥 This is a large download (~2-3GB), but UV will be much faster...');

    await installPackagesWithUV(pythonExePath, torchReqs.packages, sitePackagesPath, {
      indexUrl: torchReqs.indexUrl,
      upgrade: true,
      noCache: true,
      timeout: 300 // 5 minute timeout
    });

    // Restart spinner
    spinner.start();
    spinner.succeed('✅ PyTorch with CUDA support installed successfully with UV');
  } catch (error: any) {
    // Restart spinner for error handling
    spinner.start();
    spinner.warn(`⚠️ Failed to install PyTorch with CUDA: ${error.message}`);
    
    // Fall back to CPU version
    spinner.text = '📦 Installing PyTorch CPU version as fallback with UV...';
    try {
      await installPackagesWithUV(pythonExePath, ['torch', 'torchvision', 'torchaudio'], sitePackagesPath, {
        upgrade: true,
        noCache: true,
        timeout: 120 // 2 minute timeout for CPU version
      });
      spinner.succeed('✅ PyTorch CPU version installed as fallback with UV');
    } catch (fallbackError: any) {
      spinner.warn(`⚠️ Failed to install PyTorch fallback: ${fallbackError.message}`);
    }
  }
}

// Installs Python dependencies
async function installDependencies() {
  const spinner = ora('🔍 Checking dependencies...').start();
  const { pythonExePath, sitePackagesPath, projectRoot } = getPaths();
  
  try {
    // Ensure Python is installed
    if (!existsSync(pythonExePath)) {
      spinner.fail(`❌ Python not found at ${pythonExePath}. Please run setup first.`);
      throw new Error(`Python not found at ${pythonExePath}`);
    }
    
    // Ensure site-packages directory exists
    if (!existsSync(sitePackagesPath)) {
      fs.mkdirSync(sitePackagesPath, { recursive: true });
    }
    
    // Update package manager (UV or pip)
    spinner.text = '🔄 Setting up package manager (UV/pip)...';
    await updatePackageManager(pythonExePath);
    
    // Get requirements
    const requirements = REQUIREMENTS;
    if (requirements.length === 0) {
      spinner.succeed('✅ No dependencies to install');
      return;
    }
    
    // Get currently installed packages
    spinner.text = '🔍 Checking installed packages...';
    const installedPackages = await getInstalledPackages(pythonExePath, sitePackagesPath);
    
    // Determine which packages need to be installed or updated
    const packagesToInstall: string[] = [];
    const alreadyInstalled: string[] = [];
    
    for (const req of requirements) {
      if (!isPackageInstalled(req, installedPackages)) {
        packagesToInstall.push(req);
      } else {
        alreadyInstalled.push(req);
      }
    }
    
    // Show what's already installed
    if (alreadyInstalled.length > 0) {
      spinner.info(`✅ Already installed: ${alreadyInstalled.length} packages (${alreadyInstalled.slice(0, 3).map(pkg => parsePackageSpec(pkg).name).join(', ')}${alreadyInstalled.length > 3 ? '...' : ''})`);
    }
    
    if (packagesToInstall.length === 0) {
      spinner.succeed(`✅ All ${requirements.length} packages already installed`);
      
      // Still need to check/install PyTorch with CUDA support
      spinner.start('🔥 Checking PyTorch with CUDA installation...');
      await installTorchWithCuda(pythonExePath, sitePackagesPath, spinner, false);
    } else {
      spinner.text = `📦 Installing ${packagesToInstall.length} packages with UV (faster)...`;

      try {
        await installPackagesWithUV(pythonExePath, packagesToInstall, sitePackagesPath, {
          upgrade: true,
          noCache: true
        });

        spinner.succeed(`✅ Successfully installed ${packagesToInstall.length} packages with UV`);
        
        // Install torch with CUDA support separately
        spinner.start('🔥 Checking PyTorch with CUDA installation...');
        await installTorchWithCuda(pythonExePath, sitePackagesPath, spinner, false);
      } catch (error: any) {
        spinner.info(`ℹ️ Bulk installation not possible, switching to individual package installation...`);
        
        // Fall back to individual installation with UV
        spinner.text = '📦 Installing packages individually with UV...';

        const failedPackages: string[] = [];
        let successCount = 0;

        for (const req of packagesToInstall) {
          spinner.text = `📦 Installing ${req} (${successCount + 1}/${packagesToInstall.length})...`;
          try {
            await installPackagesWithUV(pythonExePath, [req], sitePackagesPath, {
              upgrade: true,
              noCache: true
            });
            successCount++;
          } catch (error: any) {
            spinner.warn(`⚠️ Failed to install ${req}: ${error.message}`);
            failedPackages.push(req);
          }
        }
        
        if (failedPackages.length === 0) {
          spinner.succeed(`✅ Successfully installed all ${packagesToInstall.length} packages individually`);
        } else {
          spinner.warn(`⚠️ Installed ${successCount}/${packagesToInstall.length} packages. Failed: ${failedPackages.join(', ')}`);
        }
        
        // Install torch with CUDA support separately
        spinner.start('🔥 Checking PyTorch with CUDA installation...');
        await installTorchWithCuda(pythonExePath, sitePackagesPath, spinner, false);
      }
    }
    
    // Add the python directory to Python path
    spinner.text = '🔧 Setting up Python path...';
    const pthPath = path.join(sitePackagesPath, 'project.pth');
    const srcPath = path.join(projectRoot, 'python');
    
    // Only create the .pth file if it doesn't exist or has changed
    if (!existsSync(pthPath) || readFileSync(pthPath, 'utf8') !== srcPath) {
      writeFileSync(pthPath, srcPath);
    }
    
    spinner.succeed('✅ Python environment setup complete!');
  } catch (error: any) {
    spinner.fail(`❌ Dependency installation failed: ${error.message}`);
    throw error;
  }
}

// Main execution
async function main() {
  console.log('🚀 Setting up Python environment...');

  try {
    await downloadPython();
    await installDependencies();
    console.log('✅ Setup completed successfully!');
  } catch (err) {
    console.error('❌ Setup failed:', err);
    process.exit(1);
  }
}

// Run main if this script is executed directly
if (require.main === module) {
  main();
}

export {
  downloadPython,
  installDependencies
};