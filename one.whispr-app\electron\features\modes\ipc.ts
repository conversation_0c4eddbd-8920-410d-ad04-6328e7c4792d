import { ipc<PERSON>ain } from 'electron';
import { ModeRepository } from './repository';
import { Mode } from './types';

const modeRepository = new ModeRepository();

/**
 * Sets up all mode-related IPC handlers
 */
export function setupModeIPCHandlers(): void {
  
  // Get all modes
  ipcMain.handle('modes:get-all', async (): Promise<Mode[]> => {
    try {
      return modeRepository.findAll();
    } catch (error) {
      console.error('Error getting all modes:', error);
      return [];
    }
  });

  // Get mode by ID
  ipcMain.handle('modes:get-by-id', async (_event, id: string): Promise<Mode | null> => {
    try {
      return modeRepository.findById(id);
    } catch (error) {
      console.error('Error getting mode by ID:', error);
      return null;
    }
  });

  // Get active modes
  ipcMain.handle('modes:get-active', async (): Promise<Mode[]> => {
    try {
      return modeRepository.getActiveModes();
    } catch (error) {
      console.error('Error getting active modes:', error);
      return [];
    }
  });

  // Get modes by voice model
  ipcMain.handle('modes:get-by-voice-model', async (_event, voiceModelId: string): Promise<Mode[]> => {
    try {
      return modeRepository.findByVoiceModel(voiceModelId);
    } catch (error) {
      console.error('Error getting modes by voice model:', error);
      return [];
    }
  });

  // Get modes by AI model
  ipcMain.handle('modes:get-by-ai-model', async (_event, aiModelId: string): Promise<Mode[]> => {
    try {
      return modeRepository.findByAIModel(aiModelId);
    } catch (error) {
      console.error('Error getting modes by AI model:', error);
      return [];
    }
  });

  // Search modes
  ipcMain.handle('modes:search', async (_event, searchTerm: string): Promise<Mode[]> => {
    try {
      return modeRepository.searchModes(searchTerm);
    } catch (error) {
      console.error('Error searching modes:', error);
      return [];
    }
  });

  // Create new mode
  ipcMain.handle('modes:create', async (_event, data: Omit<Mode, 'id'>): Promise<Mode | null> => {
    try {
      return modeRepository.create(data);
    } catch (error) {
      console.error('Error creating mode:', error);
      return null;
    }
  });

  // Update mode
  ipcMain.handle('modes:update', async (_event, id: string, data: Partial<Omit<Mode, 'id'>>): Promise<Mode | null> => {
    try {
      return modeRepository.update(id, data);
    } catch (error) {
      console.error('Error updating mode:', error);
      return null;
    }
  });

  // Delete mode
  ipcMain.handle('modes:delete', async (_event, id: string): Promise<boolean> => {
    try {
      return modeRepository.delete(id);
    } catch (error) {
      console.error('Error deleting mode:', error);
      return false;
    }
  });

  // Update mode order
  ipcMain.handle('modes:update-order', async (_event, modeId: string, newOrderIndex: number): Promise<Mode | null> => {
    try {
      return modeRepository.updateOrder(modeId, newOrderIndex);
    } catch (error) {
      console.error('Error updating mode order:', error);
      return null;
    }
  });

  // Bulk update mode orders
  ipcMain.handle('modes:bulk-update-order', async (_event, orderUpdates: { id: string; order_index: number }[]): Promise<boolean> => {
    try {
      return modeRepository.bulkUpdateOrder(orderUpdates);
    } catch (error) {
      console.error('Error bulk updating mode orders:', error);
      return false;
    }
  });

  // Get mode statistics
  ipcMain.handle('modes:get-statistics', async () => {
    try {
      return modeRepository.getStatistics();
    } catch (error) {
      console.error('Error getting mode statistics:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        byVoiceModel: {},
        byAIModel: {}
      };
    }
  });

  console.log('Mode IPC handlers registered');
} 