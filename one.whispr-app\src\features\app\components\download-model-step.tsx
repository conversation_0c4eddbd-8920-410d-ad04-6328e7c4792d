import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from '@src/components/ui/card';
import { But<PERSON> } from '@src/components/ui/button';
import { Badge } from '@src/components/ui/badge';
import { ScrollArea } from '@src/components/ui/scroll-area';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@src/components/ui/tooltip';
import { useSetup, useModel } from '../context';
import { VoiceModel } from '../types';
import { 
  RiCheckboxBlankLine, 
  RiCheckboxLine, 
  RiTranslate, 
  RiDownloadLine, 
  RiFlashlightLine, 
  RiCheckLine, 
  RiRefreshLine,
  RiInformationLine,
  RiLoaderLine,
  RiErrorWarningLine,
  RiCloseLine,
  RiRestartLine
} from 'react-icons/ri';
import { ModelDownloadProgress } from './model-download-progress';
import { useEffect, useState } from 'react';

export function DownloadModelStep() {
  const { 
    goToNextStep, 
    goToPreviousStep
  } = useSetup();
  
  const {
    availableModels,
    selectedModel,
    selectModel,
    downloadModel,
    loadModel,
    isDownloading,
    downloadProgress,
    isCheckingStatus,
    forceModelStatusCheck,
    currentDownloadingModel,
    loadingError,
    isModelDownloading,
    cancelDownload,
    retryDownload,
    isRetrying,
    loadedModel
  } = useModel();
  
  const [hasCheckedModels, setHasCheckedModels] = useState(false);

  // Check model status only once on mount
  useEffect(() => {
    if (!hasCheckedModels) {
      console.log('DownloadModelStep - Initial model check');
      
      // Force model status check once on component mount
      forceModelStatusCheck();
      
      // Explicit check for loadedModel to ensure it's reflected in the UI
      setTimeout(() => {
        console.log('Explicit check for loaded model status:', loadedModel);
        if (loadedModel) {
          // Update model in the list to show as downloaded
          const loadedModelObj = availableModels.find(m => m.id === loadedModel);
          if (loadedModelObj) {
            console.log(`Explicitly setting ${loadedModel} as downloaded`);
            selectModel(loadedModel);
          }
        }
      }, 1000); // Give backend a second to update
      
      setHasCheckedModels(true);
      
      // Log current state
      console.log('DownloadModelStep - Initial Models:', availableModels);
      console.log('DownloadModelStep - Initial Selected model:', selectedModel);
      console.log('DownloadModelStep - Initial Loaded model:', loadedModel);
    }
  }, [forceModelStatusCheck, hasCheckedModels, availableModels, selectedModel, loadedModel, selectModel]);
  
  // Output debug status for each model
  useEffect(() => {
    // Debug: Log download status of all models
    console.log('MODEL STATUS CHECK:');
    for (const model of availableModels) {
      console.log(`Model ${model.id}: isDownloaded=${model.isDownloaded}, loadedModel=${loadedModel === model.id ? 'true' : 'false'}`);
    }
  }, [availableModels, loadedModel]);
  
  // Handle model selection when loaded model changes
  useEffect(() => {
    // Use loadedModel info to update selection if needed
    if (loadedModel) {
      const loadedModelObj = availableModels.find(m => m.id === loadedModel);
      if (loadedModelObj) {
        console.log(`Using loaded model from backend: ${loadedModelObj.id}`);
        
        // Update the model's isDownloaded flag if needed
        if (!loadedModelObj.isDownloaded) {
          console.log(`Marking model ${loadedModelObj.id} as downloaded because it's the loaded model`);
          // This will be handled by context - just select the model
        }
        
        selectModel(loadedModelObj.id);
      }
    }
  }, [loadedModel, availableModels, selectModel]);
  
  // Special one-time effect to directly query model status from backend
  useEffect(() => {
    const checkBackendModelStatus = async () => {
      try {
        console.log("Direct backend model status check");
        const response = await window.electron.sendCommand('models.get_active');
        
        if (response.status === 'success') {
          const { modelLoaded, loadedModel: backendLoadedModel } = response.data;
          
          if (modelLoaded && backendLoadedModel) {
            console.log(`Backend reports model ${backendLoadedModel} is loaded`);
            
            // If the model exists in our available models, ensure it's selected and marked as downloaded
            const modelObj = availableModels.find(m => m.id === backendLoadedModel);
            if (modelObj) {
              if (!modelObj.isDownloaded) {
                console.log(`Backend says ${backendLoadedModel} is loaded but frontend shows not downloaded, fixing...`);
                selectModel(backendLoadedModel);
              }
            }
          }
        }
      } catch (error) {
        console.error("Error checking backend model status:", error);
      }
    };
    
    // Run once on component mount
    checkBackendModelStatus();
  }, [availableModels, selectModel]); // Include required dependencies
  
  const handleDownload = async () => {
    console.log(`Handling download for model: ${selectedModel?.id}, isDownloaded: ${selectedModel?.isDownloaded}, loadedModel: ${loadedModel}`);
    
    // If the model is already downloaded or loaded, just continue
    if (selectedModel && (selectedModel.isDownloaded || selectedModel.id === loadedModel)) {
      console.log('Model is already downloaded, proceeding to continue');
      handleContinue();
      return;
    }
    
    await downloadModel();
  };

  const handleRefresh = async () => {
    console.log('User requested model refresh');
    await forceModelStatusCheck();
  };
  
  const handleContinue = async () => {
    if (!selectedModel || !selectedModel.isDownloaded) return;
    
    // If the model is already downloaded, load it and continue
    // This ensures the model is ready in memory when proceeding
    await loadModel(selectedModel.id);
    
    // Navigate to the next step
    goToNextStep();
  };

  const handleCancel = async () => {
    await cancelDownload();
  };

  const handleRetry = async () => {
    await retryDownload();
  };
  
  // Sort models by file size (smallest to largest)
  const sortedModels = [...availableModels].sort((a, b) => {
    // Extract numeric size values (removing "MB" or "GB" and converting to MB)
    const getValueInMB = (sizeStr: string) => {
      const value = parseFloat(sizeStr.replace(/[^\d.]/g, ''));
      return sizeStr.includes('GB') ? value * 1024 : value;
    };
    
    return getValueInMB(a.size) - getValueInMB(b.size);
  });
  
  // Group models by language support
  const englishOnlyModels = sortedModels.filter(model => model.isEnglishOnly);
  const multilingualModels = sortedModels.filter(model => !model.isEnglishOnly);
  
  // Helper function to render a model card
  const renderModelCard = (model: VoiceModel) => {
    const isSelected = selectedModel?.id === model.id;
    
    // CRITICAL: Ensure models that match loadedModel are ALWAYS shown as downloaded
    // regardless of what the model object says
    let isDownloaded = model.isDownloaded || model.id === loadedModel;
    
    // Extra check for downloaded status
    if (model.id === loadedModel) {
      console.log(`Model ${model.id} detected as loaded model, forcing isDownloaded=true`);
      // If this model is the currently loaded model, force it to show as downloaded
      isDownloaded = true;
    }
    
    const isCurrentlyDownloading = isModelDownloading(model.id);
    
    // Determine if the card should be disabled (when another model is downloading)
    const isDisabled = isDownloading && !isCurrentlyDownloading;
    
    // Get tooltip content
    const getTooltipContent = () => {
      if (isCurrentlyDownloading) {
        return `Downloading: ${downloadProgress}% complete`;
      }
      if (isDownloaded) {
        return `Model is downloaded and ready to use`;
      }
      return `${model.size} model, ${model.languages}`;
    };
    
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div 
              className={`border rounded-lg p-3 transition-all flex ${
                isDisabled 
                  ? 'opacity-50 cursor-not-allowed border-muted bg-muted/20' 
                  : isSelected 
                    ? 'border-primary bg-primary/5 cursor-pointer' 
                    : 'hover:border-primary/30 hover:bg-muted/50 cursor-pointer'
              }`}
              onClick={() => !isDisabled && selectModel(model.id)}
              role="button"
              tabIndex={isDisabled ? -1 : 0}
              data-state={isSelected ? "selected" : "unselected"}
              data-testid={`model-card-${model.id.replace(/\//g, '-')}`}
              data-downloaded={isDownloaded ? "true" : "false"}
              data-downloading={isCurrentlyDownloading ? "true" : "false"}
              data-model-id={model.id}
            >
              {/* Left side - checkbox indicator */}
              <div className="flex-shrink-0 mr-3 flex items-center h-full justify-center">
                {isSelected ? (
                  <RiCheckboxLine size={18} className="text-primary" />
                ) : (
                  <RiCheckboxBlankLine size={18} className="text-muted-foreground/70" />
                )}
              </div>
              
              {/* Middle - main content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium text-sm flex items-center gap-1.5 truncate">
                    {model.name}
                    {model.isEnglishOnly && (
                      <span className="text-xs text-muted-foreground/70">(English)</span>
                    )}
                  </h3>
                  
                  <div className="flex items-center gap-1.5">
                    <Badge 
                      variant="outline" 
                      className="text-[10px] py-0 h-5 flex items-center gap-1 whitespace-nowrap"
                    >
                      {model.size}
                    </Badge>
                  </div>
                </div>
                
                <div className="flex flex-wrap items-center gap-1.5 mt-1">
                  <Badge 
                    variant={model.isEnglishOnly ? "outline" : "secondary"} 
                    className="text-[10px] py-0 h-5 flex items-center gap-1"
                  >
                    {model.isEnglishOnly ? (
                      <>English</>
                    ) : (
                      <><RiTranslate size={12} /> Multilingual</>
                    )}
                  </Badge>
                  
                  <Badge 
                    variant="outline" 
                    className={`text-[10px] py-0 h-5 flex items-center gap-1 ${
                      model.speed === 'Fast' ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' : 
                      model.speed === 'Medium' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400' : 
                      'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
                    }`}
                  >
                    <RiFlashlightLine size={10} /> {model.speed}
                  </Badge>
                  
                  {/* Download status badge */}
                  {isCurrentlyDownloading ? (
                    <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900/40 dark:text-blue-400 text-[10px] py-0 h-5 flex items-center gap-1 animate-pulse">
                      <RiLoaderLine size={10} className="animate-spin" /> Downloading
                    </Badge>
                  ) : isDownloaded ? (
                    <Badge className="bg-green-100 text-green-700 dark:bg-green-900/40 dark:text-green-400 text-[10px] py-0 h-5 flex items-center gap-1">
                      <RiCheckLine size={10} /> Downloaded
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="text-[10px] py-0 h-5 flex items-center gap-1 border-dashed">
                      <RiDownloadLine size={10} /> Not Downloaded
                    </Badge>
                  )}
                </div>
              </div>
              
              {/* Right side - icon */}
              <div className="flex-shrink-0 ml-2 flex items-center justify-center">
                {isCurrentlyDownloading ? (
                  <RiLoaderLine size={18} className="text-blue-500 animate-spin" />
                ) : isDownloaded ? (
                  <RiCheckLine size={18} className="text-green-500" />
                ) : (
                  <RiInformationLine size={18} className="text-muted-foreground/50" />
                )}
              </div>
            </div>
          </TooltipTrigger>
          <TooltipContent side="right" align="center" className="max-w-xs">
            {getTooltipContent()}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };
  
  // Determine what the primary button should be
  const renderPrimaryButton = () => {
    if (isCheckingStatus) {
      return (
        <Button disabled className="min-w-[120px]">
          <RiRefreshLine size={16} className="mr-2 animate-spin" />
          Checking...
        </Button>
      );
    }
    
    if (isDownloading) {
      return (
        <div className="flex gap-2">
          <Button 
            variant="destructive" 
            size="icon" 
            onClick={handleCancel}
            className="h-9 w-9"
            title="Cancel download"
          >
            <RiCloseLine size={16} />
          </Button>
          
          <Button disabled className="min-w-[120px]">
            <RiLoaderLine size={16} className="mr-2 animate-spin" />
            Downloading...
          </Button>
        </div>
      );
    }
    
    if (loadingError) {
      return (
        <Button 
          variant="destructive" 
          onClick={handleRetry}
          className="gap-1"
          disabled={isRetrying}
        >
          {isRetrying ? (
            <>
              <RiLoaderLine size={16} className="animate-spin" /> Retrying...
            </>
          ) : (
            <>
              <RiRestartLine size={16} /> Retry Download
            </>
          )}
        </Button>
      );
    }
    
    if (!selectedModel) {
      return (
        <Button disabled>
          Select a Model
        </Button>
      );
    }
    
    // Consider the model downloaded if either the model object says so OR it's the loaded model
    const isModelDownloaded = selectedModel.isDownloaded || loadedModel === selectedModel.id;
    
    if (isModelDownloaded) {
      return (
        <Button onClick={handleContinue} className="gap-1">
          <RiCheckLine size={16} /> Continue
        </Button>
      );
    }
    
    return (
      <Button 
        onClick={handleDownload}
        className="gap-1"
      >
        <RiDownloadLine size={16} /> Download
      </Button>
    );
  };
  
  return (
    <Card className="w-full max-w-md flex flex-col">
      <CardHeader className="text-center space-y-1">
        <CardTitle className="text-2xl">Select Voice Model</CardTitle>
        <CardDescription className="flex items-center justify-center gap-2">
          Choose a model based on your needs and hardware limits
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6" 
            onClick={handleRefresh}
            disabled={isCheckingStatus}
            aria-label="Refresh model status"
          >
            <RiRefreshLine size={16} className={isCheckingStatus ? "animate-spin" : ""} />
          </Button>
        </CardDescription>
      </CardHeader>
      
      <CardContent className="flex-1 overflow-hidden pt-0">
        {isCheckingStatus && availableModels.length === 0 ? (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-center">
              <div className="animate-spin mb-2 mx-auto">
                <RiRefreshLine size={24} />
              </div>
              <p className="text-sm text-muted-foreground">
                Checking available models...
              </p>
            </div>
          </div>
        ) : (
          <ScrollArea className="h-[350px] pr-4 pb-2">
            {loadingError && (
              <div className="mb-4 bg-red-50 dark:bg-red-950/30 text-red-800 dark:text-red-200 p-3 rounded-md flex items-start gap-2 text-sm">
                <RiErrorWarningLine size={20} className="flex-shrink-0 mt-0.5" />
                <div>
                  <p className="font-medium">Error</p>
                  <p className="text-xs mt-1">{loadingError}</p>
                </div>
              </div>
            )}
            
            <div className="space-y-4">
              {multilingualModels.length > 0 && (
                <div>
                  <h4 className="text-xs font-medium mb-2 text-muted-foreground flex items-center gap-1">
                    <RiTranslate size={14} /> Multilingual Models
                  </h4>
                  <div className="flex flex-col gap-2">
                    {multilingualModels.map(model => (
                      <div key={model.id}>
                        {renderModelCard(model)}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {englishOnlyModels.length > 0 && (
                <div>
                  <h4 className="text-xs font-medium mb-2 text-muted-foreground">
                    English-only Models
                  </h4>
                  <div className="flex flex-col gap-2 mb-2">
                    {englishOnlyModels.map(model => (
                      <div key={model.id}>
                        {renderModelCard(model)}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        )}
        
        <div className="mt-4">
          {isDownloading && currentDownloadingModel && (
            <div className="mb-4">
              <ModelDownloadProgress 
                progress={downloadProgress}
                modelName={availableModels.find(m => m.id === currentDownloadingModel)?.name}
                error={loadingError}
              />
            </div>
          )}
          
          <div className="flex justify-between">
            <Button variant="outline" onClick={goToPreviousStep} disabled={isCheckingStatus}>
              Back
            </Button>
            {renderPrimaryButton()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 