/**
 * Shortcut Context
 * Keyboard shortcut management with recording and testing functionality
 * Integrates with SetupFlowContext for step lifecycle management
 */

import React, { 
  createContext, 
  useContext, 
  useReducer, 
  useCallback, 
  useEffect,
  useRef
} from 'react';

import { useStepIntegration } from '../hooks/useStepIntegration';
import { DEFAULT_SHORTCUTS } from '../types/constants';
import type { ShortcutState, Shortcuts } from '../types/shortcut';
import type { RecordingMode } from '../types/core';

// ============================================================================
// ACTIONS
// ============================================================================

type ShortcutAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_RECORDING_MODE'; payload: RecordingMode }
  | { type: 'SET_SHORTCUTS'; payload: Shortcuts }
  | { type: 'UPDATE_SHORTCUT'; payload: { action: keyof Shortcuts; keys: string[] } }
  | { type: 'SET_PRESSED_KEYS'; payload: Set<string> }
  | { type: 'SET_RECORDING_SHORTCUT'; payload: { isRecording: boolean; action: keyof Shortcuts | null } }
  | { type: 'SET_TEMP_KEYS'; payload: string[] }
  | { type: 'SET_TEST_MODE_ENABLED'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'CLEAR_ERROR' };

// ============================================================================
// REDUCER
// ============================================================================

function shortcutReducer(state: ShortcutState, action: ShortcutAction): ShortcutState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };

    case 'SET_RECORDING_MODE':
      return { ...state, recordingMode: action.payload };

    case 'SET_SHORTCUTS':
      return { ...state, shortcuts: action.payload };

    case 'UPDATE_SHORTCUT':
      return {
        ...state,
        shortcuts: {
          ...state.shortcuts,
          [action.payload.action]: action.payload.keys,
        },
      };

    case 'SET_PRESSED_KEYS':
      return { ...state, pressedKeys: action.payload };

    case 'SET_RECORDING_SHORTCUT':
      return { 
        ...state, 
        isRecordingShortcut: action.payload.isRecording,
        recordingAction: action.payload.action,
      };

    case 'SET_TEMP_KEYS':
      return { ...state, tempKeys: action.payload };

    case 'SET_TEST_MODE_ENABLED':
      return { ...state, testModeEnabled: action.payload };

    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false };

    case 'CLEAR_ERROR':
      return { ...state, error: null };

    default:
      return state;
  }
}

// ============================================================================
// INITIAL STATE
// ============================================================================

const initialState: ShortcutState = {
  recordingMode: 'pushToTalk',
  shortcuts: { ...DEFAULT_SHORTCUTS },
  pressedKeys: new Set(),
  isRecordingShortcut: false,
  recordingAction: null,
  tempKeys: [],
  testModeEnabled: false,
  loading: false,
  error: null,
};

// ============================================================================
// CONTEXT INTERFACE
// ============================================================================

interface ShortcutContextValue {
  state: ShortcutState;
  
  // Core actions
  setRecordingMode: (mode: RecordingMode) => void;
  updateShortcut: (action: keyof Shortcuts, keys: string[]) => void;
  startRecordingShortcut: (action: keyof Shortcuts) => Promise<void>;
  stopRecordingShortcut: () => void;
  saveRecordingShortcut: () => void;
  cancelRecordingShortcut: () => void;
  
  // Test mode actions
  enableTestMode: () => Promise<void>;
  disableTestMode: () => Promise<void>;
  
  // Settings management
  loadSettings: () => Promise<void>;
  saveSettings: () => Promise<void>;
  
  // Utility actions
  clearError: () => void;
  
  // Convenience getters
  canProceedToNext: boolean;
  isShortcutReady: boolean;
}

const ShortcutContext = createContext<ShortcutContextValue | null>(null);

// ============================================================================
// PROVIDER
// ============================================================================

interface ShortcutProviderProps {
  children: React.ReactNode;
}

export function ShortcutProvider({ children }: ShortcutProviderProps) {
  const [state, dispatch] = useReducer(shortcutReducer, initialState);
  
  // Refs for tracking state and cleanup
  const testModeEnabledRef = useRef(false);
  const isUnmountingRef = useRef(false);

  // Computed state for proceed logic
  const canProceedToNext = Boolean(
    (state.recordingMode === 'pushToTalk' && state.shortcuts.pushToTalk.length > 0) ||
    (state.recordingMode === 'toggle' && state.shortcuts.toggle.length > 0)
  );
  const isShortcutReady = canProceedToNext;

  // ============================================================================
  // CORE SHORTCUT MANAGEMENT
  // ============================================================================

  const setRecordingMode = useCallback(async (mode: RecordingMode) => {
    console.log('🎯 Setting recording mode:', mode);
    dispatch({ type: 'SET_RECORDING_MODE', payload: mode });
    // Note: Persisting settings is now handled in the ShortcutPage component
  }, []);

  const updateShortcut = useCallback((action: keyof Shortcuts, keys: string[]) => {
    console.log('🔧 Updating shortcut for action:', action, 'keys:', keys);
    dispatch({ type: 'UPDATE_SHORTCUT', payload: { action, keys } });
  }, []);

  const startRecordingShortcut = useCallback(async (action: keyof Shortcuts) => {
    try {
      console.log('🎙️ Starting shortcut recording for action:', action);
      dispatch({ type: 'CLEAR_ERROR' });
      dispatch({ type: 'SET_RECORDING_SHORTCUT', payload: { isRecording: true, action } });
      dispatch({ type: 'SET_TEMP_KEYS', payload: [] });

      if (!window.electron?.sendCommand) {
        throw new Error('Python backend not available');
      }

      // Map shortcut actions to Python targets
      const targetMap = {
        pushToTalk: 'pushToTalk',
        toggle: 'toggle',
        cancel: 'cancel',
        modeSwitch: 'modeSwitch',
      };

      const result = await window.electron.sendCommand('shortcut.start_update', {
        target: targetMap[action],
      });

      if (!result.success) {
        throw new Error(result.error || 'Failed to start shortcut recording');
      }

      console.log('✓ Shortcut recording started successfully');
    } catch (error: any) {
      console.error('❌ Failed to start shortcut recording:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
      dispatch({ type: 'SET_RECORDING_SHORTCUT', payload: { isRecording: false, action: null } });
    }
  }, []);

  const stopRecordingShortcut = useCallback(() => {
    console.log('⏹️ Stopping shortcut recording');
    dispatch({ type: 'SET_RECORDING_SHORTCUT', payload: { isRecording: false, action: null } });
    dispatch({ type: 'SET_TEMP_KEYS', payload: [] });
  }, []);

  const saveRecordingShortcut = useCallback(() => {
    if (state.recordingAction && state.tempKeys.length > 0) {
      console.log('💾 Saving recorded shortcut:', state.recordingAction, state.tempKeys);
      
      // Update the shortcut
      dispatch({ type: 'UPDATE_SHORTCUT', payload: { 
        action: state.recordingAction, 
        keys: state.tempKeys 
      }});
      
      // Clear recording state
      dispatch({ type: 'SET_RECORDING_SHORTCUT', payload: { isRecording: false, action: null } });
      dispatch({ type: 'SET_TEMP_KEYS', payload: [] });
      
      // Note: Persisting to settings is now handled in the ShortcutPage component
    }
  }, [state.recordingAction, state.tempKeys]);

  const cancelRecordingShortcut = useCallback(() => {
    console.log('❌ Canceling shortcut recording');
    dispatch({ type: 'SET_RECORDING_SHORTCUT', payload: { isRecording: false, action: null } });
    dispatch({ type: 'SET_TEMP_KEYS', payload: [] });
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // ============================================================================
  // TEST MODE MANAGEMENT
  // ============================================================================

  const enableTestMode = useCallback(async () => {
    if (state.testModeEnabled) {
      console.log('🧪 Test mode already enabled, skipping request');
      return;
    }

    try {
      console.log('🧪 Enabling shortcut test mode...');
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.sendCommand) {
        throw new Error('Python backend not available');
      }

      const result = await window.electron.sendCommand('shortcut.test_mode');

      if (!result.success) {
        throw new Error(result.error || 'Failed to enable test mode');
      }

      dispatch({ type: 'SET_TEST_MODE_ENABLED', payload: true });
      console.log('✓ Test mode enabled successfully');
    } catch (error: any) {
      console.error('❌ Error enabling test mode:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    }
  }, [state.testModeEnabled]);

  const disableTestMode = useCallback(async () => {
    if (!state.testModeEnabled) {
      console.log('🧪 Test mode already disabled, skipping request');
      return;
    }

    try {
      console.log('🧪 Disabling shortcut test mode...');
      dispatch({ type: 'CLEAR_ERROR' });

      if (!window.electron?.sendCommand) {
        throw new Error('Python backend not available');
      }

      const result = await window.electron.sendCommand('shortcut.exit_test_mode');

      if (!result.success) {
        throw new Error(result.error || 'Failed to disable test mode');
      }

      dispatch({ type: 'SET_TEST_MODE_ENABLED', payload: false });
      console.log('✓ Test mode disabled successfully');
    } catch (error: any) {
      console.error('❌ Error disabling test mode:', error);
      // Don't update state on failure to prevent UI confusion
    }
  }, [state.testModeEnabled]);

  // ============================================================================
  // SETTINGS MANAGEMENT
  // ============================================================================

  const loadSettings = useCallback(async () => {
    try {
      console.log('📖 Loading shortcut settings...');
      dispatch({ type: 'SET_LOADING', payload: true });

      if (!window.electron?.getSettings) {
        console.warn('Settings API not available');
        return;
      }

      const settings = await window.electron.getSettings();
      
      // Load recording mode
      if (settings.recordingMode) {
        dispatch({ type: 'SET_RECORDING_MODE', payload: settings.recordingMode });
      }
      
      // Load shortcuts
      if (settings.shortcuts) {
        dispatch({ type: 'SET_SHORTCUTS', payload: {
          pushToTalk: settings.shortcuts.pushToTalk || DEFAULT_SHORTCUTS.pushToTalk,
          toggle: settings.shortcuts.toggle || DEFAULT_SHORTCUTS.toggle,
          cancel: settings.shortcuts.cancel || DEFAULT_SHORTCUTS.cancel,
          modeSwitch: settings.shortcuts.modeSwitch || DEFAULT_SHORTCUTS.modeSwitch,
        }});
      }

      console.log('✓ Shortcut settings loaded successfully');
    } catch (error: any) {
      console.error('❌ Error loading shortcut settings:', error);
      dispatch({ type: 'SET_ERROR', payload: error.message });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const saveSettings = useCallback(async () => {
    try {
      if (!window.electron?.updateSettings) {
        console.warn('Settings API not available');
        return;
      }

      await window.electron.updateSettings({
        recordingMode: state.recordingMode,
        shortcuts: {
          pushToTalk: state.shortcuts.pushToTalk,
          toggle: state.shortcuts.toggle,
          cancel: state.shortcuts.cancel,
          modeSwitch: state.shortcuts.modeSwitch,
        }
      });

      console.log('💾 Shortcut settings saved successfully');
    } catch (error: any) {
      console.error('❌ Error saving shortcut settings:', error);
    }
  }, [state.recordingMode, state.shortcuts]);

  const clearError = useCallback(() => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  // ============================================================================
  // PYTHON EVENT HANDLERS
  // ============================================================================

  useEffect(() => {
    console.log('🔧 Setting up shortcut event listeners');

    if (typeof window !== 'undefined' && window.electron?.onBackendMessage) {
      const cleanup = window.electron.onBackendMessage((message: any) => {
        // Filter out high-frequency audio events to prevent console spam
        if (message.type !== 'event' || message.event !== 'audio_levels') {
          console.log('📨 Shortcut Python message:', message);
        }
        
        if (message.type === 'key_event') {
          // Handle real-time key tracking from Python
          const currentlyPressed = message.data?.currently_pressed || [];
          dispatch({ type: 'SET_PRESSED_KEYS', payload: new Set(currentlyPressed) });
        } else if (message.type === 'event') {
          switch (message.event) {
            case 'shortcut_test_mode_enabled':
              console.log('🧪 Shortcut test mode enabled from Python');
              dispatch({ type: 'SET_TEST_MODE_ENABLED', payload: true });
              testModeEnabledRef.current = true;
              break;

            case 'shortcut_test_mode_disabled':
              console.log('🔒 Shortcut test mode disabled from Python');
              dispatch({ type: 'SET_TEST_MODE_ENABLED', payload: false });
              testModeEnabledRef.current = false;
              // Clear pressed keys
              dispatch({ type: 'SET_PRESSED_KEYS', payload: new Set() });
              break;

            case 'shortcutUpdate':
              // Handle shortcut update during recording
              console.log('📝 Received shortcut update:', message.data);
              if (state.recordingAction && message.data) {
                const { target, keys, complete } = message.data;
                
                // Map Python targets back to our action names
                const targetMap: Record<string, keyof Shortcuts> = {
                  pushToTalk: 'pushToTalk',
                  toggle: 'toggle',
                  cancel: 'cancel',
                  modeSwitch: 'modeSwitch',
                };
                
                const currentTarget = targetMap[target];
                if (currentTarget === state.recordingAction) {
                  console.log(`✅ Shortcut update for ${target}:`, keys, 'complete:', complete);
                  
                  // Update the temp keys display
                  dispatch({ type: 'SET_TEMP_KEYS', payload: keys || [] });
                  
                  // If recording is complete, save the shortcut
                  if (complete && keys && keys.length > 0) {
                    console.log('💾 Shortcut recording complete, saving:', keys);
                    dispatch({ type: 'UPDATE_SHORTCUT', payload: { 
                      action: state.recordingAction, 
                      keys: keys 
                    }});
                    dispatch({ type: 'SET_RECORDING_SHORTCUT', payload: { isRecording: false, action: null } });
                    dispatch({ type: 'SET_TEMP_KEYS', payload: [] });
                    dispatch({ type: 'CLEAR_ERROR' });
                  }
                }
              }
              break;
          }
        }
      });

      return () => {
        console.log('🧹 Cleaning up shortcut event listeners');
        cleanup();
      };
    }
  }, [state.recordingAction]);

  // ============================================================================
  // INITIALIZATION AND CLEANUP
  // ============================================================================

  // Load settings on mount
  useEffect(() => {
    console.log('🚀 Initializing ShortcutProvider');
    loadSettings();
  }, [loadSettings]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('🧹 Cleaning up ShortcutProvider');
      isUnmountingRef.current = true;
      
      if (testModeEnabledRef.current) {
        disableTestMode().catch(error => {
          console.error('Failed to disable test mode during cleanup:', error);
        });
      }
    };
  }, [disableTestMode]);

  // ============================================================================
  // STEP LIFECYCLE INTEGRATION
  // ============================================================================

  // Use the step integration hook to handle proceed state and lifecycle
  useStepIntegration({
    step: 'shortcut',
    canProceed: canProceedToNext,
    onEnter: async () => {
      console.log('⌨️ Entering shortcut step - loading settings and enabling test mode');
      await loadSettings();
      if (!state.testModeEnabled && !testModeEnabledRef.current) {
        await enableTestMode();
      }
    },
    onExit: async () => {
      console.log('⌨️ Exiting shortcut step - disabling test mode and saving settings');
      if (state.testModeEnabled || testModeEnabledRef.current) {
        await disableTestMode();
      }
      await saveSettings();
    },
    deps: [state.testModeEnabled]
  });

  // ============================================================================
  // CONTEXT VALUE
  // ============================================================================

  const contextValue: ShortcutContextValue = {
    state,
    setRecordingMode,
    updateShortcut,
    startRecordingShortcut,
    stopRecordingShortcut,
    saveRecordingShortcut,
    cancelRecordingShortcut,
    enableTestMode,
    disableTestMode,
    loadSettings,
    saveSettings,
    clearError,
    canProceedToNext,
    isShortcutReady,
  };

  return (
    <ShortcutContext.Provider value={contextValue}>
      {children}
    </ShortcutContext.Provider>
  );
}

// ============================================================================
// HOOK
// ============================================================================

export function useShortcut(): ShortcutContextValue {
  const context = useContext(ShortcutContext);
  if (!context) {
    throw new Error('useShortcut must be used within a ShortcutProvider');
  }
  return context;
} 