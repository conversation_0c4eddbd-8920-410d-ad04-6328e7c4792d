"""
Audio Capture

Dual audio capture with 100ms chunking and per-source VAD.
Handles microphone and system audio with volume normalization.
Applies VAD to individual sources before combining.
"""

import logging
import time
import threading
from typing import Dict, Any, Optional, Callable
import numpy as np
import sounddevice as sd

# Optional import for system audio capture on Windows
try:
    import pyaudiowpatch as pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    pyaudio = None
    PYAUDIO_AVAILABLE = False

# Import VAD for per-source processing
from .silero_vad import SileroVAD

logger = logging.getLogger(__name__)


class AudioCapture:
    """Dual audio capture with 100ms chunking and per-source VAD."""
    
    def __init__(self, sample_rate: int = 16000, mic_vad=None, system_vad=None):
        """Initialize the audio capture.
        
        Args:
            sample_rate: Audio sample rate (16kHz for Whisper)
            mic_vad: Pre-initialized VAD instance for microphone (optional)
            system_vad: Pre-initialized VAD instance for system audio (optional)
        """
        self.sample_rate = sample_rate
        self.chunk_size = int(sample_rate * 0.1)  # 100ms chunks = 1600 samples
        
        # Audio streams
        self.mic_stream = None
        self.system_stream = None
        
        # State
        self.is_capturing = False
        self.enable_system_audio = False
        self.selected_devices = None
        
        # Callback for processed chunks
        self.chunk_callback = None
        
        # Audio buffers for mixing
        self.mic_buffer = np.zeros(self.chunk_size, dtype=np.float32)
        self.system_buffer = np.zeros(self.chunk_size, dtype=np.float32)
        
        # Use pre-initialized VAD instances (required)
        if not mic_vad or not system_vad:
            raise ValueError("AudioCapture requires pre-initialized VAD instances")
        
        logger.info("Using pre-initialized VAD instances")
        self.mic_vad = mic_vad
        self.system_vad = system_vad
        
        # VAD results for decision making
        self.mic_vad_result = {"is_speech": True, "probability": 1.0}
        self.system_vad_result = {"is_speech": True, "probability": 1.0}
        
        # System audio resampling (will be determined dynamically from device)
        self.system_sample_rate = None  # Will be set when device is found
        self.system_chunk_size = None   # Will be calculated based on actual sample rate
        self.resample_ratio = None      # Will be calculated when sample rates are known
        self.system_resample_buffer = []  # Buffer for resampling
        
        # Thread safety
        self.lock = threading.RLock()
        
        logger.debug(f"AudioCapture initialized: {sample_rate}Hz, {self.chunk_size} samples per chunk")
    
    def set_chunk_callback(self, callback: Callable[[np.ndarray, float, Dict[str, Any]], None]) -> None:
        """Set callback for processed audio chunks.
        
        Args:
            callback: Function to call with (audio_data, timestamp, vad_info)
        """
        self.chunk_callback = callback
        logger.debug("Chunk callback set")
    
    def start_capture(self, enable_system_audio: bool = False, selected_devices: Optional[Dict] = None) -> bool:
        """Start audio capture.
        
        Args:
            enable_system_audio: Whether to capture system audio
            selected_devices: Selected device configuration
            
        Returns:
            True if capture started successfully
        """
        with self.lock:
            try:
                if self.is_capturing:
                    logger.warning("Audio capture already running")
                    return True
                
                self.enable_system_audio = enable_system_audio
                self.selected_devices = selected_devices or {}
                
                logger.info(f"Starting audio capture: system_audio={enable_system_audio}")
                
                # Always start microphone
                if not self._start_microphone():
                    logger.error("Failed to start microphone")
                    return False
                
                # Start system audio if requested
                if enable_system_audio:
                    if not self._start_system_audio():
                        logger.warning("Failed to start system audio, continuing with microphone only")
                        self.enable_system_audio = False
                
                self.is_capturing = True
                logger.info("Audio capture started successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to start audio capture: {e}")
                self._cleanup_streams()
                return False
    
    def stop_capture(self) -> bool:
        """Stop audio capture.
        
        Returns:
            True if capture stopped successfully
        """
        with self.lock:
            try:
                if not self.is_capturing:
                    logger.warning("Audio capture not running")
                    return True
                
                logger.info("Stopping audio capture")
                
                self._cleanup_streams()
                self.is_capturing = False
                
                logger.info("Audio capture stopped successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to stop audio capture: {e}")
                return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get capture status.
        
        Returns:
            Dictionary with current status
        """
        with self.lock:
            return {
                "is_capturing": self.is_capturing,
                "enable_system_audio": self.enable_system_audio,
                "sample_rate": self.sample_rate,
                "chunk_size": self.chunk_size,
                "selected_devices": self.selected_devices
            }
    
    def _start_microphone(self) -> bool:
        """Start microphone capture."""
        try:
            # Determine microphone device
            mic_device = self._get_microphone_device()
            
            def mic_callback(indata, frames, time_info, status):
                try:
                    if status:
                        logger.debug(f"Mic status: {status}")
                    
                    # Store microphone audio (convert to mono if needed)
                    if indata.ndim > 1:
                        audio_data = indata[:, 0]  # Use first channel
                    else:
                        audio_data = indata.flatten()
                    
                    # Ensure correct size
                    if len(audio_data) >= self.chunk_size:
                        self.mic_buffer = audio_data[:self.chunk_size].astype(np.float32)
                    else:
                        # Pad if needed
                        self.mic_buffer[:len(audio_data)] = audio_data.astype(np.float32)
                        self.mic_buffer[len(audio_data):] = 0
                    
                    # Process combined audio
                    self._process_combined_audio()
                    
                except Exception as e:
                    logger.error(f"Error in mic callback: {e}")
            
            # Create microphone stream
            self.mic_stream = sd.InputStream(
                device=mic_device,
                channels=1,
                samplerate=self.sample_rate,
                blocksize=self.chunk_size,
                dtype=np.float32,
                callback=mic_callback
            )
            
            self.mic_stream.start()
            logger.info(f"Microphone started: device={mic_device}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start microphone: {e}")
            return False
    
    def _start_system_audio(self) -> bool:
        """Start system audio capture using WASAPI loopback at 48kHz."""
        try:
            # Check if pyaudiowpatch is available
            if not PYAUDIO_AVAILABLE:
                logger.warning("pyaudiowpatch not available, system audio not supported")
                return False
            
            # Find WASAPI loopback device
            loopback_device = self._find_loopback_device()
            if not loopback_device:
                logger.warning("No WASAPI loopback device found")
                return False
            
            # Get the actual device sample rate instead of assuming 48kHz
            pa = pyaudio.PyAudio()
            device_info = pa.get_device_info_by_index(loopback_device['index'])
            self.system_sample_rate = int(device_info.get('defaultSampleRate', 48000))
            
            # Calculate chunk size based on actual sample rate
            self.system_chunk_size = int(self.system_sample_rate * 0.1)  # 100ms chunks
            self.resample_ratio = self.system_sample_rate / self.sample_rate  # Actual ratio
            
            logger.info(f"System audio device sample rate: {self.system_sample_rate}Hz, chunk size: {self.system_chunk_size}")
            
            def system_callback(in_data, frame_count, time_info, status_flags):
                try:
                    # Convert bytes to numpy array at device's native sample rate
                    audio_data = np.frombuffer(in_data, dtype=np.float32)
                    
                    # Handle stereo to mono conversion
                    if len(audio_data) >= self.system_chunk_size * 2:  # Stereo
                        stereo_data = audio_data.reshape(-1, 2)
                        # Mix both channels instead of just taking left
                        mono_data = (stereo_data[:, 0] + stereo_data[:, 1]) / 2.0
                    else:
                        # Mono data
                        mono_data = audio_data
                    
                    # Add to resample buffer
                    self.system_resample_buffer.extend(mono_data[:self.system_chunk_size])
                    
                    # Resample to target sample rate when we have enough data
                    self._resample_system_audio()
                    
                    return (None, pyaudio.paContinue)
                    
                except Exception as e:
                    logger.error(f"Error in system callback: {e}")
                    return (None, pyaudio.paContinue)
            
            # Create system audio stream at device's native sample rate
            self.system_stream = pa.open(
                format=pyaudio.paFloat32,
                channels=2,  # System audio is usually stereo
                rate=self.system_sample_rate,  # Use device's actual sample rate
                input=True,
                input_device_index=loopback_device['index'],
                frames_per_buffer=self.system_chunk_size,  # Chunk size based on actual sample rate
                stream_callback=system_callback
            )
            
            self.system_stream.start_stream()
            logger.info(f"System audio started: device={loopback_device['name']} at {self.system_sample_rate}Hz")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start system audio: {e}")
            return False
    
    def _process_combined_audio(self) -> None:
        """Process and combine microphone and system audio with per-source VAD analysis."""
        try:
            if not self.chunk_callback:
                return
            
            # Apply VAD to individual sources
            self.mic_vad_result = self.mic_vad.process_chunk(self.mic_buffer)
            
            if self.enable_system_audio and self.system_stream:
                self.system_vad_result = self.system_vad.process_chunk(self.system_buffer)
            else:
                # No system audio, set default result
                self.system_vad_result = {"is_speech": False, "probability": 0.0}
            
            # Create comprehensive VAD info
            vad_info = {
                "mic_has_speech": self.mic_vad_result.get("is_speech", False),
                "system_has_speech": self.system_vad_result.get("is_speech", False),
                "mic_probability": self.mic_vad_result.get("probability", 0.0),
                "system_probability": self.system_vad_result.get("probability", 0.0),
                "mic_confidence": self.mic_vad_result.get("confidence", "low"),
                "system_confidence": self.system_vad_result.get("confidence", "low"),
                "mic_threshold": self.mic_vad_result.get("adaptive_threshold", 0.5),
                "system_threshold": self.system_vad_result.get("adaptive_threshold", 0.4),
                "vad_enabled": self.mic_vad_result.get("vad_enabled", True)
            }
            
            # Calculate current peak levels for monitoring
            mic_peak = np.max(np.abs(self.mic_buffer))
            system_peak = 0.0
            
            if self.enable_system_audio and self.system_stream:
                system_peak = np.max(np.abs(self.system_buffer))
                # Simple combination without gain adjustment
                combined_audio = self.mic_buffer + self.system_buffer
            else:
                # Only microphone active
                combined_audio = self.mic_buffer.copy()
            
            # Send to callback with comprehensive VAD information
            timestamp = time.time()
            self.chunk_callback(combined_audio, timestamp, vad_info)
            
        except Exception as e:
            logger.error(f"Failed to process combined audio: {e}")
    
    def _resample_system_audio(self) -> None:
        """Resample system audio from device sample rate to target sample rate using linear interpolation."""
        try:
            # Need enough samples for resampling (one complete chunk)
            if len(self.system_resample_buffer) < self.system_chunk_size:
                return
            
            # Extract exactly one chunk at device sample rate
            source_samples = np.array(self.system_resample_buffer[:self.system_chunk_size])
            
            # Remove processed samples from buffer
            self.system_resample_buffer = self.system_resample_buffer[self.system_chunk_size:]
            
            # Only resample if sample rates are different
            if self.system_sample_rate != self.sample_rate:
                # Use proper linear interpolation resampling
                ratio = self.sample_rate / self.system_sample_rate
                new_length = int(len(source_samples) * ratio)
                
                # Create indices for interpolation
                source_indices = np.arange(len(source_samples))
                target_indices = np.linspace(0, len(source_samples) - 1, new_length)
                
                # Perform linear interpolation
                resampled_samples = np.interp(target_indices, source_indices, source_samples)
            else:
                # No resampling needed
                resampled_samples = source_samples
            
            # Ensure we have exactly the right number of samples for target chunk size
            if len(resampled_samples) >= self.chunk_size:
                self.system_buffer[:] = resampled_samples[:self.chunk_size].astype(np.float32)
            else:
                # Pad if needed
                self.system_buffer[:len(resampled_samples)] = resampled_samples.astype(np.float32)
                self.system_buffer[len(resampled_samples):] = 0
                
        except Exception as e:
            logger.error(f"Error resampling system audio: {e}")
            # Clear buffer to prevent buildup
            self.system_resample_buffer.clear()

    
    def _get_microphone_device(self) -> Optional[int]:
        """Get the microphone device ID to use."""
        try:
            if not self.selected_devices:
                return None  # Use default
            
            input_info = self.selected_devices.get("input", {})
            if input_info.get("use_default", True):
                return None  # Use default
            
            device_id = input_info.get("device_id")
            if device_id:
                return int(device_id)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting microphone device: {e}")
            return None
    
    def _find_loopback_device(self) -> Optional[Dict[str, Any]]:
        """Find WASAPI loopback device for system audio capture based on selected output device."""
        try:
            if not PYAUDIO_AVAILABLE:
                return None
            
            pa = pyaudio.PyAudio()
            
            # Get WASAPI host API info
            wasapi_info = pa.get_host_api_info_by_type(pyaudio.paWASAPI)
            
            # Determine which output device to use for system audio capture
            target_output_device = None
            
            # Check if user has selected a specific output device
            if self.selected_devices:
                output_info = self.selected_devices.get("output", {})
                if not output_info.get("use_default", True):
                    output_device_id = output_info.get("device_id")
                    if output_device_id:
                        try:
                            target_output_device = pa.get_device_info_by_index(int(output_device_id))
                            logger.info(f"Using selected output device for system audio: {target_output_device['name']}")
                        except (ValueError, TypeError) as e:
                            logger.warning(f"Invalid output device ID {output_device_id}, using default: {e}")
            
            # Fall back to default output device if no specific device selected
            if not target_output_device:
                try:
                    target_output_device = pa.get_device_info_by_index(wasapi_info['defaultOutputDevice'])
                    logger.info(f"Using default output device for system audio: {target_output_device['name']}")
                except Exception as e:
                    logger.error(f"Could not get default output device: {e}")
                    return None
            
            # Find the corresponding loopback device
            loopback_device = None
            
            # If the target device is already a loopback device, use it directly
            if target_output_device.get('isLoopbackDevice', False):
                loopback_device = target_output_device
            else:
                # Find the loopback device with matching name and host API
                target_name = target_output_device['name']
                target_hostapi = target_output_device['hostApi']
                
                for i in range(pa.get_device_count()):
                    device_info = pa.get_device_info_by_index(i)
                    
                    # Check if this is a loopback device with matching name and host API
                    if (device_info.get('isLoopbackDevice', False) and
                        device_info['name'] == target_name and
                        device_info['hostApi'] == target_hostapi):
                        loopback_device = device_info
                        loopback_device['index'] = i  # Add the index for stream creation
                        break
            
            if loopback_device:
                logger.info(f"Found loopback device: {loopback_device['name']}")
                return {
                    'index': loopback_device.get('index', loopback_device.get('id')),
                    'name': loopback_device['name'],
                    'channels': loopback_device['maxInputChannels']
                }
            else:
                logger.warning(f"No WASAPI loopback device found for output device: {target_output_device['name']}")
                
                # Fallback: find any WASAPI loopback device
                logger.info("Falling back to any available WASAPI loopback device")
                for i in range(pa.get_device_count()):
                    device_info = pa.get_device_info_by_index(i)
                    device_name = device_info.get('name', '').lower()
                    
                    # Check if it's a loopback device
                    if 'loopback' in device_name or device_info.get('isLoopbackDevice', False):
                        logger.info(f"Using fallback loopback device: {device_info['name']}")
                        return {
                            'index': i,
                            'name': device_info['name'],
                            'channels': device_info['maxInputChannels']
                        }
                
                logger.warning("No WASAPI loopback device found at all")
                return None
            
        except Exception as e:
            logger.error(f"Error finding loopback device: {e}")
            return None
    
    def _cleanup_streams(self) -> None:
        """Cleanup audio streams."""
        try:
            # Stop and close microphone stream
            if self.mic_stream:
                try:
                    self.mic_stream.stop()
                    self.mic_stream.close()
                except Exception as e:
                    logger.error(f"Error stopping microphone stream: {e}")
                finally:
                    self.mic_stream = None
            
            # Stop and close system audio stream
            if self.system_stream:
                try:
                    self.system_stream.stop_stream()
                    self.system_stream.close()
                except Exception as e:
                    logger.error(f"Error stopping system stream: {e}")
                finally:
                    self.system_stream = None
            
            # Clear buffers
            self.mic_buffer.fill(0)
            self.system_buffer.fill(0)
            self.system_resample_buffer.clear()
            
        except Exception as e:
            logger.error(f"Error during stream cleanup: {e}")
    
    def cleanup_vad(self) -> None:
        """Cleanup VAD instances."""
        try:
            if self.mic_vad:
                self.mic_vad.cleanup()
            if self.system_vad:
                self.system_vad.cleanup()
            logger.debug("VAD instances cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up VAD: {e}") 