'use client';

import React, { useRef, useState, useEffect, KeyboardEvent, ClipboardEvent, ChangeEvent, FocusEvent } from 'react';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface OTPInputProps {
  length?: number;
  value: string;
  onChange: (value: string) => void;
  autoFocus?: boolean;
  disabled?: boolean;
  className?: string;
}

/**
 * OTP Input component that displays multiple input boxes for entering verification codes
 */
export function OTPInput({
  length = 6,
  value,
  onChange,
  autoFocus = false,
  disabled = false,
  className,
}: OTPInputProps) {
  // Store a local representation of the OTP digits
  const [digits, setDigits] = useState<string[]>(Array(length).fill(''));
  
  // Create refs for all the input elements
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);
  
  // Initialize refs array when component mounts or length changes
  useEffect(() => {
    inputRefs.current = inputRefs.current.slice(0, length);
  }, [length]);

  // Sync internal state with external value
  useEffect(() => {
    updateDigitsFromValue(value);
  }, [value, length]);

  // Handle autofocus
  useEffect(() => {
    if (autoFocus && !disabled && inputRefs.current[0]) {
      // Focus the first empty input or the first input if all are filled
      const firstEmptyIndex = digits.findIndex(d => !d);
      const focusIndex = firstEmptyIndex === -1 ? 0 : firstEmptyIndex;
      inputRefs.current[focusIndex]?.focus();
    }
  }, [autoFocus, disabled, digits]);

  /**
   * Updates the internal digit array from an external value string
   */
  const updateDigitsFromValue = (newValue: string) => {
    const valueArray = newValue.split('').slice(0, length);
    const newDigits = Array(length).fill('');
    
    valueArray.forEach((digit, index) => {
      newDigits[index] = digit;
    });
    
    setDigits(newDigits);
  };

  /**
   * Updates the external value based on the internal digits array
   */
  const updateValue = (newDigits: string[]) => {
    setDigits(newDigits);
    onChange(newDigits.join(''));
  };

  /**
   * Handles changes to individual input fields
   */
  const handleChange = (e: ChangeEvent<HTMLInputElement>, index: number) => {
    const inputValue = e.target.value;
    
    // Only accept digits (empty is allowed for clearing)
    if (inputValue && !/^\d+$/.test(inputValue)) return;
    
    // Handle cases where multiple characters might be input (like pasting into a field)
    const inputCharacters = inputValue.split('');
    
    if (inputCharacters.length <= 1) {
      // Normal single-digit input
      const newDigits = [...digits];
      newDigits[index] = inputValue;
      updateValue(newDigits);
      
      // Auto-advance to next input if we entered a digit
      if (inputValue && index < length - 1) {
        inputRefs.current[index + 1]?.focus();
      }
    } else {
      // Handle multi-character input (like pasting into a single field)
      const availableSlots = length - index;
      const charsToUse = inputCharacters.slice(0, availableSlots);
      
      const newDigits = [...digits];
      charsToUse.forEach((char, i) => {
        if (i + index < length) {
          newDigits[i + index] = char;
        }
      });
      
      updateValue(newDigits);
      
      // Focus the next empty slot or the last slot
      const nextEmptyIndex = newDigits.findIndex((d, i) => i >= index && !d);
      const focusIndex = nextEmptyIndex === -1 ? length - 1 : nextEmptyIndex;
      inputRefs.current[focusIndex]?.focus();
    }
  };

  /**
   * Handles key presses for navigation and deletion
   */
  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number) => {
    switch (e.key) {
      case 'Backspace':
        if (digits[index]) {
          // If there's a value in the current input, clear it
          const newDigits = [...digits];
          newDigits[index] = '';
          updateValue(newDigits);
        } else if (index > 0) {
          // If current input is empty, focus and clear previous input
          const newDigits = [...digits];
          newDigits[index - 1] = '';
          updateValue(newDigits);
          inputRefs.current[index - 1]?.focus();
        }
        break;
        
      case 'Delete':
        // Clear the current input
        const newDigits = [...digits];
        newDigits[index] = '';
        updateValue(newDigits);
        break;
        
      case 'ArrowLeft':
        // Move focus to previous input
        if (index > 0) {
          inputRefs.current[index - 1]?.focus();
        }
        break;
        
      case 'ArrowRight':
        // Move focus to next input
        if (index < length - 1) {
          inputRefs.current[index + 1]?.focus();
        }
        break;
        
      default:
        // For digit keys, we let the onChange handler deal with it
        break;
    }
  };

  /**
   * Handles pasting of values
   */
  const handlePaste = (e: ClipboardEvent<HTMLInputElement>, index: number) => {
    e.preventDefault();
    if (disabled) return;
    
    const pastedData = e.clipboardData.getData('text/plain').trim();
    if (!pastedData) return;
    
    // Only accept digits
    const pastedDigits = pastedData.split('').filter(char => /\d/.test(char));
    if (pastedDigits.length === 0) return;
    
    // Create new digits array with pasted data starting from current index
    const newDigits = [...digits];
    
    pastedDigits.forEach((digit, i) => {
      const targetIndex = index + i;
      if (targetIndex < length) {
        newDigits[targetIndex] = digit;
      }
    });
    
    // Update the value
    updateValue(newDigits);
    
    // Focus the next empty input or the last input
    const nextEmptyIndex = newDigits.findIndex((d, i) => i >= index && !d);
    const focusIndex = nextEmptyIndex === -1 ? length - 1 : nextEmptyIndex;
    setTimeout(() => {
      inputRefs.current[focusIndex]?.focus();
    }, 0);
  };

  /**
   * Handle clicking on an input - select all text in it
   */
  const handleFocus = (e: FocusEvent<HTMLInputElement>) => {
    e.target.select();
  };

  return (
    <div className={cn("flex justify-center items-center gap-3 w-full", className)}>
      {Array.from({ length }, (_, index) => (
        <div key={index} className="relative">
          <Input
            ref={(el) => {
              inputRefs.current[index] = el;
            }}
            type="text"
            inputMode="numeric"
            pattern="[0-9]*"
            maxLength={1}
            className={cn(
              "w-14 h-14 text-center text-xl font-semibold rounded-md p-0 border-2",
              "focus:border-primary focus:ring-1 focus:ring-primary",
              "transition-all duration-150",
              digits[index] ? "border-primary/70 bg-primary/5" : ""
            )}
            value={digits[index] || ''}
            onChange={(e) => handleChange(e, index)}
            onKeyDown={(e) => handleKeyDown(e, index)}
            onPaste={(e) => handlePaste(e, index)}
            onFocus={handleFocus}
            autoComplete={index === 0 ? "one-time-code" : "off"}
            disabled={disabled}
            aria-label={`Digit ${index + 1}`}
          />
        </div>
      ))}
    </div>
  );
} 