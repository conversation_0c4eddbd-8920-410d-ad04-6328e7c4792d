"""
Transcription Inference Engine

Handles Whisper model interaction and audio-to-text inference.
"""

import logging
import numpy as np
from typing import Optional

from .transcription_types import TranscriptionResult

logger = logging.getLogger(__name__)


class TranscriptionInferenceEngine:
    """Handles Whisper model inference for transcription."""
    
    def __init__(self, target_sample_rate: int = 16000):
        """Initialize the inference engine.
        
        Args:
            target_sample_rate: Target sample rate for audio processing
        """
        self.target_sample_rate = target_sample_rate
        logger.debug("TranscriptionInferenceEngine initialized")
    
    async def run_whisper_inference(self, audio_data: np.ndarray, model, processor, loaded_model) -> Optional[TranscriptionResult]:
        """Run Whisper model inference on preprocessed audio.
        
        Args:
            audio_data: Preprocessed audio samples
            model: Whisper model object
            processor: Whisper processor object
            loaded_model: LoadedModel metadata
            
        Returns:
            TranscriptionResult or None if inference failed
        """
        try:
            # Import torch dynamically
            import torch
            
            # Process audio with Whisper processor
            inputs = processor(
                audio_data,
                sampling_rate=self.target_sample_rate,
                return_tensors="pt",
                padding=True,
                return_attention_mask=True
            )
            
            # Validate processor output
            if not hasattr(inputs, 'input_features'):
                logger.error("Processor didn't return input_features")
                return None
            
            # Get features from processor - already in correct format [batch, 80, seq_len]
            input_features = inputs.input_features
            attention_mask = inputs.attention_mask if hasattr(inputs, 'attention_mask') else None
            
            # Debug: Log input shape for verification
            logger.info(f"Input features shape: {input_features.shape}")
            logger.info(f"Input features dtype: {input_features.dtype}")
            logger.info(f"Audio data shape that was processed: {audio_data.shape}")
            logger.info(f"Audio data dtype that was processed: {audio_data.dtype}")
            
            # Validate input_features shape: should be [batch, 80, seq_len]
            if len(input_features.shape) != 3 or input_features.shape[1] != 80:
                logger.error(f"Invalid input_features shape: {input_features.shape}, expected [batch, 80, seq_len]")
                logger.error(f"This is a critical error - processor should output [batch, 80, seq_len] format")
                return None
            
            # Move to model device and dtype
            model_device = loaded_model.device
            model_dtype = getattr(loaded_model, 'dtype', 'float32')
            
            # Convert dtype string to torch dtype
            dtype_map = {
                'float32': torch.float32,
                'float16': torch.float16,
                'half': torch.float16,
                'bfloat16': torch.bfloat16
            }
            torch_dtype = dtype_map.get(model_dtype, torch.float32)
            
            input_features = input_features.to(device=model_device, dtype=torch_dtype)
            if attention_mask is not None:
                attention_mask = attention_mask.to(device=model_device)
            
            # Prepare generation config
            generation_config = {
                "task": "transcribe",
                "return_timestamps": False,
                "language": "en",  # Can be made configurable
                "forced_decoder_ids": None
            }
            
            if attention_mask is not None:
                generation_config["attention_mask"] = attention_mask
            
            # Run inference
            with torch.no_grad():
                transcribed_ids = model.generate(input_features, **generation_config)
            
            # Decode result
            if transcribed_ids is None or len(transcribed_ids) == 0:
                logger.debug("Model generated empty transcription")
                return None
            
            # Decode transcription
            transcribed_text = processor.batch_decode(transcribed_ids, skip_special_tokens=True)[0]
            
            # Skip empty or whitespace-only results
            if not transcribed_text or not transcribed_text.strip():
                logger.debug("Transcription result is empty")
                return None
            
            # Calculate confidence (placeholder - Whisper doesn't provide direct confidence)
            confidence = 0.8 if transcribed_text.strip() else 0.0
            
            return TranscriptionResult(
                text=transcribed_text.strip(),
                confidence=confidence,
                processing_time_ms=0.0,  # Will be set by caller
                language="en",  # Auto-detected by Whisper
                raw_text=transcribed_text
            )
            
        except Exception as e:
            logger.error(f"Error in Whisper inference: {e}", exc_info=True)
            return None 