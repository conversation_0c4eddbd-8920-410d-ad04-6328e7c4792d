import { BaseEntity } from '../../database/repositories/base';

// Simplified VoiceModel that matches what Python actually provides
export interface VoiceModel extends BaseEntity {
  id: string;
  name: string;
  provider: string; // 'openai', 'distil-whisper', etc.
  type: 'local' | 'cloud';
  
  // Status (synced from Python ModelService)
  status: {
    isDownloaded: boolean;
    isLoaded: boolean;
    isAvailable: boolean;
  };
}

// AI Models are not implemented in Python backend yet - removing for now
// When AI processing is added to Python, we can add this back
// export interface AIModel extends BaseEntity { ... }

// Simplified ModelSettings that only tracks what's actually used
export interface ModelSettings extends BaseEntity {
  id: string;
  activeVoiceModelId: string;
  defaultVoiceModelId: string;
  // Note: AI models not implemented in Python backend yet
  // activeAIModelId?: string;
  
  // Available models (synced from Python)
  availableVoiceModels: VoiceModel[];
}

// NOTE: No DEFAULT_VOICE_MODELS - Python backend is the source of truth
// Voice models are loaded from Python via handle_get_all_models in model_handlers.py
// The Python model registry contains the real model definitions with download URLs, sizes, etc.

// Simplified default settings (with empty models array - will be populated from Python)
export const DEFAULT_MODEL_SETTINGS: ModelSettings = {
  id: 'default-model-settings',
  activeVoiceModelId: 'openai/whisper-base.en', // Default fallback
  defaultVoiceModelId: 'openai/whisper-base.en',
  availableVoiceModels: [] // Will be populated from Python backend
}; 