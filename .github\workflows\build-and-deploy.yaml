name: Build and Deploy One Whispr (PowerShell Fixed)

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      build_electron:
        description: 'Build Electron App'
        required: false
        default: false
        type: boolean
      force_python_deploy:
        description: 'Force deploy Python artifacts (runtime + scripts)'
        required: false
        default: false
        type: boolean
      force_scripts_only:
        description: 'Force deploy scripts only (quick update)'
        required: false
        default: false
        type: boolean
      rollback_to_version:
        description: 'Rollback to specific version (e.g., 1.2.1)'
        required: false
        type: string
      emergency_rollback:
        description: 'Emergency rollback to previous version'
        required: false
        default: false
        type: boolean
      clean_python_cache:
        description: 'Clean Python cache and reinstall (use sparingly)'
        required: false
        default: false
        type: boolean

env:
  # Define paths for Python cache persistence (using Windows temp directory for self-hosted runner)
  PYTHON_CACHE_DIR: C:/temp/github-actions-cache/python-cache
  PYTHON_VENV_DIR: C:/temp/github-actions-cache/python-venv
  NODE_MODULES_CACHE: C:/temp/github-actions-cache/node-modules-cache

jobs:
  # Build and deploy site on self-hosted Windows runner
  build-and-deploy-site:
    runs-on: self-hosted
    if: github.event.inputs.rollback_to_version == '' && github.event.inputs.emergency_rollback != 'true' && !contains(github.event.head_commit.message, '[skip]')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check for site changes
        id: site-changes
        run: |
          if ("${{ github.event_name }}" -eq "workflow_dispatch") {
            Write-Host "Manual workflow dispatch - will build site"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "site-changed=true"
          } else {
            # Check if one.whispr-site folder changed
            $siteFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-site/'
            if ($siteFiles) {
              Write-Host "Site files changed - will build and deploy site"
              Add-Content -Path $env:GITHUB_OUTPUT -Value "site-changed=true"
            } else {
              Write-Host "No site files changed - skipping site build"
              Add-Content -Path $env:GITHUB_OUTPUT -Value "site-changed=false"
            }
          }
        shell: powershell

      - name: Setup Node.js
        if: steps.site-changes.outputs.site-changed == 'true'
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: 'one.whispr-site/package-lock.json'

      - name: Cache Node modules for site
        if: steps.site-changes.outputs.site-changed == 'true'
        run: |
          $cacheDir = "${{ env.NODE_MODULES_CACHE }}/site"
          $targetDir = "one.whispr-site/node_modules"
          
          if ((Test-Path $cacheDir) -and (Test-Path "$cacheDir/package-lock.json")) {
            $cachedLock = Get-Content "$cacheDir/package-lock.json" -Raw -ErrorAction SilentlyContinue
            $currentLock = Get-Content "one.whispr-site/package-lock.json" -Raw -ErrorAction SilentlyContinue
            
            if ($cachedLock -eq $currentLock) {
              Write-Host "Restoring cached node_modules..."
              if (Test-Path $targetDir) { Remove-Item $targetDir -Recurse -Force }
              Copy-Item "$cacheDir/node_modules" $targetDir -Recurse -Force
              Write-Host "Cache restored successfully"
              Add-Content -Path $env:GITHUB_ENV -Value "CACHE_RESTORED=true"
            } else {
              Write-Host "Package-lock.json changed, cache invalidated"
              Add-Content -Path $env:GITHUB_ENV -Value "CACHE_RESTORED=false"
            }
          } else {
            Write-Host "No cache found"
            Add-Content -Path $env:GITHUB_ENV -Value "CACHE_RESTORED=false"
          }
        shell: powershell

      - name: Install dependencies
        if: steps.site-changes.outputs.site-changed == 'true' && env.CACHE_RESTORED != 'true'
        run: |
          cd one.whispr-site
          npm ci --prefer-offline --no-audit
        shell: powershell

      - name: Save Node modules cache
        if: steps.site-changes.outputs.site-changed == 'true' && env.CACHE_RESTORED != 'true'
        run: |
          $cacheDir = "${{ env.NODE_MODULES_CACHE }}/site"
          New-Item -ItemType Directory -Path $cacheDir -Force
          Copy-Item "one.whispr-site/node_modules" "$cacheDir/node_modules" -Recurse -Force
          Copy-Item "one.whispr-site/package-lock.json" "$cacheDir/package-lock.json" -Force
          Write-Host "Node modules cached successfully"
        shell: powershell

      - name: Apply database migrations
        if: steps.site-changes.outputs.site-changed == 'true'
        run: |
          cd one.whispr-site
          npx tsx scripts/apply-migrations.ts
        shell: powershell
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}

      - name: Build site
        if: steps.site-changes.outputs.site-changed == 'true'
        run: |
          cd one.whispr-site
          npm run build
        shell: powershell
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_SITE_URL: ${{ secrets.NEXT_PUBLIC_SITE_URL }}

      - name: Install SSH key
        if: steps.site-changes.outputs.site-changed == 'true'
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Deploy site to VPS using tar+SSH (more reliable)
        if: steps.site-changes.outputs.site-changed == 'true'
        run: |
          # Test SSH connection first
          echo "Testing SSH connection..."
          ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "echo 'SSH connection successful'"
          
          # Create target directory structure on VPS
          ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "mkdir -p ${{ secrets.DEPLOY_PATH }}/{.next,public,scripts,src/migrations}"
          
          # Function to deploy directory contents with retries using tar
          deploy_dir_with_retry() {
            local source_dir="$1"
            local target_dir="$2"
            local max_retries=3
            local retry_count=0
            
            if [ ! -d "$source_dir" ]; then
              echo "⚠️ Source directory $source_dir does not exist, skipping..."
              return 0
            fi
            
            while [ $retry_count -lt $max_retries ]; do
              retry_count=$((retry_count + 1))
              echo "Deploying $source_dir contents (attempt $retry_count of $max_retries)..."
              
              if tar -czf - -C "$source_dir" . | ssh -o ConnectTimeout=60 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd $target_dir && tar -xzf -"; then
                echo "✅ Successfully deployed $source_dir"
                return 0
              else
                echo "❌ Failed to deploy $source_dir"
                if [ $retry_count -lt $max_retries ]; then
                  echo "⏳ Waiting 10 seconds before retry..."
                  sleep 10
                fi
              fi
            done
            
            echo "❌ Failed to deploy $source_dir after $max_retries attempts"
            return 1
          }
          
          # Function to deploy single files with retries
          deploy_file_with_retry() {
            local source_file="$1"
            local target_path="$2"
            local max_retries=3
            local retry_count=0
            
            if [ ! -f "$source_file" ]; then
              echo "⚠️ Source file $source_file does not exist, skipping..."
              return 0
            fi
            
            while [ $retry_count -lt $max_retries ]; do
              retry_count=$((retry_count + 1))
              echo "Deploying $source_file (attempt $retry_count of $max_retries)..."
              
              if scp -o ConnectTimeout=60 -o StrictHostKeyChecking=no "$source_file" "${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:$target_path"; then
                echo "✅ Successfully deployed $source_file"
                return 0
              else
                echo "❌ Failed to deploy $source_file"
                if [ $retry_count -lt $max_retries ]; then
                  echo "⏳ Waiting 10 seconds before retry..."
                  sleep 10
                fi
              fi
            done
            
            echo "❌ Failed to deploy $source_file after $max_retries attempts"
            return 1
          }
          
          # Deploy directories (using tar for efficiency)
          deploy_dir_with_retry "one.whispr-site/.next" "${{ secrets.DEPLOY_PATH }}/.next"
          deploy_dir_with_retry "one.whispr-site/public" "${{ secrets.DEPLOY_PATH }}/public"
          deploy_dir_with_retry "one.whispr-site/scripts" "${{ secrets.DEPLOY_PATH }}/scripts"
          deploy_dir_with_retry "one.whispr-site/src/migrations" "${{ secrets.DEPLOY_PATH }}/src/migrations"
          
          # Deploy individual files
          deploy_file_with_retry "one.whispr-site/package.json" "${{ secrets.DEPLOY_PATH }}/"
          deploy_file_with_retry "one.whispr-site/package-lock.json" "${{ secrets.DEPLOY_PATH }}/"
          deploy_file_with_retry "one.whispr-site/next.config.ts" "${{ secrets.DEPLOY_PATH }}/"
          
          echo "✅ All site files deployed successfully"
        shell: bash

      - name: Deploy utility scripts to VPS
        if: steps.site-changes.outputs.site-changed == 'true'
        run: |
          scp -o ConnectTimeout=30 -o StrictHostKeyChecking=no .github/scripts/rollback.sh ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }}:${{ secrets.DEPLOY_PATH }}/scripts/
          ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "chmod +x ${{ secrets.DEPLOY_PATH }}/scripts/rollback.sh"
        shell: bash
          
      - name: Restart site application
        if: steps.site-changes.outputs.site-changed == 'true'
        run: |
          ssh -o ConnectTimeout=60 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'ENDSSH'
            cd ${{ secrets.DEPLOY_PATH }}
            echo "NEXT_PUBLIC_SUPABASE_URL=${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}" > .env.production
            echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}" >> .env.production
            echo "NEXT_PUBLIC_SITE_URL=${{ secrets.NEXT_PUBLIC_SITE_URL }}" >> .env.production
            echo "SUPABASE_SERVICE_ROLE_KEY=${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}" >> .env.production
            npm install --production
            npx tsx scripts/apply-migrations.ts
            pm2 restart one-whispr-site || pm2 start npm --name "one-whispr-site" -- start
          ENDSSH
        shell: bash

  # Build and deploy app on self-hosted Windows runner
  build-and-deploy-app:
    runs-on: self-hosted
    if: (github.event.inputs.build_electron == 'true' || contains(github.event.head_commit.message, '[build-electron]')) && github.event.inputs.rollback_to_version == '' && github.event.inputs.emergency_rollback != 'true' && !contains(github.event.head_commit.message, '[skip]')
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Debug environment and test PowerShell syntax
        run: |
          Write-Host "Current directory: $(Get-Location)"
          Write-Host "Node version: $(node --version)"
          Write-Host "NPM version: $(npm --version)"
          Write-Host "PowerShell version: $($PSVersionTable.PSVersion)"
          Write-Host "Available disk space:"
          Get-WmiObject -Class Win32_LogicalDisk | Select-Object DeviceID, @{Name="Size(GB)";Expression={[math]::Round($_.Size/1GB,2)}}, @{Name="FreeSpace(GB)";Expression={[math]::Round($_.FreeSpace/1GB,2)}}
          Write-Host "Python cache directory: ${{ env.PYTHON_CACHE_DIR }}"
          Write-Host "Python venv directory: ${{ env.PYTHON_VENV_DIR }}"
          
          # Test PowerShell environment variable syntax
          Write-Host "Testing PowerShell env variable syntax..."
          Add-Content -Path $env:GITHUB_ENV -Value "TEST_VAR=powershell_syntax_works"
          Write-Host "PowerShell syntax test completed"
        shell: powershell

      - name: Cache Node modules for main app (Fixed PowerShell)
        run: |
          $cacheDir = "${{ env.NODE_MODULES_CACHE }}/main-app"
          $targetDir = "one.whispr-app/node_modules"
          
          Write-Host "Cache directory: $cacheDir"
          Write-Host "Target directory: $targetDir"
          
          if ((Test-Path $cacheDir) -and (Test-Path "$cacheDir/package-lock.json")) {
            $cachedLock = Get-Content "$cacheDir/package-lock.json" -Raw -ErrorAction SilentlyContinue
            $currentLock = Get-Content "one.whispr-app/package-lock.json" -Raw -ErrorAction SilentlyContinue
            
            if ($cachedLock -eq $currentLock) {
              Write-Host "Restoring cached node_modules..."
              if (Test-Path $targetDir) { Remove-Item $targetDir -Recurse -Force }
              Copy-Item "$cacheDir/node_modules" $targetDir -Recurse -Force
              Write-Host "Cache restored successfully"
              Add-Content -Path $env:GITHUB_ENV -Value "MAIN_APP_CACHE_RESTORED=true"
            } else {
              Write-Host "Package-lock.json changed, cache invalidated"
              Add-Content -Path $env:GITHUB_ENV -Value "MAIN_APP_CACHE_RESTORED=false"
            }
          } else {
            Write-Host "No cache found"
            Add-Content -Path $env:GITHUB_ENV -Value "MAIN_APP_CACHE_RESTORED=false"
          }
        shell: powershell

      - name: Install dependencies for main app
        if: env.MAIN_APP_CACHE_RESTORED != 'true'
        run: |
          Set-Location one.whispr-app
          Write-Host "Installing dependencies in: $(Get-Location)"
          if (Test-Path "package-lock.json") {
            Write-Host "package-lock.json found, running npm ci"
            npm ci --verbose --no-audit --prefer-offline
          } else {
            Write-Host "No package-lock.json found, running npm install"
            npm install --verbose --no-audit
          }
          if ($LASTEXITCODE -ne 0) {
            Write-Error "npm install failed with exit code $LASTEXITCODE"
            exit $LASTEXITCODE
          }
        shell: powershell

      - name: Save Node modules cache for main app
        if: env.MAIN_APP_CACHE_RESTORED != 'true'
        run: |
          $cacheDir = "${{ env.NODE_MODULES_CACHE }}/main-app"
          New-Item -ItemType Directory -Path $cacheDir -Force
          Copy-Item "one.whispr-app/node_modules" "$cacheDir/node_modules" -Recurse -Force
          Copy-Item "one.whispr-app/package-lock.json" "$cacheDir/package-lock.json" -Force
          Write-Host "Node modules cached successfully"
        shell: powershell

      - name: Check for Python file changes
        id: python-changes
        run: |
          $mainWhisprChanged = $false
          $depsChanged = $false
          $forceClean = '${{ github.event.inputs.clean_python_cache }}' -eq 'true'
          $forcePythonDeploy = '${{ github.event.inputs.force_python_deploy }}' -eq 'true'
          $forceScriptsOnly = '${{ github.event.inputs.force_scripts_only }}' -eq 'true'

          Write-Host "Force clean cache: $forceClean"
          Write-Host "Force python deploy: $forcePythonDeploy"
          Write-Host "Force scripts only: $forceScriptsOnly"

          if ($forceClean) {
            Write-Host "Force clean requested - will rebuild Python environment"
            $mainWhisprChanged = $true
            $depsChanged = $true
            Add-Content -Path $env:GITHUB_OUTPUT -Value "main-whispr-changed=true"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "deps-changed=true"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "python-changed=true"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "force-clean=true"
          } elseif ($forcePythonDeploy) {
            Write-Host "Force Python deploy requested - will rebuild both runtime and scripts"
            $mainWhisprChanged = $true
            $depsChanged = $true
            Add-Content -Path $env:GITHUB_OUTPUT -Value "main-whispr-changed=true"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "deps-changed=true"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "python-changed=true"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "force-clean=false"
          } elseif ($forceScriptsOnly) {
            Write-Host "Force scripts only requested - will rebuild only scripts"
            $mainWhisprChanged = $true
            $depsChanged = $false
            Add-Content -Path $env:GITHUB_OUTPUT -Value "main-whispr-changed=true"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "deps-changed=false"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "python-changed=true"
            Add-Content -Path $env:GITHUB_OUTPUT -Value "force-clean=false"
          } else {
            # Check if main.py or whispr folder changed (quick update)
            $mainWhisprFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-app/python/main\.py$|^one\.whispr-app/python/whispr/'
            if ($mainWhisprFiles) {
              $mainWhisprChanged = $true
              Add-Content -Path $env:GITHUB_OUTPUT -Value "main-whispr-changed=true"
              Write-Host "Main.py or whispr folder changed - quick update needed"
            } else {
              Add-Content -Path $env:GITHUB_OUTPUT -Value "main-whispr-changed=false"
            }

            # Check if dependencies or spec files changed (full rebuild)
            $depsFiles = git diff --name-only ${{ github.event.before }}..${{ github.sha }} | Select-String -Pattern '^one\.whispr-app/python/.*\.spec$|^one\.whispr-app/scripts/backend-setup\.ts$|^one\.whispr-app/python/requirements\.txt$|^one\.whispr-app/package\.json$'
            if ($depsFiles) {
              $depsChanged = $true
              Add-Content -Path $env:GITHUB_OUTPUT -Value "deps-changed=true"
              Write-Host "Dependencies or build configuration changed - full rebuild needed"
            } else {
              Add-Content -Path $env:GITHUB_OUTPUT -Value "deps-changed=false"
            }

            # Set overall python-changed flag
            if (($mainWhisprChanged) -or ($depsChanged)) {
              Add-Content -Path $env:GITHUB_OUTPUT -Value "python-changed=true"
              Write-Host "Python files have changed, will rebuild"
            } else {
              Add-Content -Path $env:GITHUB_OUTPUT -Value "python-changed=false"
              Write-Host "No Python files changed, skipping Python build"
            }
            
            Add-Content -Path $env:GITHUB_OUTPUT -Value "force-clean=false"
          }
        shell: powershell

      - name: Clean Python cache if requested
        if: steps.python-changes.outputs.force-clean == 'true'
        run: |
          Write-Host "Cleaning Python cache directories..."
          
          # Remove Python cache directories
          $dirs = @("${{ env.PYTHON_CACHE_DIR }}", "${{ env.PYTHON_VENV_DIR }}")
          foreach ($dir in $dirs) {
            if (Test-Path $dir) {
              Remove-Item $dir -Recurse -Force
              Write-Host "Removed: $dir"
            }
          }
          
          # Also clean the project's Python directory
          $projectPythonDir = "one.whispr-app/python/.python"
          if (Test-Path $projectPythonDir) {
            Remove-Item $projectPythonDir -Recurse -Force
            Write-Host "Removed: $projectPythonDir"
          }
          
          Write-Host "Python cache cleaned"
        shell: powershell

      - name: Cache and restore Python installation
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          $pythonCacheDir = "${{ env.PYTHON_CACHE_DIR }}"
          $projectPythonDir = "one.whispr-app/python/.python"
          $forceClean = '${{ steps.python-changes.outputs.force-clean }}' -eq 'true'
          
          # Create cache directory if it doesn't exist
          if (-not (Test-Path $pythonCacheDir)) {
            New-Item -ItemType Directory -Path $pythonCacheDir -Force
          }
          
          # Check if we have a cached Python installation
          if ((Test-Path "$pythonCacheDir/python.exe") -and (-not $forceClean)) {
            Write-Host "Restoring Python from cache..."
            
            # Remove existing Python directory
            if (Test-Path $projectPythonDir) {
              Remove-Item $projectPythonDir -Recurse -Force
            }
            
            # Create project Python directory
            New-Item -ItemType Directory -Path $projectPythonDir -Force
            
            # Copy cached Python installation
            Copy-Item "$pythonCacheDir/*" $projectPythonDir -Recurse -Force
            
            Write-Host "Python restored from cache"
            Add-Content -Path $env:GITHUB_ENV -Value "PYTHON_CACHE_RESTORED=true"
          } else {
            Write-Host "No cached Python found or force clean requested"
            Add-Content -Path $env:GITHUB_ENV -Value "PYTHON_CACHE_RESTORED=false"
          }
        shell: powershell

      - name: Setup Python environment
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          Set-Location one.whispr-app
          npm run backend-setup
        shell: powershell

      - name: Cache Python installation
        if: steps.python-changes.outputs.python-changed == 'true' && env.PYTHON_CACHE_RESTORED != 'true'
        run: |
          $pythonCacheDir = "${{ env.PYTHON_CACHE_DIR }}"
          $projectPythonDir = "one.whispr-app/python/.python"
          
          if (Test-Path $projectPythonDir) {
            Write-Host "Caching Python installation..."
            
            # Remove existing cache
            if (Test-Path $pythonCacheDir) {
              Remove-Item $pythonCacheDir -Recurse -Force
            }
            
            # Create cache directory
            New-Item -ItemType Directory -Path $pythonCacheDir -Force
            
            # Copy Python installation to cache
            Copy-Item "$projectPythonDir/*" $pythonCacheDir -Recurse -Force
            
            Write-Host "Python installation cached"
          }
        shell: powershell

      - name: Install dependencies
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          Set-Location one.whispr-app
          npm install --verbose --no-audit
          if ($LASTEXITCODE -ne 0) {
            Write-Error "npm install failed with exit code $LASTEXITCODE"
            exit $LASTEXITCODE
          }
        shell: powershell

      - name: Restore cached backend files
        if: steps.python-changes.outputs.python-changed == 'true'
        run: |
          $backendCacheDir = "${{ env.PYTHON_CACHE_DIR }}/backend-compiled"
          $backendTargetDir = "one.whispr-app/.dist/One Whispr Backend"
          $forceClean = '${{ steps.python-changes.outputs.force-clean }}' -eq 'true'
          $depsChanged = '${{ steps.python-changes.outputs.deps-changed }}' -eq 'true'

          # Only restore cache if dependencies haven't changed and not force cleaning
          if ((Test-Path $backendCacheDir) -and (-not $depsChanged) -and (-not $forceClean)) {
            Write-Host "Restoring cached backend files..."

            # Create target directory
            New-Item -ItemType Directory -Path $backendTargetDir -Force

            # Copy cached backend files
            Copy-Item "$backendCacheDir/*" $backendTargetDir -Recurse -Force

            Write-Host "Backend files restored from cache"
            Write-Host "Restored files:"
            Get-ChildItem $backendTargetDir -Recurse | ForEach-Object { Write-Host "  $($_.FullName)" }
            Add-Content -Path $env:GITHUB_ENV -Value "BACKEND_CACHE_RESTORED=true"
          } else {
            if ($depsChanged) {
              Write-Host "Dependencies changed - backend cache invalidated"
            } elseif ($forceClean) {
              Write-Host "Force clean requested - skipping backend cache"
            } else {
              Write-Host "No cached backend files found"
            }
            Add-Content -Path $env:GITHUB_ENV -Value "BACKEND_CACHE_RESTORED=false"
          }
        shell: powershell

      - name: Compile Python backend (Full rebuild)
        if: steps.python-changes.outputs.deps-changed == 'true' && env.BACKEND_CACHE_RESTORED != 'true'
        run: |
          Set-Location one.whispr-app
          npm run backend:compile
        shell: powershell

      - name: Quick update (bytecode compilation only)
        if: steps.python-changes.outputs.main-whispr-changed == 'true' && steps.python-changes.outputs.deps-changed == 'false' && env.BACKEND_CACHE_RESTORED != 'true'
        run: |
          Set-Location one.whispr-app
          npm run backend:compile:quick
        shell: powershell

      - name: Cache compiled backend files
        if: steps.python-changes.outputs.python-changed == 'true' && env.BACKEND_CACHE_RESTORED != 'true'
        run: |
          $backendCacheDir = "${{ env.PYTHON_CACHE_DIR }}/backend-compiled"
          $backendSourceDir = "one.whispr-app/.dist/One Whispr Backend"

          if (Test-Path $backendSourceDir) {
            Write-Host "Caching compiled backend files..."

            # Remove existing cache
            if (Test-Path $backendCacheDir) {
              Remove-Item $backendCacheDir -Recurse -Force
            }

            # Create cache directory
            New-Item -ItemType Directory -Path $backendCacheDir -Force

            # Copy compiled backend files to cache
            Copy-Item "$backendSourceDir/*" $backendCacheDir -Recurse -Force

            Write-Host "Backend files cached successfully"
            Write-Host "Cached files:"
            Get-ChildItem $backendCacheDir -Recurse | ForEach-Object { Write-Host "  $($_.FullName)" }
          } else {
            Write-Host "No compiled backend files found to cache"
          }
        shell: powershell

      - name: Build main Electron app to individual files
        run: |
          Set-Location one.whispr-app
          npm run build:dist
        shell: powershell

      - name: Cache Node modules for setup
        run: |
          $cacheDir = "${{ env.NODE_MODULES_CACHE }}/setup"
          $targetDir = "one.whispr-setup/node_modules"
          
          if ((Test-Path $cacheDir) -and (Test-Path "$cacheDir/package-lock.json")) {
            $cachedLock = Get-Content "$cacheDir/package-lock.json" -Raw -ErrorAction SilentlyContinue
            $currentLock = Get-Content "one.whispr-setup/package-lock.json" -Raw -ErrorAction SilentlyContinue
            
            if ($cachedLock -eq $currentLock) {
              Write-Host "Restoring cached setup node_modules..."
              if (Test-Path $targetDir) { Remove-Item $targetDir -Recurse -Force }
              Copy-Item "$cacheDir/node_modules" $targetDir -Recurse -Force
              Write-Host "Setup cache restored successfully"
              Add-Content -Path $env:GITHUB_ENV -Value "SETUP_CACHE_RESTORED=true"
            } else {
              Write-Host "Setup package-lock.json changed, cache invalidated"
              Add-Content -Path $env:GITHUB_ENV -Value "SETUP_CACHE_RESTORED=false"
            }
          } else {
            Write-Host "No setup cache found"
            Add-Content -Path $env:GITHUB_ENV -Value "SETUP_CACHE_RESTORED=false"
          }
        shell: powershell

      - name: Install dependencies for setup
        if: env.SETUP_CACHE_RESTORED != 'true'
        run: |
          Set-Location one.whispr-setup
          Write-Host "Installing setup dependencies in: $(Get-Location)"
          if (Test-Path "package-lock.json") {
            npm ci --verbose --no-audit --prefer-offline
          } else {
            npm install --verbose --no-audit
          }
          if ($LASTEXITCODE -ne 0) {
            Write-Error "npm install failed with exit code $LASTEXITCODE"
            exit $LASTEXITCODE
          }
        shell: powershell

      - name: Save Node modules cache for setup
        if: env.SETUP_CACHE_RESTORED != 'true'
        run: |
          $cacheDir = "${{ env.NODE_MODULES_CACHE }}/setup"
          New-Item -ItemType Directory -Path $cacheDir -Force
          Copy-Item "one.whispr-setup/node_modules" "$cacheDir/node_modules" -Recurse -Force
          Copy-Item "one.whispr-setup/package-lock.json" "$cacheDir/package-lock.json" -Force
          Write-Host "Setup node modules cached successfully"
        shell: powershell

      - name: Remove Scripts.7z from main app (not needed in Microsoft Store)
        run: |
          Set-Location one.whispr-app
          Remove-Item ".release/win-unpacked/resources/backend/OneWhispr-Scripts.7z" -Force -ErrorAction SilentlyContinue
        shell: powershell

      - name: Build setup/installer (Microsoft Store)
        run: |
          Set-Location one.whispr-setup
          npm run build:dist:microsoft
        shell: powershell
        env:
          IS_MICROSOFT: true
          UPDATE_SERVER_URL: https://whispr.one/updates

      - name: Remove remaining 7z files from main app for direct distribution
        run: |
          Set-Location one.whispr-app
          Remove-Item ".release/win-unpacked/resources/backend/OneWhispr-Runtime-Base.7z" -Force -ErrorAction SilentlyContinue
          Remove-Item ".release/win-unpacked/resources/backend/msstore.json" -Force -ErrorAction SilentlyContinue
        shell: powershell

      - name: Build setup/installer (Direct distribution)
        run: |
          Set-Location one.whispr-setup
          npm run build:dist:direct
        shell: powershell
        env:
          UPDATE_SERVER_URL: https://whispr.one/updates

      - name: Get version for deployment
        id: get-version
        run: |
          Set-Location one.whispr-setup
          $VERSION = node -p "require('./package.json').version"
          "version=$VERSION" | Out-File -FilePath $env:GITHUB_OUTPUT -Append
          Write-Host "Version: $VERSION"
        shell: powershell

      # Use SCP instead of rsync for Windows compatibility
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Prepare deployment structure
        run: |
          $VERSION = "${{ steps.get-version.outputs.version }}"
          $PYTHON_CHANGED = "${{ steps.python-changes.outputs.python-changed }}"
          $MAIN_WHISPR_CHANGED = "${{ steps.python-changes.outputs.main-whispr-changed }}"
          $DEPS_CHANGED = "${{ steps.python-changes.outputs.deps-changed }}"

          Write-Host "Deploying version: $VERSION"

          # Create directory structure for setup installer
          New-Item -ItemType Directory -Path "updates/setup/versions/$VERSION" -Force
          New-Item -ItemType Directory -Path "updates/setup/latest" -Force

          # Copy setup files (only installer, nupkg, and RELEASES - exclude Squirrel internals)
          if (Test-Path "one.whispr-setup/.release-direct") {
            # Copy only the installer executable (not Squirrel internals like 7za.exe, Squirrel.exe, etc.)
            Get-ChildItem -Path "one.whispr-setup/.release-direct" -Recurse -Include "OneWhisprSetup.exe", "*.nupkg", "RELEASES" | ForEach-Object {
              Copy-Item -Path $_.FullName -Destination "updates/setup/versions/$VERSION/" -Force
              Copy-Item -Path $_.FullName -Destination "updates/setup/latest/" -Force
            }
            Write-Host "Setup installer files copied (OneWhisprSetup.exe, .nupkg, RELEASES)"
          }

          # Handle Python artifacts based on what changed
          if ($DEPS_CHANGED -eq 'true') {
            Write-Host "Full rebuild - deploying base runtime and scripts"
            New-Item -ItemType Directory -Path "updates/backend-runtime/versions/$VERSION" -Force
            New-Item -ItemType Directory -Path "updates/backend-runtime/latest" -Force
            New-Item -ItemType Directory -Path "updates/backend-scripts/versions/$VERSION" -Force
            New-Item -ItemType Directory -Path "updates/backend-scripts/latest" -Force

            if (Test-Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z") {
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z" -Destination "updates/backend-runtime/versions/$VERSION/" -Force
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Runtime-Base.7z" -Destination "updates/backend-runtime/latest/" -Force
            }
            
            if (Test-Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z") {
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" -Destination "updates/backend-scripts/versions/$VERSION/" -Force
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" -Destination "updates/backend-scripts/latest/" -Force
            }

            New-Item -ItemType File -Path "updates/RUNTIME_DEPLOYED" -Force
            New-Item -ItemType File -Path "updates/SCRIPTS_DEPLOYED" -Force
          } elseif ($MAIN_WHISPR_CHANGED -eq 'true') {
            Write-Host "Quick update - deploying only scripts"
            New-Item -ItemType Directory -Path "updates/backend-scripts/versions/$VERSION" -Force
            New-Item -ItemType Directory -Path "updates/backend-scripts/latest" -Force

            if (Test-Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z") {
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" -Destination "updates/backend-scripts/versions/$VERSION/" -Force
              Copy-Item -Path "one.whispr-app/.dist/One Whispr Backend/OneWhispr-Scripts.7z" -Destination "updates/backend-scripts/latest/" -Force
            }

            New-Item -ItemType File -Path "updates/SCRIPTS_DEPLOYED" -Force
          }

          # Always deploy main app
          New-Item -ItemType Directory -Path "updates/main-app/versions/$VERSION" -Force
          New-Item -ItemType Directory -Path "updates/main-app/latest" -Force
          
          if (Test-Path "one.whispr-app/.release/win-unpacked") {
            Copy-Item -Path "one.whispr-app/.release/win-unpacked/*" -Destination "updates/main-app/versions/$VERSION/" -Recurse -Force
            Copy-Item -Path "one.whispr-app/.release/win-unpacked/*" -Destination "updates/main-app/latest/" -Recurse -Force
          }
        shell: powershell

      - name: Create JSON manifest
        run: |
          $VERSION = "${{ steps.get-version.outputs.version }}"
          
          # Create JSON manifest for the setup app
          Set-Location "updates/main-app/versions/$VERSION"
          
          $files = @()
          $totalSize = 0
          
          Get-ChildItem -Path . -Recurse -File | ForEach-Object {
            $relativePath = $_.FullName.Replace((Get-Location).Path, "").TrimStart('\').Replace('\', '/')
            $size = $_.Length
            $checksum = (Get-FileHash -Path $_.FullName -Algorithm SHA256).Hash.ToLower()
            $baseUrl = "https://whispr.one/updates/main-app/latest"
            
            $files += @{
              path = "./$relativePath"
              size = $size
              checksum = $checksum
              url = "$baseUrl/$relativePath"
            }
            
            $totalSize += $size
          }
          
          $manifest = @{
            version = $VERSION
            files = $files
            totalSize = $totalSize
          }
          
          $manifest | ConvertTo-Json -Depth 3 | Out-File -FilePath "../../../manifest-$VERSION.json" -Encoding UTF8
          
          Set-Location ../../../..
          Copy-Item -Path "updates/manifest-$VERSION.json" -Destination "updates/main-app/latest/manifest.json" -Force
        shell: powershell

      - name: Deploy to VPS using tar+SSH (reliable and efficient)
        run: |
          # Test SSH connection first
          echo "Testing SSH connection..."
          ssh -o ConnectTimeout=10 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "echo 'SSH connection successful'"
          
          # Create updates directory on VPS
          ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "mkdir -p /var/www/html/updates"
          
          # Function to deploy directory with retries
          deploy_dir_with_retry() {
            local source_dir="$1"
            local target_dir="$2"
            local max_retries=3
            local retry_count=0
            
            if [ ! -d "$source_dir" ]; then
              echo "⚠️ Source directory $source_dir does not exist, skipping..."
              return 0
            fi
            
            while [ $retry_count -lt $max_retries ]; do
              retry_count=$((retry_count + 1))
              echo "Deploying $source_dir to $target_dir (attempt $retry_count of $max_retries)..."
              
              # Create target directory on VPS
              ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "mkdir -p $target_dir"
              
              # Use tar to compress and transfer (more efficient than scp for large directories)
              if tar -czf - -C "$source_dir" . | ssh -o ConnectTimeout=60 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd $target_dir && tar -xzf -"; then
                echo "✅ Successfully deployed $source_dir"
                return 0
              else
                echo "❌ Failed to deploy $source_dir"
                if [ $retry_count -lt $max_retries ]; then
                  echo "⏳ Waiting 15 seconds before retry..."
                  sleep 15
                fi
              fi
            done
            
            echo "❌ Failed to deploy $source_dir after $max_retries attempts"
            return 1
          }
          
          # Deploy updates directory (this contains all the built files)
          if [ -d "updates" ]; then
            echo "📦 Deploying updates directory..."
            deploy_dir_with_retry "updates" "/var/www/html/updates"
            echo "✅ Updates directory deployed successfully"
          else
            echo "❌ Updates directory not found"
            exit 1
          fi
        shell: bash

      - name: Update version info and cleanup on VPS
        run: |
          ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} << 'ENDSSH'
            cd /var/www/html/updates
            
            # Extract version from uploaded files
            VERSION=$(ls setup/versions/ | sort -V | tail -1)
            
            # Create version.json for setup installer
            cat > setup/version.json << EOF
          {
            "version": "$VERSION",
            "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "downloadUrl": "https://whispr.one/updates/setup/latest/OneWhisprSetup.exe",
            "releaseNotes": "Latest version of OneWhispr Setup",
            "deltaUpdatesUrl": "https://whispr.one/updates/setup/versions/$VERSION/"
          }
          EOF
            
            # Create latest.yml for electron-updater
            if [ -f "setup/latest/OneWhisprSetup.exe" ]; then
              SETUP_FILE_SIZE=$(stat -c%s setup/latest/OneWhisprSetup.exe)
              SETUP_SHA512=$(sha512sum setup/latest/OneWhisprSetup.exe | cut -d' ' -f1)
              cat > setup/latest.yml << EOF
          version: $VERSION
          files:
            - url: OneWhisprSetup.exe
              sha512: $SETUP_SHA512
              size: $SETUP_FILE_SIZE
          path: OneWhisprSetup.exe
          sha512: $SETUP_SHA512
          releaseDate: $(date -u +%Y-%m-%dT%H:%M:%SZ)
          EOF
            fi
            
            # Create version.json for main app updates
            cat > main-app/version.json << EOF
          {
            "version": "$VERSION",
            "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "releaseNotes": "Latest version of One Whispr",
            "downloadUrl": "https://whispr.one/updates/main-app/latest/",
            "manifestUrl": "https://whispr.one/updates/main-app/latest/manifest.json",
            "versionsUrl": "https://whispr.one/updates/main-app/versions/$VERSION/"
          }
          EOF
            
            # Create version files for backend components if they were deployed
            if [ -f "RUNTIME_DEPLOYED" ]; then
              echo "Creating backend runtime version file..."
              if [ -f "backend-runtime/latest/OneWhispr-Runtime-Base.7z" ]; then
                RUNTIME_CHECKSUM=$(sha256sum backend-runtime/latest/OneWhispr-Runtime-Base.7z | cut -d' ' -f1)
                cat > backend-runtime/runtime-version.json << EOF
          {
            "version": "$VERSION",
            "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "releaseNotes": "Backend runtime base package",
            "downloadUrl": "https://whispr.one/updates/backend-runtime/latest/OneWhispr-Runtime-Base.7z",
            "versionsUrl": "https://whispr.one/updates/backend-runtime/versions/$VERSION/",
            "compressionType": "7z-lzma2-ultra",
            "checksum": "$RUNTIME_CHECKSUM"
          }
          EOF
              fi
              rm -f "RUNTIME_DEPLOYED"
            fi
            
            if [ -f "SCRIPTS_DEPLOYED" ]; then
              echo "Creating backend scripts version file..."
              if [ -f "backend-scripts/latest/OneWhispr-Scripts.7z" ]; then
                SCRIPTS_CHECKSUM=$(sha256sum backend-scripts/latest/OneWhispr-Scripts.7z | cut -d' ' -f1)
                cat > backend-scripts/scripts-version.json << EOF
          {
            "version": "$VERSION",
            "releaseDate": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "releaseNotes": "Backend scripts bytecode update",
            "downloadUrl": "https://whispr.one/updates/backend-scripts/latest/OneWhispr-Scripts.7z",
            "versionsUrl": "https://whispr.one/updates/backend-scripts/versions/$VERSION/",
            "updateType": "bytecode",
            "compressionType": "7z",
            "checksum": "$SCRIPTS_CHECKSUM"
          }
          EOF
              fi
              rm -f "SCRIPTS_DEPLOYED"
            fi
            
            # Keep only last 5 versions (cleanup)
            cd setup/versions && ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            cd ../../main-app/versions && ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            
            if [ -d "../../backend-runtime/versions" ]; then
              cd ../../backend-runtime/versions && ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            fi
            
            if [ -d "../../backend-scripts/versions" ]; then
              cd ../../backend-scripts/versions && ls -1 | sort -V | head -n -5 | xargs -r rm -rf
            fi
            
            echo "Deployment completed successfully!"
          ENDSSH
        shell: bash

  # Rollback functionality
  rollback:
    runs-on: self-hosted
    if: github.event.inputs.rollback_to_version != '' || github.event.inputs.emergency_rollback == 'true'

    steps:
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          known_hosts: ${{ secrets.SSH_KNOWN_HOSTS }}

      - name: Execute rollback
        run: |
          if [ "${{ github.event.inputs.emergency_rollback }}" = "true" ]; then
            echo "Performing emergency rollback to previous version..."
            ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd ${{ secrets.DEPLOY_PATH }}/scripts && ./rollback.sh --emergency"
          else
            echo "Rolling back to version ${{ github.event.inputs.rollback_to_version }}..."
            ssh -o ConnectTimeout=30 -o StrictHostKeyChecking=no ${{ secrets.SSH_USER }}@${{ secrets.SSH_HOST }} "cd ${{ secrets.DEPLOY_PATH }}/scripts && ./rollback.sh --version ${{ github.event.inputs.rollback_to_version }}"
          fi
        shell: bash