"""
Model validation system for One.Whispr voice models.

This module provides comprehensive validation for downloaded voice models,
including file format support, integrity checking, and completeness validation.
"""

import os
import json
import hashlib
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from .model_registry import ModelRegistry, ModelFile
from .storage_manager import StorageManager

logger = logging.getLogger('whispr.services.model_validator')


class ValidationResult(Enum):
    """Validation result status."""
    VALID = "valid"
    INVALID = "invalid"
    INCOMPLETE = "incomplete"
    CORRUPTED = "corrupted"
    MISSING_FILES = "missing_files"
    FORMAT_ERROR = "format_error"


@dataclass
class ValidationError:
    """Represents a validation error."""
    file_path: str
    error_type: ValidationResult
    message: str
    suggestion: Optional[str] = None


@dataclass
class ValidationReport:
    """Comprehensive validation report for a model."""
    model_id: str
    status: ValidationResult
    errors: List[ValidationError]
    validated_files: List[str]
    missing_files: List[str]
    format_info: Dict[str, Any]
    total_size: int
    
    @property
    def is_valid(self) -> bool:
        """Check if the model passed validation."""
        return self.status == ValidationResult.VALID
    
    @property
    def has_errors(self) -> bool:
        """Check if there are validation errors."""
        return len(self.errors) > 0


class ModelValidator:
    """Comprehensive model validation system."""
    
    def __init__(self, storage_manager: StorageManager):
        """Initialize the model validator.
        
        Args:
            storage_manager: Storage manager for file operations
        """
        self.storage_manager = storage_manager
        self.model_registry = ModelRegistry()
        
        # File format preferences (bin preferred as specified)
        self.format_preferences = ['.bin', '.safetensors']
        
        # Required configuration files
        self.required_config_files = ['config.json']
        self.optional_config_files = ['tokenizer.json', 'tokenizer_config.json', 'special_tokens_map.json']
        
        # Supported model file extensions
        self.supported_extensions = {'.bin', '.safetensors'}
    
    async def validate_model(self, model_id: str) -> ValidationReport:
        """Validate a complete model installation.
        
        Args:
            model_id: The model identifier to validate
            
        Returns:
            Comprehensive validation report
        """
        logger.info(f"Starting validation for model: {model_id}")
        
        # Get model metadata
        model_metadata = self.model_registry.get_model(model_id)
        if not model_metadata:
            return ValidationReport(
                model_id=model_id,
                status=ValidationResult.INVALID,
                errors=[ValidationError(
                    file_path="",
                    error_type=ValidationResult.INVALID,
                    message=f"Model '{model_id}' not found in registry",
                    suggestion="Check model ID and registry configuration"
                )],
                validated_files=[],
                missing_files=[],
                format_info={},
                total_size=0
            )
        
        model_dir = self.storage_manager.get_model_directory(model_id)
        if not model_dir.exists():
            return ValidationReport(
                model_id=model_id,
                status=ValidationResult.MISSING_FILES,
                errors=[ValidationError(
                    file_path=str(model_dir),
                    error_type=ValidationResult.MISSING_FILES,
                    message=f"Model directory does not exist: {model_dir}",
                    suggestion="Download the model first"
                )],
                validated_files=[],
                missing_files=[f.name for f in model_metadata.files],
                format_info={},
                total_size=0
            )
        
        errors = []
        validated_files = []
        missing_files = []
        total_size = 0
        format_info = {}
        
        # Validate model files
        model_file_errors, model_validated, model_missing, model_size, model_formats = await self._validate_model_files(
            model_dir, model_metadata.files
        )
        errors.extend(model_file_errors)
        validated_files.extend(model_validated)
        missing_files.extend(model_missing)
        total_size += model_size
        format_info.update(model_formats)
        
        # Validate configuration files
        config_errors, config_validated, config_missing = await self._validate_config_files(model_dir)
        errors.extend(config_errors)
        validated_files.extend(config_validated)
        missing_files.extend(config_missing)
        
        # Determine overall status
        if missing_files:
            status = ValidationResult.INCOMPLETE
        elif any(error.error_type == ValidationResult.CORRUPTED for error in errors):
            status = ValidationResult.CORRUPTED
        elif any(error.error_type == ValidationResult.FORMAT_ERROR for error in errors):
            status = ValidationResult.FORMAT_ERROR
        elif errors:
            status = ValidationResult.INVALID
        else:
            status = ValidationResult.VALID
        
        report = ValidationReport(
            model_id=model_id,
            status=status,
            errors=errors,
            validated_files=validated_files,
            missing_files=missing_files,
            format_info=format_info,
            total_size=total_size
        )
        
        logger.info(f"Validation completed for {model_id}: {status.value}")
        return report
    
    async def _validate_model_files(
        self, 
        model_dir: Path, 
        expected_files: List[ModelFile]
    ) -> Tuple[List[ValidationError], List[str], List[str], int, Dict[str, Any]]:
        """Validate model files against expected files.
        
        Args:
            model_dir: Model directory path
            expected_files: List of expected model files
            
        Returns:
            Tuple of (errors, validated_files, missing_files, total_size, format_info)
        """
        errors = []
        validated_files = []
        missing_files = []
        total_size = 0
        format_info = {}
        
        for expected_file in expected_files:
            file_path = model_dir / expected_file.name
            
            # Check if file exists
            if not file_path.exists():
                # Try alternative formats
                alternative_path = self._find_alternative_format(model_dir, expected_file.name)
                if alternative_path:
                    file_path = alternative_path
                    format_info[expected_file.name] = {
                        'expected': expected_file.name,
                        'actual': alternative_path.name,
                        'format_substitution': True
                    }
                else:
                    missing_files.append(expected_file.name)
                    continue
            
            # Validate file size
            try:
                actual_size = file_path.stat().st_size
                if expected_file.size and abs(actual_size - expected_file.size) > (expected_file.size * 0.01):  # 1% tolerance
                    errors.append(ValidationError(
                        file_path=str(file_path),
                        error_type=ValidationResult.CORRUPTED,
                        message=f"File size mismatch: expected {expected_file.size}, got {actual_size}",
                        suggestion="Re-download the file"
                    ))
                
                total_size += actual_size
                
            except OSError as e:
                errors.append(ValidationError(
                    file_path=str(file_path),
                    error_type=ValidationResult.INVALID,
                    message=f"Cannot access file: {e}",
                    suggestion="Check file permissions and disk health"
                ))
                continue
            
            # Validate file format
            format_error = await self._validate_file_format(file_path)
            if format_error:
                errors.append(format_error)
            else:
                validated_files.append(str(file_path))
                
                # Record format information
                if file_path.suffix in self.supported_extensions:
                    format_info[expected_file.name] = {
                        'format': file_path.suffix,
                        'size': actual_size,
                        'preferred': file_path.suffix == self.format_preferences[0]
                    }
        
        return errors, validated_files, missing_files, total_size, format_info
    
    def _find_alternative_format(self, model_dir: Path, filename: str) -> Optional[Path]:
        """Find alternative file format for a missing file.
        
        Args:
            model_dir: Model directory
            filename: Original filename
            
        Returns:
            Path to alternative format file if found
        """
        base_name = Path(filename).stem
        
        # Try preferred formats in order
        for ext in self.format_preferences:
            alternative_path = model_dir / f"{base_name}{ext}"
            if alternative_path.exists():
                return alternative_path
        
        return None
    
    async def _validate_file_format(self, file_path: Path) -> Optional[ValidationError]:
        """Validate file format and basic structure.
        
        Args:
            file_path: Path to file to validate
            
        Returns:
            ValidationError if validation fails, None if valid
        """
        try:
            if file_path.suffix == '.safetensors':
                return await self._validate_safetensors_file(file_path)
            elif file_path.suffix == '.bin':
                return await self._validate_bin_file(file_path)
            elif file_path.suffix == '.json':
                return await self._validate_json_file(file_path)
            elif file_path.suffix == '.txt':
                return await self._validate_txt_file(file_path)
            else:
                return ValidationError(
                    file_path=str(file_path),
                    error_type=ValidationResult.FORMAT_ERROR,
                    message=f"Unsupported file format: {file_path.suffix}",
                    suggestion="Check if file is corrupted or has wrong extension"
                )
        except Exception as e:
            return ValidationError(
                file_path=str(file_path),
                error_type=ValidationResult.CORRUPTED,
                message=f"Error validating file format: {e}",
                suggestion="File may be corrupted, try re-downloading"
            )
    
    async def _validate_safetensors_file(self, file_path: Path) -> Optional[ValidationError]:
        """Validate safetensors file format.
        
        Args:
            file_path: Path to safetensors file
            
        Returns:
            ValidationError if validation fails, None if valid
        """
        try:
            # Basic safetensors validation - check header
            with open(file_path, 'rb') as f:
                # Read first 8 bytes for header length
                header_length_bytes = f.read(8)
                if len(header_length_bytes) < 8:
                    return ValidationError(
                        file_path=str(file_path),
                        error_type=ValidationResult.CORRUPTED,
                        message="Safetensors file too short to contain valid header",
                        suggestion="Re-download the file"
                    )
                
                # Decode header length
                header_length = int.from_bytes(header_length_bytes, byteorder='little')
                if header_length <= 0 or header_length > 100 * 1024 * 1024:  # Max 100MB header
                    return ValidationError(
                        file_path=str(file_path),
                        error_type=ValidationResult.CORRUPTED,
                        message="Invalid safetensors header length",
                        suggestion="File may be corrupted, re-download"
                    )
                
                # Try to read and parse header JSON
                header_bytes = f.read(header_length)
                if len(header_bytes) < header_length:
                    return ValidationError(
                        file_path=str(file_path),
                        error_type=ValidationResult.CORRUPTED,
                        message="Safetensors header incomplete",
                        suggestion="Re-download the file"
                    )
                
                # Parse header JSON
                header_json = json.loads(header_bytes.decode('utf-8'))
                if not isinstance(header_json, dict):
                    return ValidationError(
                        file_path=str(file_path),
                        error_type=ValidationResult.FORMAT_ERROR,
                        message="Invalid safetensors header format",
                        suggestion="File may be corrupted"
                    )
            
            return None
            
        except json.JSONDecodeError:
            return ValidationError(
                file_path=str(file_path),
                error_type=ValidationResult.FORMAT_ERROR,
                message="Invalid JSON in safetensors header",
                suggestion="File may be corrupted, re-download"
            )
        except Exception as e:
            return ValidationError(
                file_path=str(file_path),
                error_type=ValidationResult.CORRUPTED,
                message=f"Error reading safetensors file: {e}",
                suggestion="File may be corrupted, re-download"
            )
    
    async def _validate_bin_file(self, file_path: Path) -> Optional[ValidationError]:
        """Validate PyTorch .bin file format.
        
        Args:
            file_path: Path to .bin file
            
        Returns:
            ValidationError if validation fails, None if valid
        """
        try:
            # Basic validation - check if file can be opened and has reasonable size
            file_size = file_path.stat().st_size
            if file_size < 1024:  # Less than 1KB is suspicious for a model file
                return ValidationError(
                    file_path=str(file_path),
                    error_type=ValidationResult.CORRUPTED,
                    message="Model file too small to be valid",
                    suggestion="Re-download the file"
                )
            
            # Try to read first few bytes to ensure file is readable
            with open(file_path, 'rb') as f:
                first_bytes = f.read(64)  # Read first 64 bytes
                
                # Check if file is readable and not empty
                if len(first_bytes) == 0:
                    return ValidationError(
                        file_path=str(file_path),
                        error_type=ValidationResult.CORRUPTED,
                        message="Model file appears to be empty",
                        suggestion="Re-download the file"
                    )
                
                # For PyTorch models, we'll do a lenient check
                # Most PyTorch models are pickle files, but formats can vary
                # Just ensure it's not obviously corrupted (all zeros, all 0xFF, etc.)
                if all(b == 0 for b in first_bytes[:32]) or all(b == 0xFF for b in first_bytes[:32]):
                    return ValidationError(
                        file_path=str(file_path),
                        error_type=ValidationResult.CORRUPTED,
                        message="Model file appears to be corrupted (invalid data pattern)",
                        suggestion="Re-download the file"
                    )
            
            return None
            
        except Exception as e:
            return ValidationError(
                file_path=str(file_path),
                error_type=ValidationResult.CORRUPTED,
                message=f"Error reading .bin file: {e}",
                suggestion="File may be corrupted, re-download"
            )
    
    async def _validate_json_file(self, file_path: Path) -> Optional[ValidationError]:
        """Validate JSON configuration file.
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            ValidationError if validation fails, None if valid
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                json.load(f)
            return None
            
        except json.JSONDecodeError as e:
            return ValidationError(
                file_path=str(file_path),
                error_type=ValidationResult.FORMAT_ERROR,
                message=f"Invalid JSON format: {e}",
                suggestion="File may be corrupted, re-download"
            )
        except Exception as e:
            return ValidationError(
                file_path=str(file_path),
                error_type=ValidationResult.CORRUPTED,
                message=f"Error reading JSON file: {e}",
                suggestion="Check file permissions and re-download if needed"
            )
    
    async def _validate_txt_file(self, file_path: Path) -> Optional[ValidationError]:
        """Validate text file format (e.g., merges.txt).
        
        Args:
            file_path: Path to text file
            
        Returns:
            ValidationError if validation fails, None if valid
        """
        try:
            # Check if file exists and is readable
            file_size = file_path.stat().st_size
            if file_size == 0:
                return ValidationError(
                    file_path=str(file_path),
                    error_type=ValidationResult.CORRUPTED,
                    message="Text file is empty",
                    suggestion="Re-download the file"
                )
            
            # Try to read the file as UTF-8 text
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read(1024)  # Read first 1KB to check if it's valid text
                
                # Basic validation - should contain printable text
                if not content.strip():
                    return ValidationError(
                        file_path=str(file_path),
                        error_type=ValidationResult.CORRUPTED,
                        message="Text file contains no readable content",
                        suggestion="Re-download the file"
                    )
            
            return None
            
        except UnicodeDecodeError:
            return ValidationError(
                file_path=str(file_path),
                error_type=ValidationResult.FORMAT_ERROR,
                message="Text file is not valid UTF-8",
                suggestion="File may be corrupted or have wrong encoding"
            )
        except Exception as e:
            return ValidationError(
                file_path=str(file_path),
                error_type=ValidationResult.CORRUPTED,
                message=f"Error reading text file: {e}",
                suggestion="Check file permissions and re-download if needed"
            )
    
    async def _validate_config_files(
        self, 
        model_dir: Path
    ) -> Tuple[List[ValidationError], List[str], List[str]]:
        """Validate configuration files in model directory.
        
        Args:
            model_dir: Model directory path
            
        Returns:
            Tuple of (errors, validated_files, missing_files)
        """
        errors = []
        validated_files = []
        missing_files = []
        
        # Check required config files
        for config_file in self.required_config_files:
            config_path = model_dir / config_file
            if not config_path.exists():
                missing_files.append(config_file)
            else:
                format_error = await self._validate_file_format(config_path)
                if format_error:
                    errors.append(format_error)
                else:
                    validated_files.append(str(config_path))
        
        # Check optional config files (don't mark as missing)
        for config_file in self.optional_config_files:
            config_path = model_dir / config_file
            if config_path.exists():
                format_error = await self._validate_file_format(config_path)
                if format_error:
                    errors.append(format_error)
                else:
                    validated_files.append(str(config_path))
        
        return errors, validated_files, missing_files
    
    async def validate_file_integrity(self, file_path: Path, expected_checksum: Optional[str] = None) -> bool:
        """Validate file integrity using checksum.
        
        Args:
            file_path: Path to file to validate
            expected_checksum: Expected SHA256 checksum (if available)
            
        Returns:
            True if file integrity is valid
        """
        if not expected_checksum:
            return True  # Can't validate without expected checksum
        
        try:
            sha256_hash = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            
            actual_checksum = sha256_hash.hexdigest()
            return actual_checksum.lower() == expected_checksum.lower()
            
        except Exception as e:
            logger.error(f"Error calculating checksum for {file_path}: {e}")
            return False
    
    def get_model_format_info(self, model_id: str) -> Dict[str, Any]:
        """Get format information for a model.
        
        Args:
            model_id: Model identifier
            
        Returns:
            Dictionary with format information
        """
        model_dir = self.storage_manager.get_model_directory(model_id)
        if not model_dir.exists():
            return {}
        
        format_info = {}
        for file_path in model_dir.iterdir():
            if file_path.is_file() and file_path.suffix in self.supported_extensions:
                format_info[file_path.name] = {
                    'format': file_path.suffix,
                    'size': file_path.stat().st_size,
                    'preferred': file_path.suffix == self.format_preferences[0]
                }
        
        return format_info 