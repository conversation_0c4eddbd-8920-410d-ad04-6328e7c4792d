"""
System command handlers for One.Whispr.

This module contains handlers for system-level commands like ping and status.
"""

import logging
from typing import Dict, Any, TYPE_CHECKING

from whispr.core.response_utils import success_response, error_response, ErrorCodes

if TYPE_CHECKING:
    from whispr.core.handlers import CommandHandlers

# Configure logging
logger = logging.getLogger('whispr.handlers.system')


async def handle_ping(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle ping messages from the client.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        The response data
    """
    timestamp = params.get('timestamp', 0)
    logger.debug(f"Received ping with timestamp: {timestamp}")
    return success_response({'timestamp': timestamp}, "Pong")


async def handle_get_status(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle status request from the client.
    
    Args:
        params: The message parameters
        context: The execution context
        
    Returns:
        Status information for all services
    """
    # Get service container from context if available
    service_container = context.get('service_container')
    if service_container:
        # Get actual service statuses
        services = {}
        for service_type in service_container.get_services():
            service = service_container.resolve(service_type)
            if service and hasattr(service, 'get_status'):
                services[service_type] = service.get_status()
        
        return success_response({
            "running": True,
            "services": services,
            "timestamp": params.get('timestamp', 0)
        }, "System status retrieved successfully")
    else:
        return success_response({
            "running": True,
            "timestamp": params.get('timestamp', 0)
        }, "System is running")


def register_handlers(command_handlers: 'CommandHandlers') -> None:
    """Register system handlers with the command handlers.
    
    Args:
        command_handlers: The CommandHandlers instance to register with
    """
    logger.debug("Registering system handlers")
    
    command_handlers.registry.register_function("system.ping", handle_ping)
    command_handlers.registry.register_function("system.status", handle_get_status)
    
    logger.info("System handlers registered successfully") 