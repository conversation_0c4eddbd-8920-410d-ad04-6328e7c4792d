import { BaseEntity } from '../../database/repositories/base';

/**
 * App configuration and setup state entity
 */
export interface AppEntity extends BaseEntity {
  id: string;
  version: string;
  firstRun: boolean;
  setupCompleted: boolean;
  lastUsed: string;
  currentSetupStep?: string; // Only if setup not completed
}

/**
 * Setup steps for the 5-step flow
 */
export const SETUP_STEPS = [
  'auth',
  'models', 
  'audio',
  'shortcuts',
  'tryit'
] as const;

export type SetupStep = typeof SETUP_STEPS[number];

/**
 * Default app entity values
 */
export const DEFAULT_APP_ENTITY: Omit<AppEntity, 'id'> = {
  version: '1.0.0',
  firstRun: true,
  setupCompleted: false,
  lastUsed: new Date().toISOString(),
  currentSetupStep: 'auth'
};

// === AUTHENTICATION TYPES ===

/**
 * User entity interface
 */
export interface User extends BaseEntity {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  avatarUrl?: string;
  providerId?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Session entity interface
 */
export interface UserSession extends BaseEntity {
  id: string;
  userId: string;
  accessToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresAt: string;
  scopes?: string[];
  deviceInfo?: {
    platform: string;
    userAgent?: string;
    deviceId: string;
  };
  isActive: boolean;
  lastAccessedAt: string;
  createdAt: string;
}

/**
 * Auth settings for per-user configurations that sync across devices
 */
export interface AuthSettings extends BaseEntity {
  id: string;
  userId: string;
  autoLogin: boolean;
  rememberDevice: boolean;
  sessionTimeout: number; // in minutes
  biometricEnabled: boolean;
  lastSyncedAt?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * OAuth callback data interface
 */
export interface AuthCallbackData {
  token: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
}