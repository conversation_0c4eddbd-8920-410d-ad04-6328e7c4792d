'use client';

import { IoArrowBack } from 'react-icons/io5';
import { Button } from '@/components/ui/button';

interface BackButtonProps {
  onClick: () => void;
  disabled?: boolean;
}

/**
 * A reusable back button for authentication flows
 */
export function BackButton({ onClick, disabled = false }: BackButtonProps) {
  return (
    <Button 
      type="button" 
      variant="ghost" 
      className="h-8 w-8 p-0" 
      onClick={onClick}
      disabled={disabled}
    >
      <IoArrowBack className="h-4 w-4" />
      <span className="sr-only">Back</span>
    </Button>
  );
} 