import { getDatabase } from '../core/connection';

/**
 * Schema for database table
 */
export interface TableSchema {
  name: string;
  createStatement: string;
}

/**
 * Migration interface for schema versioning
 */
export interface Migration {
  version: number;
  up: string;  // SQL to run when upgrading
  down: string; // SQL to run when downgrading
}

/**
 * Extended table schema with migration support
 */
export interface SchemaWithMigrations extends TableSchema {
  migrations?: Migration[];
}

// Store all schemas
const schemas: Record<string, SchemaWithMigrations> = {};

/**
 * Register a schema with migrations
 */
export function registerSchema(schema: SchemaWithMigrations): void {
  schemas[schema.name] = schema;
}

/**
 * Initialize all registered schemas
 */
export function initializeSchemas(): void {
  const db = getDatabase();
  
  // Create migration tracking table
  db.exec(`
    CREATE TABLE IF NOT EXISTS _schema_migrations (
      table_name TEXT NOT NULL,
      version INTEGER NOT NULL,
      applied_at TEXT NOT NULL,
      PRIMARY KEY (table_name, version)
    );
  `);
  
  // Initialize each table
  Object.values(schemas).forEach(schema => {
    // Create the table
    db.exec(schema.createStatement);
    
    // Apply migrations
    if (schema.migrations && schema.migrations.length > 0) {
      applyMigrations(schema.name);
    }
  });
}

/**
 * Get the current schema version for a table
 */
function getCurrentVersion(tableName: string): number {
  const db = getDatabase();
  const query = `
    SELECT MAX(version) as version FROM _schema_migrations
    WHERE table_name = ?
  `;
  
  const result = db.prepare(query).get(tableName) as any;
  return result && result.version ? result.version : 0;
}

/**
 * Apply all pending migrations for a table
 */
export function applyMigrations(tableName: string): void {
  const schema = schemas[tableName];
  if (!schema || !schema.migrations || schema.migrations.length === 0) {
    return;
  }
  
  const db = getDatabase();
  const currentVersion = getCurrentVersion(tableName);
  
  // Find migrations to apply
  const pendingMigrations = schema.migrations
    .filter(m => m.version > currentVersion)
    .sort((a, b) => a.version - b.version);
  
  if (pendingMigrations.length === 0) {
    return;
  }
  
  // Apply each migration in a transaction
  db.transaction(() => {
    pendingMigrations.forEach(migration => {
      // Apply the migration
      db.exec(migration.up);
      
      // Record that we applied it
      db.prepare(`
        INSERT INTO _schema_migrations (table_name, version, applied_at)
        VALUES (?, ?, datetime('now'))
      `).run(tableName, migration.version);
    });
  })();
}

/**
 * Roll back migrations to a specific version
 */
export function rollbackToVersion(tableName: string, targetVersion: number): void {
  const schema = schemas[tableName];
  if (!schema || !schema.migrations) {
    return;
  }
  
  const db = getDatabase();
  const currentVersion = getCurrentVersion(tableName);
  
  if (currentVersion <= targetVersion) {
    return;
  }
  
  // Find migrations to roll back (in reverse order)
  const migrationsToRollback = schema.migrations
    .filter(m => m.version > targetVersion && m.version <= currentVersion)
    .sort((a, b) => b.version - a.version);  // Descending order
  
  if (migrationsToRollback.length === 0) {
    return;
  }
  
  // Roll back each migration in a transaction
  db.transaction(() => {
    migrationsToRollback.forEach(migration => {
      console.log(`Rolling back migration v${migration.version} from ${tableName}`);
      
      // Apply the down migration
      db.exec(migration.down);
      
      // Remove migration record
      db.prepare(`
        DELETE FROM _schema_migrations
        WHERE table_name = ? AND version = ?
      `).run(tableName, migration.version);
    });
  })();
}

/**
 * Get a registered schema
 */
export function getSchema(tableName: string): SchemaWithMigrations | undefined {
  return schemas[tableName];
}

/**
 * Get all registered schemas
 */
export function getAllSchemas(): Record<string, SchemaWithMigrations> {
  return { ...schemas };
} 