import { app, shell } from 'electron';
import * as fs from 'fs-extra';
import * as path from 'path';
import { getLauncherWindow, closeLauncherWindow } from '../../window';
import { ProcessChecker } from './processChecker';
import { LaunchPathResolver } from './launchPathResolver';

/**
 * Service for actually launching the main app and handling communication
 */
export class MainAppLauncher {
  private processChecker: ProcessChecker;
  private pathResolver: LaunchPathResolver;
  private launchTimeout: NodeJS.Timeout | null = null;
  private isLaunching: boolean = false;

  constructor(processChecker: ProcessChecker, pathResolver: LaunchPathResolver) {
    this.processChecker = processChecker;
    this.pathResolver = pathResolver;
  }

  /**
   * Launch the main app using Electron's shell API
   */
  public async launchMainApp(): Promise<boolean> {
    // Prevent double launches
    if (this.isLaunching) {
      console.log('[MAIN_APP_LAUNCHER] Launch already in progress, ignoring duplicate request');
      return true;
    }

    try {
      this.isLaunching = true;
      console.log('[MAIN_APP_LAUNCHER] Setting isLaunching = true');
      console.log('[MAIN_APP_LAUNCHER] Attempting to launch main app...');
      
      const mainAppPath = this.pathResolver.getMainAppPath();
      console.log('[MAIN_APP_LAUNCHER] Main app path:', mainAppPath);
      console.log('[MAIN_APP_LAUNCHER] Path exists:', fs.existsSync(mainAppPath));
      
      // Check if the main app exists
      if (!fs.existsSync(mainAppPath)) {
        console.error('[MAIN_APP_LAUNCHER] Main app not found:', mainAppPath);
        this.notifyError('Main app not found', mainAppPath);
        this.isLaunching = false;
        return false;
      }
      
      // Create shared state file for communication first
      const sharedStatePath = await this.createSharedStateFile();
      if (!sharedStatePath) {
        this.isLaunching = false;
        return false;
      }

      // Check if main app is already running
      if (await this.processChecker.isMainAppAlreadyRunning()) {
        console.log('[MAIN_APP_LAUNCHER] Main app is already running, using proper communication flow');
        this.isLaunching = false;
        // Use proper closeLauncher method that sets showSettings flags
        this.closeLauncher();
        return true;
      }

      // Launch the main app
      const launched = await this.performLaunch(mainAppPath);
      if (!launched) {
        this.isLaunching = false;
        return false;
      }

      console.log('[MAIN_APP_LAUNCHER] Main app launched, waiting for ML libraries initialization...');
      console.log('[MAIN_APP_LAUNCHER] Setting isLaunching = false after successful launch');
      this.isLaunching = false;

      // Wait for ML libraries ready signal via shared memory
      this.waitForMainAppReadyViaSharedMemory(sharedStatePath);
      
      return true;
    } catch (error) {
      console.error('[MAIN_APP_LAUNCHER] Error launching main app:', error);
      this.isLaunching = false;
      this.notifyError('Failed to launch main app', error instanceof Error ? error.message : String(error));
      return false;
    }
  }

  /**
   * Create shared state file for inter-process communication
   */
  private async createSharedStateFile(): Promise<string | null> {
    const sharedStatePath = path.join(app.getPath('temp'), 'whispr-launcher-state.json');
    const initialState = {
      launcherPid: process.pid,
      launcherReady: true,
      mlLibrariesReady: false,
      showSettings: false,
      timestamp: Date.now()
    };

    try {
      fs.writeFileSync(sharedStatePath, JSON.stringify(initialState, null, 2));
      console.log('[MAIN_APP_LAUNCHER] Shared state file created:', sharedStatePath);
      return sharedStatePath;
    } catch (error) {
      console.error('[MAIN_APP_LAUNCHER] Failed to create shared state file:', error);
      this.notifyError('Failed to create shared state file', error instanceof Error ? error.message : String(error));
      return null;
    }
  }

  /**
   * Perform the actual launch using shell.openExternal with fallback
   */
  private async performLaunch(mainAppPath: string): Promise<boolean> {
    console.log('[MAIN_APP_LAUNCHER] About to call shell.openExternal with path:', mainAppPath);
    
    try {
      await shell.openExternal(mainAppPath);
      console.log('[MAIN_APP_LAUNCHER] shell.openExternal() completed successfully');
      
      // Give the process a moment to start, then check if it's running
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const isRunning = await this.processChecker.isMainAppAlreadyRunning();
      console.log('[MAIN_APP_LAUNCHER] Main app running check after launch:', isRunning);
      
      if (!isRunning) {
        console.error('[MAIN_APP_LAUNCHER] WARNING: shell.openExternal() returned but main app process was not detected');
        console.log('[MAIN_APP_LAUNCHER] This might indicate the exe failed to start or crashed immediately');
        
        // Try alternative launch method with spawn as fallback
        return this.performFallbackLaunch(mainAppPath);
      }

      return true;
    } catch (error) {
      console.error('[MAIN_APP_LAUNCHER] Error with shell.openExternal:', error);
      throw error;
    }
  }

  /**
   * Fallback launch method using spawn
   */
  private async performFallbackLaunch(mainAppPath: string): Promise<boolean> {
    try {
      console.log('[MAIN_APP_LAUNCHER] Attempting fallback launch with spawn...');
      const { spawn } = await import('child_process');
      const mainAppDir = path.dirname(mainAppPath);
      
      const mainAppProcess = spawn(mainAppPath, [], {
        cwd: mainAppDir,
        detached: true,
        stdio: 'ignore',
        windowsHide: false,
      });
      
      mainAppProcess.unref();
      console.log('[MAIN_APP_LAUNCHER] Fallback spawn completed with PID:', mainAppProcess.pid);
      return true;
    } catch (error) {
      console.error('[MAIN_APP_LAUNCHER] Fallback launch failed:', error);
      return false;
    }
  }

  /**
   * Wait for main app to signal readiness via shared memory
   */
  private waitForMainAppReadyViaSharedMemory(sharedStatePath: string): void {
    console.log('[MAIN_APP_LAUNCHER] Starting shared memory monitoring for main app readiness...');
    console.log('[MAIN_APP_LAUNCHER] Shared state path:', sharedStatePath);

    const checkInterval = 1000; // Check every 1 second
    let checkCount = 0;
    const maxChecks = 60; // Maximum 60 seconds

    const checkSharedState = () => {
      checkCount++;
      console.log(`[MAIN_APP_LAUNCHER] Checking shared state (attempt ${checkCount}/${maxChecks})...`);

      try {
        if (fs.existsSync(sharedStatePath)) {
          const stateContent = fs.readFileSync(sharedStatePath, 'utf8');
          const state = JSON.parse(stateContent);
          console.log('[MAIN_APP_LAUNCHER] Current shared state:', state);

          if (state.mlLibrariesReady) {
            console.log('[MAIN_APP_LAUNCHER] ML libraries are ready! Main app initialization complete.');
            this.closeLauncher();
            return;
          }
        } else {
          console.log('[MAIN_APP_LAUNCHER] Shared state file does not exist yet');
        }
      } catch (error) {
        console.error('[MAIN_APP_LAUNCHER] Error reading shared state:', error);
      }

      // Continue checking if we haven't reached the limit
      if (checkCount < maxChecks) {
        setTimeout(checkSharedState, checkInterval);
      } else {
        console.log('[MAIN_APP_LAUNCHER] Timeout waiting for main app readiness, closing anyway');
        this.closeLauncher();
      }
    };

    // Start checking after a short delay
    setTimeout(checkSharedState, checkInterval);

    // Set up timeout as fallback
    const maxWaitTime = 60000; // Maximum 60 seconds
    this.launchTimeout = setTimeout(() => {
      console.log('[MAIN_APP_LAUNCHER] Timeout waiting for main app readiness (60s), closing anyway');
      this.closeLauncher();
    }, maxWaitTime);
  }



  /**
   * Close the launcher properly after main app is ready
   */
  private closeLauncher(): void {
    console.log('[MAIN_APP_LAUNCHER] Closing launcher after main app is ready');
    console.log('[MAIN_APP_LAUNCHER] Resetting isLaunching flag in closeLauncher');
    this.isLaunching = false;

    // Clear any pending timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // Update shared state to signal the main app to show settings window
    try {
      const sharedStatePath = path.join(app.getPath('temp'), 'whispr-launcher-state.json');
      if (fs.existsSync(sharedStatePath)) {
        const stateContent = fs.readFileSync(sharedStatePath, 'utf8');
        const state = JSON.parse(stateContent);
        
        // Set flags to tell main app to show settings window
        state.showSettings = true;
        state.launcherClosing = true;
        
        fs.writeFileSync(sharedStatePath, JSON.stringify(state, null, 2));
        console.log('[MAIN_APP_LAUNCHER] Updated shared state to signal settings window should be shown');
      }
    } catch (error) {
      console.error('[MAIN_APP_LAUNCHER] Failed to update shared state before closing:', error);
    }

    // Close the launcher window properly (sets isQuitting flag)
    closeLauncherWindow();

    // Force quit if the window close doesn't trigger app quit
    setTimeout(() => {
      console.log('[MAIN_APP_LAUNCHER] Force quitting if still running');
      app.quit();

      // If app.quit() doesn't work (common in dev mode), force exit
      setTimeout(() => {
        console.log('[MAIN_APP_LAUNCHER] Final force exit');
        process.exit(0);
      }, 2000);
    }, 1000);
  }

  /**
   * Notify renderer of error
   */
  private notifyError(error: string, details?: string): void {
    const launcherWindow = getLauncherWindow();
    if (launcherWindow) {
      launcherWindow.webContents.send('launcher:main-app-error', {
        error,
        details
      });
    }
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    // Clear launch timeout
    if (this.launchTimeout) {
      clearTimeout(this.launchTimeout);
      this.launchTimeout = null;
    }

    // Reset launching flag
    this.isLaunching = false;
  }

  /**
   * Check if currently launching
   */
  public isCurrentlyLaunching(): boolean {
    return this.isLaunching;
  }
}
