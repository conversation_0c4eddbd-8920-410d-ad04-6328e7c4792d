import { Progress } from "@src/components/ui/progress";
import { cn } from "@src/lib/utils";
import { RiCloseCircleLine, RiLoader4Line, RiCheckLine } from "react-icons/ri";

export interface ModelDownloadProgressProps {
  progress: number;
  modelName?: string;
  error?: string | null;
  className?: string;
}

export function ModelDownloadProgress({
  progress = 0,
  modelName,
  error = null,
  className,
}: ModelDownloadProgressProps) {
  // Normalize progress to ensure it's between 0-100
  const normalizedProgress = Math.max(0, Math.min(100, progress));
  const isComplete = normalizedProgress === 100 && !error;
  
  // Helper function to get appropriate status text
  const getStatusText = () => {
    if (error) return "Error";
    if (isComplete) return `${modelName || 'Model'} downloaded successfully`;
    
    // Different display based on progress
    if (normalizedProgress === 0) return `Preparing ${modelName || 'model'}...`;
    if (normalizedProgress <= 10) return `Initializing ${modelName || 'model'}...`;
    if (normalizedProgress < 100) return `Downloading ${modelName || 'model'}: ${normalizedProgress}%`;
    return `Finalizing ${modelName || 'model'}...`;
  };
  
  return (
    <div className={cn("space-y-2", className)}>
      {/* Status text */}
      <div className="flex items-center text-sm">
        <div className="flex items-center gap-1.5">
          {error ? (
            <RiCloseCircleLine className="text-red-500" />
          ) : isComplete ? (
            <RiCheckLine className="text-green-500" />
          ) : (
            <RiLoader4Line className="animate-spin text-primary" />
          )}
          <span className={
            error ? "text-red-500 font-medium" : 
            isComplete ? "text-green-500 font-medium" : 
            "text-muted-foreground"
          }>
            {getStatusText()}
          </span>
        </div>
      </div>
      
      {/* Progress bar */}
      <Progress 
        value={normalizedProgress} 
        className={cn(
          "h-2",
          error && "bg-red-100 [&>*]:bg-red-500/70",
          isComplete && "bg-green-100 [&>*]:bg-green-500/70"
        )}
      />
      
      {/* Error message if applicable */}
      {error && (
        <div className="text-xs text-red-500 mt-1">
          {error}
        </div>
      )}
    </div>
  );
} 