"""
Model metadata management for One.Whispr.

This module provides classes for tracking model status and metadata.
"""

import logging
import time
from enum import Enum
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field

logger = logging.getLogger("whispr.models.metadata")


class ModelStatus(Enum):
    """Enumeration of possible model states."""
    NOT_DOWNLOADED = "not_downloaded"
    DOWNLOADING = "downloading"
    DOWNLOADED = "downloaded"
    LOADING = "loading"
    LOADED = "loaded"
    ERROR = "error"


@dataclass
class ModelFile:
    """Represents a single model file with metadata."""
    name: str
    size: int
    url: str
    required: bool = True
    downloaded: bool = False
    download_path: Optional[str] = None


@dataclass
class ModelMetadata:
    """Metadata for a voice model."""
    
    # Basic model information
    id: str
    name: str
    size: str  # Human readable size like "144 MB"
    languages: str  # "Multilingual" or "English only"
    is_english_only: bool
    is_recommended: bool
    model_type: str  # "Standard" or "Distilled"
    
    # Files and download information
    files: List[ModelFile] = field(default_factory=list)
    
    # Status and state tracking
    status: ModelStatus = ModelStatus.NOT_DOWNLOADED
    download_progress: float = 0.0
    error_message: Optional[str] = None
    
    # Storage information
    storage_path: Optional[str] = None
    downloaded_at: Optional[float] = None
    
    @property
    def is_downloaded(self) -> bool:
        """Check if the model is downloaded."""
        return self.status in [ModelStatus.DOWNLOADED, ModelStatus.LOADING, ModelStatus.LOADED]
    
    @property
    def has_error(self) -> bool:
        """Check if the model has an error."""
        return self.status == ModelStatus.ERROR
    
    @property
    def download_progress_percent(self) -> int:
        """Get download progress as percentage (0-100)."""
        return int(self.download_progress * 100)
    
    @property
    def size_bytes(self) -> int:
        """Get total model size in bytes from all files."""
        return sum(file_info.size for file_info in self.files if file_info.required)
    
    def update_status(self, status: ModelStatus, error_message: Optional[str] = None):
        """Update the model status."""
        self.status = status
        
        if error_message:
            self.error_message = error_message
        elif status != ModelStatus.ERROR:
            self.error_message = None
            
        if status == ModelStatus.DOWNLOADED:
            self.downloaded_at = time.time()
            self.download_progress = 1.0
    
    def update_download_progress(self, progress: float):
        """Update download progress (0.0 to 1.0)."""
        self.download_progress = max(0.0, min(1.0, progress))
        
        if self.status != ModelStatus.DOWNLOADING and progress > 0:
            self.status = ModelStatus.DOWNLOADING
    
    def mark_file_downloaded(self, filename: str, file_path: str):
        """Mark a specific file as downloaded."""
        for file_info in self.files:
            if file_info.name == filename:
                file_info.downloaded = True
                file_info.download_path = file_path
                break
    
    def get_missing_files(self) -> List[ModelFile]:
        """Get list of files that still need to be downloaded."""
        return [f for f in self.files if f.required and not f.downloaded]
    
    def is_complete(self) -> bool:
        """Check if all required files are downloaded."""
        return len(self.get_missing_files()) == 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary for serialization."""
        return {
            "id": self.id,
            "name": self.name,
            "size": self.size,
            "languages": self.languages,
            "isEnglishOnly": self.is_english_only,
            "isRecommended": self.is_recommended,
            "type": self.model_type,
            "isDownloaded": self.is_downloaded,
            "downloadProgress": self.download_progress_percent,
            "status": self.status.value,
            "error": self.error_message,
            "storage_path": self.storage_path,
            "downloaded_at": self.downloaded_at
        } 