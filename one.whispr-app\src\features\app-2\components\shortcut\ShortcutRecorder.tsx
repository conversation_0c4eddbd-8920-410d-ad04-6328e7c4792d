/**
 * Shortcut Recorder
 * Component for recording keyboard shortcuts
 * Handles save/cancel recording operations
 */

import { Button } from '@src/components/ui/button';
import { Alert, AlertDescription } from '@src/components/ui/alert';
import { HiCheck, HiXMark } from 'react-icons/hi2';

// ============================================================================
// TYPES
// ============================================================================

interface ShortcutRecorderProps {
  isRecording: boolean;
  tempKeys: string[];
  onSave: () => void;
  onCancel: () => void;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function ShortcutRecorder({
  isRecording,
  tempKeys,
  onSave,
  onCancel,
  className
}: ShortcutRecorderProps) {
  if (!isRecording) {
    return null;
  }

  const hasKeys = tempKeys.length > 0;

  return (
    <div className={`space-y-4 ${className || ''}`}>
      <Alert>
        <AlertDescription>
          {hasKeys 
            ? `Press another key to add to the combination, or save your shortcut: ${tempKeys.join(' + ')}`
            : 'Press the keys you want to use for this shortcut...'
          }
        </AlertDescription>
      </Alert>

      {hasKeys && (
        <div className="flex items-center gap-2">
          <Button
            onClick={onSave}
            size="sm"
            className="gap-2"
          >
            <HiCheck className="w-4 h-4" />
            Save Shortcut
          </Button>
          <Button
            onClick={onCancel}
            variant="outline"
            size="sm"
            className="gap-2"
          >
            <HiXMark className="w-4 h-4" />
            Cancel
          </Button>
        </div>
      )}
    </div>
  );
}

export default ShortcutRecorder; 