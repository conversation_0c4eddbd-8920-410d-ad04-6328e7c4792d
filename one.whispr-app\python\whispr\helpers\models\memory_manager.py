"""
Memory manager for One.Whispr voice models.

This module provides automatic memory management, monitoring, and optimization
for model loading operations with configurable thresholds and cleanup strategies.
"""

import asyncio
import gc
import logging
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional, Callable, List
import threading

from .hardware_detector import HardwareDetector, MemoryInfo

logger = logging.getLogger('whispr.helpers.models.memory_manager')


class MemoryPressure(Enum):
    """Memory pressure levels."""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class MemoryThresholds:
    """Memory usage thresholds for different pressure levels."""
    low_threshold: float = 60.0      # Below 60% usage
    moderate_threshold: float = 75.0  # 60-75% usage
    high_threshold: float = 85.0     # 75-85% usage
    critical_threshold: float = 95.0  # Above 85% usage


@dataclass
class MemoryEvent:
    """Memory event information."""
    timestamp: float
    event_type: str
    pressure_level: MemoryPressure
    memory_info: MemoryInfo
    action_taken: Optional[str] = None
    bytes_freed: Optional[int] = None


@dataclass
class MemoryStats:
    """Memory usage statistics."""
    current_pressure: MemoryPressure
    system_memory_percent: float
    gpu_memory_percent: Optional[float]
    total_system_memory: int
    available_system_memory: int
    total_gpu_memory: Optional[int]
    available_gpu_memory: Optional[int]
    cleanup_count: int
    last_cleanup_time: Optional[float]
    bytes_freed_total: int


class MemoryManager:
    """Automatic memory management and monitoring."""
    
    def __init__(self, thresholds: Optional[MemoryThresholds] = None):
        """Initialize the memory manager.
        
        Args:
            thresholds: Custom memory thresholds, uses defaults if None
        """
        self.hardware_detector = HardwareDetector()
        self.thresholds = thresholds or MemoryThresholds()
        
        # State tracking
        self._monitoring = False
        self._monitor_task: Optional[asyncio.Task] = None
        self._monitor_interval = 5.0  # Check every 5 seconds
        self._cleanup_callbacks: List[Callable[[], None]] = []
        self._event_callbacks: List[Callable[[MemoryEvent], None]] = []
        self._lock = threading.Lock()
        
        # Statistics
        self._cleanup_count = 0
        self._last_cleanup_time: Optional[float] = None
        self._bytes_freed_total = 0
        self._events: List[MemoryEvent] = []
        self._max_events = 100  # Keep last 100 events
        
        # Cleanup strategies
        self._cleanup_strategies = [
            self._cleanup_python_gc,
            self._cleanup_torch_cache,
            self._cleanup_system_memory
        ]
    
    def add_cleanup_callback(self, callback: Callable[[], None]) -> None:
        """Add a callback for custom cleanup operations.
        
        Args:
            callback: Function to call during cleanup
        """
        if callback not in self._cleanup_callbacks:
            self._cleanup_callbacks.append(callback)
    
    def remove_cleanup_callback(self, callback: Callable[[], None]) -> None:
        """Remove a cleanup callback.
        
        Args:
            callback: Function to remove
        """
        if callback in self._cleanup_callbacks:
            self._cleanup_callbacks.remove(callback)
    
    def add_event_callback(self, callback: Callable[[MemoryEvent], None]) -> None:
        """Add a callback for memory events.
        
        Args:
            callback: Function to call with memory events
        """
        if callback not in self._event_callbacks:
            self._event_callbacks.append(callback)
    
    def remove_event_callback(self, callback: Callable[[MemoryEvent], None]) -> None:
        """Remove an event callback.
        
        Args:
            callback: Function to remove
        """
        if callback in self._event_callbacks:
            self._event_callbacks.remove(callback)
    
    def start_monitoring(self, interval: float = 5.0) -> None:
        """Start automatic memory monitoring.
        
        Args:
            interval: Monitoring interval in seconds
        """
        if self._monitoring:
            logger.warning("Memory monitoring already started")
            return
        
        self._monitor_interval = interval
        self._monitoring = True
        
        # Start monitoring task
        loop = asyncio.get_event_loop()
        self._monitor_task = loop.create_task(self._monitor_loop())
        
        logger.info(f"Memory monitoring started with {interval}s interval")
    
    def stop_monitoring(self) -> None:
        """Stop automatic memory monitoring."""
        if not self._monitoring:
            return
        
        self._monitoring = False
        
        if self._monitor_task:
            self._monitor_task.cancel()
            self._monitor_task = None
        
        logger.info("Memory monitoring stopped")
    
    async def _monitor_loop(self) -> None:
        """Main monitoring loop."""
        try:
            while self._monitoring:
                await self._check_memory_pressure()
                await asyncio.sleep(self._monitor_interval)
        except asyncio.CancelledError:
            logger.debug("Memory monitoring loop cancelled")
        except Exception as e:
            logger.error(f"Error in memory monitoring loop: {e}")
            self._monitoring = False
    
    async def _check_memory_pressure(self) -> None:
        """Check current memory pressure and take action if needed."""
        # Only check system memory during routine monitoring to avoid heavy imports
        memory_info = self.hardware_detector.get_memory_info(include_gpu=False)
        pressure = self._calculate_pressure(memory_info)
        
        # Create memory event
        event = MemoryEvent(
            timestamp=time.time(),
            event_type="pressure_check",
            pressure_level=pressure,
            memory_info=memory_info
        )
        
        # Take action based on pressure level
        if pressure == MemoryPressure.CRITICAL:
            await self._handle_critical_pressure(event)
        elif pressure == MemoryPressure.HIGH:
            await self._handle_high_pressure(event)
        elif pressure == MemoryPressure.MODERATE:
            await self._handle_moderate_pressure(event)
        
        # Record event
        self._record_event(event)
    
    def _calculate_pressure(self, memory_info: MemoryInfo) -> MemoryPressure:
        """Calculate memory pressure level.
        
        Args:
            memory_info: Current memory information
            
        Returns:
            MemoryPressure level
        """
        # Use the higher of system or GPU memory pressure
        system_percent = memory_info.memory_percent
        gpu_percent = memory_info.gpu_memory_percent or 0
        
        max_percent = max(system_percent, gpu_percent)
        
        if max_percent >= self.thresholds.critical_threshold:
            return MemoryPressure.CRITICAL
        elif max_percent >= self.thresholds.high_threshold:
            return MemoryPressure.HIGH
        elif max_percent >= self.thresholds.moderate_threshold:
            return MemoryPressure.MODERATE
        else:
            return MemoryPressure.LOW
    
    async def _handle_critical_pressure(self, event: MemoryEvent) -> None:
        """Handle critical memory pressure.
        
        Args:
            event: Memory event to update
        """
        logger.warning("Critical memory pressure detected - performing aggressive cleanup")
        
        # Aggressive cleanup
        bytes_freed = await self.cleanup_memory(aggressive=True)
        
        event.action_taken = "aggressive_cleanup"
        event.bytes_freed = bytes_freed
        
        # If still critical after cleanup, log warning
        post_cleanup_info = self.hardware_detector.get_memory_info()
        post_pressure = self._calculate_pressure(post_cleanup_info)
        
        if post_pressure == MemoryPressure.CRITICAL:
            logger.error("Memory pressure still critical after cleanup - system may be unstable")
    
    async def _handle_high_pressure(self, event: MemoryEvent) -> None:
        """Handle high memory pressure.
        
        Args:
            event: Memory event to update
        """
        logger.info("High memory pressure detected - performing cleanup")
        
        bytes_freed = await self.cleanup_memory(aggressive=False)
        
        event.action_taken = "standard_cleanup"
        event.bytes_freed = bytes_freed
    
    async def _handle_moderate_pressure(self, event: MemoryEvent) -> None:
        """Handle moderate memory pressure.
        
        Args:
            event: Memory event to update
        """
        # Only cleanup if we haven't done so recently
        if (self._last_cleanup_time is None or 
            time.time() - self._last_cleanup_time > 60):  # 1 minute cooldown
            
            logger.debug("Moderate memory pressure - performing light cleanup")
            
            bytes_freed = await self.cleanup_memory(aggressive=False, light=True)
            
            event.action_taken = "light_cleanup"
            event.bytes_freed = bytes_freed
    
    async def cleanup_memory(self, aggressive: bool = False, light: bool = False) -> int:
        """Perform memory cleanup.
        
        Args:
            aggressive: Whether to perform aggressive cleanup
            light: Whether to perform only light cleanup
            
        Returns:
            Estimated bytes freed
        """
        with self._lock:
            start_time = time.time()
            initial_memory = self.hardware_detector.get_memory_info()
            
            logger.debug(f"Starting memory cleanup (aggressive={aggressive}, light={light})")
            
            total_freed = 0
            
            # Run cleanup strategies
            for strategy in self._cleanup_strategies:
                try:
                    if light and strategy != self._cleanup_python_gc:
                        continue  # Only run GC for light cleanup
                    
                    freed = await strategy(aggressive)
                    total_freed += freed
                    
                    # Small delay between strategies
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    logger.error(f"Error in cleanup strategy {strategy.__name__}: {e}")
            
            # Run custom cleanup callbacks
            for callback in self._cleanup_callbacks:
                try:
                    callback()
                except Exception as e:
                    logger.error(f"Error in cleanup callback: {e}")
            
            # Update statistics
            self._cleanup_count += 1
            self._last_cleanup_time = time.time()
            self._bytes_freed_total += total_freed
            
            cleanup_time = time.time() - start_time
            logger.debug(f"Memory cleanup completed in {cleanup_time:.2f}s, freed ~{total_freed / (1024**2):.1f}MB")
            
            return total_freed
    
    async def _cleanup_python_gc(self, aggressive: bool) -> int:
        """Python garbage collection cleanup.
        
        Args:
            aggressive: Whether to perform aggressive cleanup
            
        Returns:
            Estimated bytes freed
        """
        if aggressive:
            # Multiple GC passes for aggressive cleanup
            total_collected = 0
            for _ in range(3):
                collected = gc.collect()
                total_collected += collected
                await asyncio.sleep(0.05)
            
            # Estimate bytes freed (rough approximation)
            estimated_freed = total_collected * 100  # Assume 100 bytes per object
            
        else:
            collected = gc.collect()
            estimated_freed = collected * 100
        
        logger.debug(f"Python GC freed {collected if not aggressive else total_collected} objects")
        return estimated_freed
    
    async def _cleanup_torch_cache(self, aggressive: bool) -> int:
        """PyTorch CUDA cache cleanup.
        
        Args:
            aggressive: Whether to perform aggressive cleanup
            
        Returns:
            Estimated bytes freed
        """
        try:
            import torch
            
            if not torch.cuda.is_available():
                return 0
            
            # Get memory before cleanup
            memory_before = torch.cuda.memory_allocated()
            
            if aggressive:
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                # Force garbage collection of CUDA tensors
                torch.cuda.ipc_collect()
            else:
                torch.cuda.empty_cache()
            
            # Get memory after cleanup
            memory_after = torch.cuda.memory_allocated()
            freed = max(0, memory_before - memory_after)
            
            logger.debug(f"PyTorch CUDA cache cleanup freed {freed / (1024**2):.1f}MB")
            return freed
            
        except ImportError:
            return 0
        except Exception as e:
            logger.error(f"Error in PyTorch cleanup: {e}")
            return 0
    
    async def _cleanup_system_memory(self, aggressive: bool) -> int:
        """System-specific memory cleanup.
        
        Args:
            aggressive: Whether to perform aggressive cleanup
            
        Returns:
            Estimated bytes freed
        """
        if not aggressive:
            return 0
        
        try:
            import platform
            
            if platform.system() == "Linux":
                import ctypes
                libc = ctypes.CDLL("libc.so.6")
                libc.malloc_trim(0)
                logger.debug("Linux memory trimming performed")
                return 1024 * 1024  # Estimate 1MB freed
            
        except Exception as e:
            logger.debug(f"System memory cleanup not available: {e}")
        
        return 0
    
    def _record_event(self, event: MemoryEvent) -> None:
        """Record a memory event.
        
        Args:
            event: Event to record
        """
        self._events.append(event)
        
        # Keep only recent events
        if len(self._events) > self._max_events:
            self._events = self._events[-self._max_events:]
        
        # Notify event callbacks
        for callback in self._event_callbacks:
            try:
                callback(event)
            except Exception as e:
                logger.error(f"Error in event callback: {e}")
    
    def get_memory_stats(self, include_gpu: bool = False) -> MemoryStats:
        """Get current memory statistics.
        
        Args:
            include_gpu: Whether to include GPU memory info (requires PyTorch import)
        
        Returns:
            MemoryStats with current information
        """
        memory_info = self.hardware_detector.get_memory_info(include_gpu=include_gpu)
        pressure = self._calculate_pressure(memory_info)
        
        return MemoryStats(
            current_pressure=pressure,
            system_memory_percent=memory_info.memory_percent,
            gpu_memory_percent=memory_info.gpu_memory_percent,
            total_system_memory=memory_info.total_system_memory,
            available_system_memory=memory_info.available_system_memory,
            total_gpu_memory=memory_info.gpu_memory_total,
            available_gpu_memory=memory_info.gpu_memory_free,
            cleanup_count=self._cleanup_count,
            last_cleanup_time=self._last_cleanup_time,
            bytes_freed_total=self._bytes_freed_total
        )
    
    def get_recent_events(self, count: int = 10) -> List[MemoryEvent]:
        """Get recent memory events.
        
        Args:
            count: Number of recent events to return
            
        Returns:
            List of recent MemoryEvent objects
        """
        return self._events[-count:] if self._events else []
    
    def check_memory_for_operation(self, required_bytes: int, include_gpu: bool = False) -> tuple[bool, str]:
        """Check if there's enough memory for an operation.
        
        Args:
            required_bytes: Bytes required for the operation
            include_gpu: Whether to include GPU memory info (requires PyTorch import)
            
        Returns:
            Tuple of (can_proceed, reason)
        """
        memory_info = self.hardware_detector.get_memory_info(include_gpu=include_gpu)
        pressure = self._calculate_pressure(memory_info)
        
        # Check if we have enough available memory
        available = memory_info.available_system_memory
        
        if required_bytes > available:
            return False, f"Insufficient memory: need {required_bytes / (1024**3):.1f}GB, have {available / (1024**3):.1f}GB"
        
        # Check pressure level
        if pressure == MemoryPressure.CRITICAL:
            return False, "Memory pressure is critical - operation not recommended"
        
        # Check if operation would push us into critical territory
        estimated_usage = (memory_info.used_system_memory + required_bytes) / memory_info.total_system_memory * 100
        
        if estimated_usage > self.thresholds.critical_threshold:
            return False, f"Operation would cause critical memory pressure ({estimated_usage:.1f}%)"
        
        return True, "Memory check passed"
    
    async def prepare_for_operation(self, required_bytes: int) -> bool:
        """Prepare memory for a large operation.
        
        Args:
            required_bytes: Bytes required for the operation
            
        Returns:
            True if memory was successfully prepared
        """
        can_proceed, reason = self.check_memory_for_operation(required_bytes)
        
        if can_proceed:
            return True
        
        logger.info(f"Preparing memory for operation requiring {required_bytes / (1024**3):.1f}GB")
        
        # Try cleanup to free memory
        await self.cleanup_memory(aggressive=True)
        
        # Check again after cleanup
        can_proceed, reason = self.check_memory_for_operation(required_bytes)
        
        if not can_proceed:
            logger.warning(f"Failed to prepare memory: {reason}")
        
        return can_proceed
    
    def to_dict(self, include_gpu: bool = False) -> Dict[str, Any]:
        """Convert memory manager state to dictionary.
        
        Args:
            include_gpu: Whether to include GPU memory info (requires PyTorch import)
        
        Returns:
            Dictionary representation of memory manager state
        """
        stats = self.get_memory_stats(include_gpu=include_gpu)
        recent_events = self.get_recent_events(5)
        
        return {
            "monitoring": self._monitoring,
            "monitor_interval": self._monitor_interval,
            "thresholds": {
                "low": self.thresholds.low_threshold,
                "moderate": self.thresholds.moderate_threshold,
                "high": self.thresholds.high_threshold,
                "critical": self.thresholds.critical_threshold
            },
            "stats": {
                "current_pressure": stats.current_pressure.value,
                "system_memory_percent": stats.system_memory_percent,
                "gpu_memory_percent": stats.gpu_memory_percent,
                "total_system_memory": stats.total_system_memory,
                "available_system_memory": stats.available_system_memory,
                "total_gpu_memory": stats.total_gpu_memory,
                "available_gpu_memory": stats.available_gpu_memory,
                "cleanup_count": stats.cleanup_count,
                "last_cleanup_time": stats.last_cleanup_time,
                "bytes_freed_total": stats.bytes_freed_total
            },
            "recent_events": [
                {
                    "timestamp": event.timestamp,
                    "event_type": event.event_type,
                    "pressure_level": event.pressure_level.value,
                    "action_taken": event.action_taken,
                    "bytes_freed": event.bytes_freed
                }
                for event in recent_events
            ]
        } 