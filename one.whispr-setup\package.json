{"name": "OneWhispr", "version": "1.0.0", "description": "One Whispr - <PERSON><PERSON><PERSON>, Updater, and Launcher", "author": "One Whispr Team", "private": true, "main": ".dist/main/electron/main.js", "scripts": {"dev": "cross-env NODE_ENV=development npm run build && electron .", "dev:no-reload": "cross-env NODE_ENV=development npm run build && cross-env LOAD_FROM_VITE=false electron .", "dev:download": "npm run build && cross-env NODE_ENV=development FORCE_DOWNLOAD=true DOWNLOAD_PATH=.dist/main-app LOAD_FROM_VITE=false electron .", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.electron.json", "build:dist": "npm run build && electron-builder --config electron-builder-direct.json", "build:dist:direct": "npm run build && electron-builder --config electron-builder-direct.json", "build:dist:microsoft": "npm run build && node scripts/prepare-microsoft-build.js prepare && electron-builder --config electron-builder-microsoft.json && node scripts/prepare-microsoft-build.js restore", "clean": "rimraf .dist", "postinstall": "electron-builder install-app-deps && npx @electron/rebuild"}, "dependencies": {"@radix-ui/react-label": "^2.1.3", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "7zip-bin": "^5.2.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "electron-squirrel-startup": "^1.0.1", "electron-updater": "^6.3.4", "fs-extra": "^11.3.0", "progress-stream": "^2.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "semver": "^7.6.5", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5"}, "devDependencies": {"@electron/rebuild": "^4.0.1", "@tailwindcss/vite": "^4.1.3", "@types/fs-extra": "^11.0.4", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@types/semver": "^7.7.0", "@vitejs/plugin-react": "^4.3.4", "cross-env": "^7.0.3", "electron": "^35.0.0", "electron-builder": "^26.0.12", "electron-builder-squirrel-windows": "^26.0.12", "rimraf": "^5.0.1", "tailwindcss": "^4.1.3", "typescript": "^5.8.3", "vite": "^6.2.6"}}