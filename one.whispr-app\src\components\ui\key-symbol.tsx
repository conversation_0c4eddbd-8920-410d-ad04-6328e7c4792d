import { HTMLProps } from 'react';
import { cn } from "@src/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@src/components/ui/tooltip";

export enum Keys {
  Enter = 'Enter',
  Space = 'Space',
  Control = 'Control',
  Shift = 'Shift',
  Alt = 'Alt',
  Escape = 'Escape',
  ArrowUp = 'ArrowUp',
  ArrowDown = 'ArrowDown',
  ArrowLeft = 'ArrowLeft',
  ArrowRight = 'ArrowRight',
  Backspace = 'Backspace',
  Tab = 'Tab',
  CapsLock = 'CapsLock',
  Fn = 'Fn',
  Command = 'Command',
  Meta = 'Meta',
  Insert = 'Insert',
  Delete = 'Delete',
  Home = 'Home',
  End = 'End',
  PageUp = 'PageUp',
  PageDown = 'PageDown',
  PrintScreen = 'PrintScreen',
  Pause = 'Pause',
  F1 = 'F1',
  F2 = 'F2',
  F3 = 'F3',
  F4 = 'F4',
  F5 = 'F5',
  F6 = 'F6',
  F7 = 'F7',
  F8 = 'F8',
  F9 = 'F9',
  F10 = 'F10',
  F11 = 'F11',
  F12 = 'F12',
}

interface KeyData {
  symbol: string;
  label: string;
}

export const KEY_MAPPINGS: Record<string, KeyData> = {
  [Keys.Enter]: { symbol: '↵', label: 'Enter' },
  [Keys.Space]: { symbol: 'Space', label: 'Space' },
  [Keys.Control]: { symbol: 'Ctrl', label: 'Control' },
  [Keys.Shift]: { symbol: 'Shift', label: 'Shift' },
  [Keys.Alt]: { symbol: 'Alt', label: 'Alt' },
  [Keys.Escape]: { symbol: 'Esc', label: 'Escape' },
  [Keys.ArrowUp]: { symbol: '↑', label: 'Arrow Up' },
  [Keys.ArrowDown]: { symbol: '↓', label: 'Arrow Down' },
  [Keys.ArrowLeft]: { symbol: '←', label: 'Arrow Left' },
  [Keys.ArrowRight]: { symbol: '→', label: 'Arrow Right' },
  [Keys.Backspace]: { symbol: '⌫', label: 'Backspace' },
  [Keys.Tab]: { symbol: '⭾', label: 'Tab' },
  [Keys.CapsLock]: { symbol: '⇪', label: 'Caps Lock' },
  [Keys.Fn]: { symbol: 'Fn', label: 'Function' },
  [Keys.Command]: { symbol: '⌘', label: 'Command' },
  [Keys.Meta]: { symbol: '⊞', label: 'Windows' },
  [Keys.Insert]: { symbol: 'Ins', label: 'Insert' },
  [Keys.Delete]: { symbol: 'Del', label: 'Delete' },
  [Keys.Home]: { symbol: 'Home', label: 'Home' },
  [Keys.End]: { symbol: 'End', label: 'End' },
  [Keys.PageUp]: { symbol: 'PgUp', label: 'Page Up' },
  [Keys.PageDown]: { symbol: 'PgDn', label: 'Page Down' },
  [Keys.PrintScreen]: { symbol: 'PrtSc', label: 'Print Screen' },
  [Keys.Pause]: { symbol: 'Pause', label: 'Pause/Break' },
  [Keys.F1]: { symbol: 'F1', label: 'F1' },
  [Keys.F2]: { symbol: 'F2', label: 'F2' },
  [Keys.F3]: { symbol: 'F3', label: 'F3' },
  [Keys.F4]: { symbol: 'F4', label: 'F4' },
  [Keys.F5]: { symbol: 'F5', label: 'F5' },
  [Keys.F6]: { symbol: 'F6', label: 'F6' },
  [Keys.F7]: { symbol: 'F7', label: 'F7' },
  [Keys.F8]: { symbol: 'F8', label: 'F8' },
  [Keys.F9]: { symbol: 'F9', label: 'F9' },
  [Keys.F10]: { symbol: 'F10', label: 'F10' },
  [Keys.F11]: { symbol: 'F11', label: 'F11' },
  [Keys.F12]: { symbol: 'F12', label: 'F12' },
};

interface KeySymbolProps {
  keyName: string;
  disableTooltip?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'setup';
  className?: string;
}

const sizeClasses = {
  sm: 'h-5 min-w-[1.25rem] text-[10px]',
  md: 'h-6 min-w-[1.5rem] text-xs',
  lg: 'h-7 min-w-[1.75rem] text-sm',
  setup: 'h-14 min-w-[3.5rem] text-lg'
} as const;

export function KeySymbol({
  keyName,
  disableTooltip = false,
  className,
  size = 'md',
  ...otherProps
}: KeySymbolProps & Omit<HTMLProps<HTMLDivElement>, keyof KeySymbolProps>) {
  const keyData = KEY_MAPPINGS[keyName];
  const symbol = keyData?.symbol ?? keyName;
  const label = keyData?.label ?? keyName;

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <div
            className={cn(
              'flex items-center justify-center px-2',
              'bg-neutral-200 dark:bg-neutral-800',
              'border border-neutral-300 dark:border-neutral-700',
              'shadow-sm',
              'text-neutral-700 dark:text-neutral-200 font-medium rounded-md',
              'transition-colors',
              'hover:bg-neutral-300 dark:hover:bg-neutral-700',
              sizeClasses[size],
              className
            )}
            aria-label={label}
            {...otherProps}
          >
            <span>{symbol}</span>
          </div>
        </TooltipTrigger>
        {!disableTooltip && label !== symbol && (
          <TooltipContent side="top" className="px-2 py-1 text-xs">
            {label}
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
} 