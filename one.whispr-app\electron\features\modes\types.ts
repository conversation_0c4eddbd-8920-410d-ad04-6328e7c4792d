import { BaseEntity } from '../../database/repositories/base';
import { nanoid } from 'nanoid';

export interface ModeExample {
  id?: string;
  input: string;
  output: string;
}

export interface Mode extends BaseEntity {
  id: string;
  name: string;
  description: string;
  color: string;
  icon: string;
  isActive: boolean;
  order_index: number;
  voiceModel: string; // Voice model ID reference
  aiModel?: string; // AI model ID reference (optional)
  
  promptSettings: {
    systemPrompt: string;
    examples: ModeExample[];
  };
  
  configuration: {
    // AI Processing options
    enableAIPostProcessing: boolean;
    includeSelectionAsContextInAI: boolean;
    includeClipboardAsContextInAI: boolean;

    // Text insertion options
    autoSelectAllAfter: boolean;
    autoPaste: boolean;
    copyToClipboard: boolean;
    addSpaceAfter: boolean;

    // Transcription options
    realTimeTranscription: boolean;
    listenToSpeaker: boolean;
    removeFormatting: boolean;

    // UI options
    showOverlay: boolean;
  };
}

// Default mode examples
export const DEFAULT_MODE_EXAMPLES: ModeExample[] = [
  {
    id: nanoid(),
    input: "um well i think that the meeting went really well today",
    output: "I think the meeting went really well today."
  },
  {
    id: nanoid(),
    input: "so basically what we need to do is uh implement the new feature",
    output: "We need to implement the new feature."
  }
];

// Default modes
export const DEFAULT_MODES: Mode[] = [
  {
    id: nanoid(),
    name: 'Clean Transcription',
    description: 'Clean up transcription by removing filler words and improving grammar',
    color: '#3b82f6',
    icon: '✨',
    isActive: true,
    order_index: 1,
    voiceModel: 'openai/whisper-base',
    aiModel: 'gemini-1.5-flash',
    promptSettings: {
      systemPrompt: 'Clean up the following transcription by removing filler words (um, uh, like, you know), fixing grammar, and making it more professional while preserving the original meaning and tone.',
      examples: DEFAULT_MODE_EXAMPLES
    },
    configuration: {
      enableAIPostProcessing: true,
      includeSelectionAsContextInAI: false,
      includeClipboardAsContextInAI: false,
      autoSelectAllAfter: true,
      autoPaste: true,
      copyToClipboard: true,
      addSpaceAfter: true,
      realTimeTranscription: false,
      listenToSpeaker: false,
      removeFormatting: false,
      showOverlay: true
    }
  },
  {
    id: nanoid(),
    name: 'Direct Transcription',
    description: 'Raw transcription with no AI processing - just paste what was said',
    color: '#10b981',
    icon: '🎤',
    isActive: false,
    order_index: 2,
    voiceModel: 'openai/whisper-base.en',
    promptSettings: {
      systemPrompt: '',
      examples: []
    },
    configuration: {
      enableAIPostProcessing: false,
      includeSelectionAsContextInAI: false,
      includeClipboardAsContextInAI: false,
      autoSelectAllAfter: false,
      autoPaste: true,
      copyToClipboard: true,
      addSpaceAfter: true,
      realTimeTranscription: true,
      listenToSpeaker: false,
      removeFormatting: false,
      showOverlay: false
    }
  },
  {
    id: nanoid(),
    name: 'Meeting Notes',
    description: 'Convert speech into structured meeting notes with key points',
    color: '#f59e0b',
    icon: '📝',
    isActive: false,
    order_index: 3,
    voiceModel: 'openai/whisper-base.en',
    aiModel: 'gemini-1.5-flash',
    promptSettings: {
      systemPrompt: 'Convert the following transcription into structured meeting notes. Extract key points, action items, and decisions. Format as bullet points with clear sections.',
      examples: [
        {
          id: nanoid(),
          input: "so we discussed the project timeline and john said he can finish the design by friday and sarah mentioned she needs two more days for testing",
          output: "## Key Points\n• Project timeline discussed\n• Design completion: Friday (John)\n• Testing timeline: +2 days needed (Sarah)\n\n## Action Items\n• John: Complete design by Friday\n• Sarah: Finish testing by Sunday"
        }
      ]
    },
    configuration: {
      enableAIPostProcessing: true,
      includeSelectionAsContextInAI: true,
      includeClipboardAsContextInAI: false,
      autoSelectAllAfter: true,
      autoPaste: false,
      copyToClipboard: true,
      addSpaceAfter: true,
      realTimeTranscription: false,
      listenToSpeaker: true,
      removeFormatting: false,
      showOverlay: true
    }
  },
  {
    id: nanoid(),
    name: 'Email Draft',
    description: 'Convert speech into a professional email format',
    color: '#8b5cf6',
    icon: '📧',
    isActive: false,
    order_index: 4,
    voiceModel: 'openai/whisper-base.en',
    aiModel: 'gemini-1.5-flash',
    promptSettings: {
      systemPrompt: 'Convert the following speech into a professional email. Add appropriate subject line, greeting, and closing. Make it polite and business-appropriate.',
      examples: [
        {
          id: nanoid(),
          input: "hey can you send me those reports we talked about yesterday i need them for the presentation tomorrow",
          output: "Subject: Request for Reports - Presentation Tomorrow\n\nHi [Name],\n\nI hope this email finds you well. Could you please send me the reports we discussed yesterday? I need them for my presentation tomorrow.\n\nThank you for your assistance.\n\nBest regards,\n[Your Name]"
        }
      ]
    },
    configuration: {
      enableAIPostProcessing: true,
      includeSelectionAsContextInAI: false,
      includeClipboardAsContextInAI: false,
      autoSelectAllAfter: true,
      autoPaste: false,
      copyToClipboard: true,
      addSpaceAfter: true,
      realTimeTranscription: false,
      listenToSpeaker: false,
      removeFormatting: false,
      showOverlay: true
    }
  }
]; 