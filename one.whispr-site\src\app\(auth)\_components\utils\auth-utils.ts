/**
 * Auth utilities for handling URL parameters, navigation, and common operations
 */

/**
 * Builds URL parameters for auth navigation
 */
export function buildAuthParams(params: Record<string, string | null | undefined>): URLSearchParams {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      searchParams.set(key, value);
    }
  });
  
  return searchParams;
}

/**
 * Appends callback URL to parameters if it exists
 */
export function appendCallbackUrl(
  baseUrl: string, 
  callbackUrl: string | null | undefined
): string {
  if (!callbackUrl) return baseUrl;
  return `${baseUrl}${baseUrl.includes('?') ? '&' : '?'}callbackUrl=${encodeURIComponent(callbackUrl)}`;
}

/**
 * Auth step types for the different flows
 */
export type AuthStep = 'email' | 'password' | 'otp' | 'info' | 'reset';

/**
 * Auth mode types
 */
export type AuthMode = 'login' | 'register' | 'reset';

/**
 * Base interface for auth components
 */
export interface BaseAuthProps {
  email?: string;
  callbackUrl?: string | null;
  onSuccess?: (data: any) => void;
}

/**
 * Generates the URL for the next step in the auth flow
 */
export function buildNextStepUrl(
  flow: 'login' | 'register' | 'forgot-password',
  step: AuthStep,
  params: Record<string, string | null | undefined>
): string {
  const searchParams = buildAuthParams(params);
  return `/${flow}?${searchParams.toString()}`;
} 