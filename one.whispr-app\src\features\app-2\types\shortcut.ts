/**
 * Shortcut Types
 * Types related to keyboard shortcuts, recording modes, and shortcut management
 * Based on electron/features/settings/types.ts and app-latest implementation
 */

import type { RecordingMode } from './core';

// ============================================================================
// SHORTCUT TYPES
// ============================================================================

export interface Shortcuts {
  pushToTalk: string[];
  toggle: string[];
  cancel: string[];
  modeSwitch: string[];
}

export interface ShortcutState {
  recordingMode: RecordingMode;
  shortcuts: Shortcuts;
  
  // Recording shortcut state
  isRecordingShortcut: boolean;
  recordingAction: keyof Shortcuts | null;
  tempKeys: string[];
  
  // Real-time key tracking
  pressedKeys: Set<string>;
  
  // Test mode state
  testModeEnabled: boolean;
  
  // Loading and error states
  loading: boolean;
  error: string | null;
}

export interface ShortcutActions {
  setRecordingMode: (mode: RecordingMode) => void;
  updateShortcut: (action: keyof Shortcuts, keys: string[]) => void;
  startRecordingShortcut: (action: keyof Shortcuts) => Promise<void>;
  stopRecordingShortcut: () => void;
  clearError: () => void;
}

export interface ShortcutContextValue {
  state: ShortcutState;
  actions: ShortcutActions;
} 