/**
 * Error Message
 * Reusable error display component matching app-latest design
 * Uses the existing Alert UI component with consistent styling
 */

import { Alert, AlertDescription } from '@src/components/ui/alert';
import { Button } from '@src/components/ui/button';
import { HiExclamationCircle, HiXMark } from 'react-icons/hi2';
import { cn } from '@src/lib/utils';

// ============================================================================
// TYPES
// ============================================================================

interface ErrorMessageProps {
  message: string;
  title?: string;
  onDismiss?: () => void;
  className?: string;
  variant?: 'destructive' | 'default';
}

// ============================================================================
// COMPONENT
// ============================================================================

export function ErrorMessage({ 
  message, 
  title = 'Error',
  onDismiss,
  className,
  variant = 'destructive'
}: ErrorMessageProps) {
  return (
    <Alert 
      variant={variant} 
      className={cn(
        'animate-in slide-in-from-top-2 duration-300',
        className
      )}
    >
      <HiExclamationCircle className="h-4 w-4" />
      <div className="flex-1">
        {title && <AlertDescription className="font-medium">{title}</AlertDescription>}
        <AlertDescription className={title ? 'mt-1' : ''}>{message}</AlertDescription>
      </div>
      {onDismiss && (
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8 ml-auto"
          onClick={onDismiss}
        >
          <HiXMark className="h-4 w-4" />
          <span className="sr-only">Dismiss</span>
        </Button>
      )}
    </Alert>
  );
}

export default ErrorMessage; 