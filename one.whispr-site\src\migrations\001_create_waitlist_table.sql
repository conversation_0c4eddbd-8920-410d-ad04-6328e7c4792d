-- Add extension for UUID generation if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create waitlist table
CREATE TABLE IF NOT EXISTS waitlist (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create a unique index for email to prevent duplicates
CREATE UNIQUE INDEX IF NOT EXISTS waitlist_email_idx ON waitlist (email);

-- Create or replace function for updating timestamps
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists to avoid errors when re-running migration
DROP TRIGGER IF EXISTS set_timestamp ON waitlist;

-- Create trigger for updating timestamps
CREATE TRIGGER set_timestamp
BEFORE UPDATE ON waitlist
FOR EACH ROW
EXECUTE FUNCTION trigger_set_timestamp();

-- Add comment to table
COMMENT ON TABLE waitlist IS 'Stores email addresses of users who want to join the waitlist'; 