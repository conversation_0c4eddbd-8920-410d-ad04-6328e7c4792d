"""
Model downloader for One.Whispr voice models.

This module provides robust downloading capabilities with progress tracking,
resumption, error handling, and queue management.
"""

import os
import time
import logging
import asyncio
import aiohttp
import aiofiles
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List, Tuple
from dataclasses import dataclass
from enum import Enum
import tempfile
import shutil

from .model_metadata import ModelMetadata, ModelFile, ModelStatus
from .storage_manager import StorageManager

logger = logging.getLogger("whispr.models.downloader")


class DownloadStatus(Enum):
    """Download status enumeration."""
    PENDING = "pending"
    DOWNLOADING = "downloading"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class DownloadProgress:
    """Progress information for a download."""
    model_id: str
    file_name: str
    bytes_downloaded: int
    total_bytes: int
    progress_percent: float
    download_speed: float  # bytes per second
    eta_seconds: Optional[float]  # estimated time remaining
    status: DownloadStatus
    error_message: Optional[str] = None
    
    @property
    def progress_mb(self) -> float:
        """Progress in MB."""
        return self.bytes_downloaded / (1024 * 1024)
    
    @property
    def total_mb(self) -> float:
        """Total size in MB."""
        return self.total_bytes / (1024 * 1024)
    
    @property
    def speed_mbps(self) -> float:
        """Download speed in MB/s."""
        return self.download_speed / (1024 * 1024)


class ModelDownloader:
    """Downloads voice models with progress tracking and error handling."""
    
    def __init__(self, storage_manager: StorageManager):
        """Initialize the model downloader.
        
        Args:
            storage_manager: Storage manager instance
        """
        self.logger = logging.getLogger("whispr.models.ModelDownloader")
        self.storage = storage_manager
        self._progress_callbacks: List[Callable[[DownloadProgress], None]] = []
        self._active_downloads: Dict[str, asyncio.Task] = {}
        self._download_queue: List[Tuple[ModelMetadata, ModelFile]] = []
        self._is_downloading = False
        
        # Configuration
        self.chunk_size = 8192  # 8KB chunks
        self.timeout = 30  # 30 second timeout
        self.max_retries = 3
        self.retry_delay = 1.0  # Initial retry delay in seconds
        self.max_concurrent_downloads = 2
    
    def add_progress_callback(self, callback: Callable[[DownloadProgress], None]):
        """Add a progress callback.
        
        Args:
            callback: Function to call with progress updates
        """
        if callback not in self._progress_callbacks:
            self._progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable[[DownloadProgress], None]):
        """Remove a progress callback.
        
        Args:
            callback: Function to remove
        """
        if callback in self._progress_callbacks:
            self._progress_callbacks.remove(callback)
    
    def _notify_progress(self, progress: DownloadProgress):
        """Notify all progress callbacks.
        
        Args:
            progress: Progress information
        """
        for callback in self._progress_callbacks:
            try:
                callback(progress)
            except Exception as e:
                self.logger.error(f"Error in progress callback: {e}")
    
    async def download_model(self, model: ModelMetadata) -> bool:
        """Download a complete model.
        
        Args:
            model: Model metadata to download
            
        Returns:
            True if download was successful
        """
        try:
            self.logger.info(f"Starting download for model: {model.id}")
            
            # Update model status
            model.update_status(ModelStatus.DOWNLOADING)
            
            # Create model directory
            model_dir = self.storage.create_model_directory(model.id)
            model.storage_path = str(model_dir)
            
            # Check available space
            if not self._check_available_space(model):
                error_msg = "Insufficient disk space for download"
                model.update_status(ModelStatus.ERROR, error_msg)
                return False
            
            # Download all required files
            total_files = len([f for f in model.files if f.required])
            completed_files = 0
            
            for file_info in model.files:
                if not file_info.required:
                    continue
                
                success = await self._download_file(model, file_info)
                if not success:
                    # Cleanup on failure
                    self._cleanup_partial_download(model)
                    return False
                
                completed_files += 1
                # Update overall model progress
                overall_progress = completed_files / total_files
                model.update_download_progress(overall_progress)
            
            # Mark model as downloaded
            model.update_status(ModelStatus.DOWNLOADED)
            self.logger.info(f"Successfully downloaded model: {model.id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error downloading model {model.id}: {e}")
            model.update_status(ModelStatus.ERROR, str(e))
            self._cleanup_partial_download(model)
            return False
    
    async def _download_file(self, model: ModelMetadata, file_info: ModelFile) -> bool:
        """Download a single file with progress tracking and resumption.
        
        Args:
            model: Model metadata
            file_info: File information
            
        Returns:
            True if download was successful
        """
        file_path = self.storage.get_file_path(model.id, file_info.name)
        temp_path = file_path.with_suffix(file_path.suffix + ".tmp")
        
        # Check if file already exists and is complete
        if file_path.exists() and file_path.stat().st_size == file_info.size:
            self.logger.info(f"File already exists and is complete: {file_info.name}")
            file_info.downloaded = True
            file_info.download_path = str(file_path)
            return True
        
        # Check for partial download
        resume_pos = 0
        if temp_path.exists():
            resume_pos = temp_path.stat().st_size
            if resume_pos >= file_info.size:
                # Temp file is larger than expected, remove it
                temp_path.unlink()
                resume_pos = 0
        
        retry_count = 0
        while retry_count <= self.max_retries:
            try:
                success = await self._download_file_with_progress(
                    model, file_info, temp_path, resume_pos
                )
                
                if success:
                    # Move temp file to final location
                    shutil.move(str(temp_path), str(file_path))
                    file_info.downloaded = True
                    file_info.download_path = str(file_path)
                    model.mark_file_downloaded(file_info.name, str(file_path))
                    return True
                else:
                    raise Exception("Download failed")
                    
            except Exception as e:
                retry_count += 1
                if retry_count <= self.max_retries:
                    delay = self.retry_delay * (2 ** (retry_count - 1))  # Exponential backoff
                    self.logger.warning(f"Download failed for {file_info.name}, retrying in {delay}s (attempt {retry_count}/{self.max_retries}): {e}")
                    await asyncio.sleep(delay)
                else:
                    self.logger.error(f"Download failed for {file_info.name} after {self.max_retries} retries: {e}")
                    # Cleanup temp file
                    if temp_path.exists():
                        temp_path.unlink()
                    return False
        
        return False
    
    async def _download_file_with_progress(
        self, 
        model: ModelMetadata, 
        file_info: ModelFile, 
        temp_path: Path, 
        resume_pos: int = 0
    ) -> bool:
        """Download a file with progress tracking.
        
        Args:
            model: Model metadata
            file_info: File information
            temp_path: Temporary file path
            resume_pos: Position to resume from
            
        Returns:
            True if download was successful
        """
        headers = {}
        if resume_pos > 0:
            headers['Range'] = f'bytes={resume_pos}-'
        
        start_time = time.time()
        last_update_time = start_time
        last_bytes = resume_pos
        
        timeout = aiohttp.ClientTimeout(total=None, connect=self.timeout)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(file_info.url, headers=headers) as response:
                if response.status not in [200, 206]:  # 206 for partial content
                    raise Exception(f"HTTP {response.status}: {response.reason}")
                
                # Get total size
                content_length = response.headers.get('content-length')
                if content_length:
                    if resume_pos > 0:
                        total_size = resume_pos + int(content_length)
                    else:
                        total_size = int(content_length)
                else:
                    total_size = file_info.size
                
                # Open file for writing
                mode = 'ab' if resume_pos > 0 else 'wb'
                async with aiofiles.open(temp_path, mode) as f:
                    bytes_downloaded = resume_pos
                    
                    async for chunk in response.content.iter_chunked(self.chunk_size):
                        await f.write(chunk)
                        bytes_downloaded += len(chunk)
                        
                        # Update progress
                        current_time = time.time()
                        if current_time - last_update_time >= 0.5:  # Update every 500ms
                            elapsed = current_time - start_time
                            if elapsed > 0:
                                speed = (bytes_downloaded - last_bytes) / (current_time - last_update_time)
                                
                                # Calculate ETA
                                remaining_bytes = total_size - bytes_downloaded
                                eta = remaining_bytes / speed if speed > 0 else None
                                
                                progress = DownloadProgress(
                                    model_id=model.id,
                                    file_name=file_info.name,
                                    bytes_downloaded=bytes_downloaded,
                                    total_bytes=total_size,
                                    progress_percent=(bytes_downloaded / total_size) * 100,
                                    download_speed=speed,
                                    eta_seconds=eta,
                                    status=DownloadStatus.DOWNLOADING
                                )
                                
                                self._notify_progress(progress)
                                last_update_time = current_time
                                last_bytes = bytes_downloaded
                
                # Final progress update
                progress = DownloadProgress(
                    model_id=model.id,
                    file_name=file_info.name,
                    bytes_downloaded=bytes_downloaded,
                    total_bytes=total_size,
                    progress_percent=100.0,
                    download_speed=0.0,
                    eta_seconds=0.0,
                    status=DownloadStatus.COMPLETED
                )
                self._notify_progress(progress)
                
                # Verify file size
                if bytes_downloaded != total_size:
                    raise Exception(f"Downloaded size {bytes_downloaded} doesn't match expected {total_size}")
                
                return True
    
    def _check_available_space(self, model: ModelMetadata) -> bool:
        """Check if there's enough disk space for the model.
        
        Args:
            model: Model metadata
            
        Returns:
            True if there's enough space
        """
        try:
            # Calculate total size needed
            total_size = sum(f.size for f in model.files if f.required)
            
            # Get available space
            storage_info = self.storage.get_storage_info()
            available_space = storage_info.get('available_space')
            
            if available_space is None:
                self.logger.warning("Could not determine available disk space")
                return True  # Assume we have space if we can't check
            
            # Add 10% buffer
            required_space = total_size * 1.1
            
            if available_space < required_space:
                self.logger.error(f"Insufficient disk space. Required: {required_space / (1024**3):.2f} GB, Available: {available_space / (1024**3):.2f} GB")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking disk space: {e}")
            return True  # Assume we have space if check fails
    
    def _cleanup_partial_download(self, model: ModelMetadata):
        """Clean up partial downloads on failure.
        
        Args:
            model: Model metadata
        """
        try:
            model_dir = self.storage.get_model_directory(model.id)
            if model_dir.exists():
                # Remove .tmp files
                for temp_file in model_dir.glob("*.tmp"):
                    temp_file.unlink()
                    self.logger.debug(f"Removed temp file: {temp_file}")
                
                # Remove incomplete files
                for file_info in model.files:
                    if file_info.required and not file_info.downloaded:
                        file_path = model_dir / file_info.name
                        if file_path.exists():
                            file_path.unlink()
                            self.logger.debug(f"Removed incomplete file: {file_path}")
                
                # Remove directory if empty
                if not any(model_dir.iterdir()):
                    model_dir.rmdir()
                    self.logger.debug(f"Removed empty model directory: {model_dir}")
                    
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def cancel_download(self, model_id: str):
        """Cancel an active download.
        
        Args:
            model_id: Model ID to cancel
        """
        if model_id in self._active_downloads:
            task = self._active_downloads[model_id]
            task.cancel()
            del self._active_downloads[model_id]
            self.logger.info(f"Cancelled download for model: {model_id}")
    
    def is_downloading(self, model_id: str) -> bool:
        """Check if a model is currently being downloaded.
        
        Args:
            model_id: Model ID to check
            
        Returns:
            True if model is being downloaded
        """
        return model_id in self._active_downloads
    
    def get_active_downloads(self) -> List[str]:
        """Get list of models currently being downloaded.
        
        Returns:
            List of model IDs being downloaded
        """
        return list(self._active_downloads.keys()) 