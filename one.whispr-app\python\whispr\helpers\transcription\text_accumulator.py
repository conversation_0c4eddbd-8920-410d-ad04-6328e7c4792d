"""
Text Accumulator for Real-time Transcription

Manages cumulative text building across sliding audio windows to preserve all transcribed content.
Handles text overlap detection, merging, and stability tracking.
"""

import logging
import time
from typing import Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class TextAccumulator:
    """Manages cumulative text building across sliding windows."""
    
    def __init__(self, stability_threshold: float = 3.0, min_stable_length: int = 10):
        """Initialize text accumulator.
        
        Args:
            stability_threshold: Seconds after which unchanged text is considered stable
            min_stable_length: Minimum character length for text to be considered stable
        """
        # Text buffers
        self.stable_text: str = ""           # Text that won't change anymore
        self.growing_text: str = ""          # Text still being refined
        self.complete_text: str = ""         # Full combined text for output
        
        # Stability tracking
        self.last_text: str = ""
        self.last_change_time: float = 0.0
        self.stability_threshold = stability_threshold
        self.min_stable_length = min_stable_length
        
        # Processing state
        self.last_update_time: float = 0.0
        self.update_count: int = 0
        
        logger.debug(f"TextAccumulator initialized: {stability_threshold}s stability threshold")
    
    def update(self, window_text: str, current_time: Optional[float] = None) -> Dict[str, Any]:
        """Update with new window transcription and return complete text.
        
        Args:
            window_text: New transcription from current audio window
            current_time: Current timestamp (defaults to time.time())
            
        Returns:
            Dictionary with complete text and metadata
        """
        if current_time is None:
            current_time = time.time()
        
        self.update_count += 1
        
        # Handle empty input
        if not window_text or not window_text.strip():
            return self._build_result(current_time, "empty_input")
        
        window_text = window_text.strip()
        
        # Check if this is the first update
        if not self.complete_text:
            self.growing_text = window_text
            self.complete_text = window_text
            self.last_text = window_text
            self.last_change_time = current_time
            self.last_update_time = current_time
            
            return self._build_result(current_time, "first_update")
        
        # Analyze the change from previous text
        change_info = self._analyze_text_change(self.growing_text, window_text)
        
        # Check for stability of current growing text
        time_since_change = current_time - self.last_change_time
        should_stabilize = self._should_stabilize_text(self.growing_text, time_since_change)
        
        if should_stabilize and self.growing_text and self.growing_text not in self.stable_text:
            # Move growing text to stable
            if self.stable_text:
                self.stable_text = self._merge_texts(self.stable_text, self.growing_text)
            else:
                self.stable_text = self.growing_text
            
            logger.debug(f"Stabilized text: '{self.growing_text}' -> stable buffer")
        
        # Determine how to handle the new window text
        if change_info["type"] == "no_change":
            # Text hasn't changed, just update timestamp
            self.last_update_time = current_time
        else:
            # Text has changed, update growing buffer
            self.growing_text = self._integrate_new_text(window_text)
            self.last_change_time = current_time
            self.last_update_time = current_time
        
        # Build complete text from stable + growing
        self.complete_text = self._build_complete_text()
        self.last_text = window_text
        
        return self._build_result(current_time, change_info["type"], change_info)
    
    def _analyze_text_change(self, old_text: str, new_text: str) -> Dict[str, Any]:
        """Analyze the type of change between old and new text.
        
        Args:
            old_text: Previous text
            new_text: New text
            
        Returns:
            Dictionary describing the change
        """
        if old_text == new_text:
            return {"type": "no_change", "description": "Text unchanged"}
        
        if not old_text:
            return {"type": "new_text", "description": "New text appeared"}
        
        if not new_text:
            return {"type": "text_cleared", "description": "Text was cleared"}
        
        # Check for simple addition (new text extends old text)
        if new_text.startswith(old_text):
            added_text = new_text[len(old_text):].strip()
            return {
                "type": "extension", 
                "description": f"Text extended",
                "added_text": added_text
            }
        
        # Check for truncation (old text was shortened)
        if old_text.startswith(new_text):
            removed_text = old_text[len(new_text):].strip()
            return {
                "type": "truncation",
                "description": "Text was shortened",
                "removed_text": removed_text
            }
        
        # Check for overlap (sliding window effect)
        overlap_info = self._find_text_overlap(old_text, new_text)
        if overlap_info["overlap_length"] > 0:
            return {
                "type": "overlap",
                "description": "Text has overlap (sliding window)",
                **overlap_info
            }
        
        # Complete replacement
        return {
            "type": "replacement",
            "description": "Text completely changed",
            "old_length": len(old_text),
            "new_length": len(new_text)
        }
    
    def _find_text_overlap(self, text1: str, text2: str) -> Dict[str, Any]:
        """Find overlap between two text strings.
        
        Args:
            text1: First text string
            text2: Second text string
            
        Returns:
            Dictionary with overlap information
        """
        words1 = text1.split()
        words2 = text2.split()
        
        max_overlap = 0
        best_overlap_text = ""
        
        # Try different overlap lengths starting from the end of text1
        for i in range(1, min(len(words1), len(words2)) + 1):
            end_words1 = words1[-i:]
            start_words2 = words2[:i]
            
            if end_words1 == start_words2:
                if i > max_overlap:
                    max_overlap = i
                    best_overlap_text = " ".join(end_words1)
        
        return {
            "overlap_length": max_overlap,
            "overlap_text": best_overlap_text,
            "overlap_words": max_overlap
        }
    
    def _should_stabilize_text(self, text: str, time_unchanged: float) -> bool:
        """Determine if text should be moved to stable buffer.
        
        Args:
            text: Text to evaluate for stability
            time_unchanged: How long the text has been unchanged
            
        Returns:
            True if text should be stabilized
        """
        if not text or len(text) < self.min_stable_length:
            return False
        
        # Check time threshold
        if time_unchanged < self.stability_threshold:
            return False
        
        # Check for sentence-like endings (good stability indicators)
        if text.strip().endswith(('.', '!', '?')):
            return True
        
        # For longer text, we can be more lenient
        if len(text) > 30 and time_unchanged >= self.stability_threshold:
            return True
        
        return False
    
    def _integrate_new_text(self, new_text: str) -> str:
        """Integrate new window text with existing context.
        
        Args:
            new_text: New text from transcription window
            
        Returns:
            Updated growing text
        """
        # For now, we'll use a simple approach: 
        # If the new text seems to be a continuation, use it directly
        # More sophisticated overlap handling could be added here
        
        # Check if new text overlaps with stable text
        if self.stable_text:
            overlap_info = self._find_text_overlap(self.stable_text, new_text)
            if overlap_info["overlap_length"] > 0:
                # Remove overlapping part from new text
                overlap_words = overlap_info["overlap_length"]
                new_words = new_text.split()[overlap_words:]
                return " ".join(new_words) if new_words else ""
        
        return new_text
    
    def _merge_texts(self, text1: str, text2: str) -> str:
        """Merge two text strings intelligently.
        
        Args:
            text1: First text (usually stable text)
            text2: Second text (usually growing text)
            
        Returns:
            Merged text
        """
        if not text1:
            return text2
        if not text2:
            return text1
        
        # Check for overlap and merge accordingly
        overlap_info = self._find_text_overlap(text1, text2)
        
        if overlap_info["overlap_length"] > 0:
            # Remove overlapping part from second text
            words2 = text2.split()
            non_overlap_words = words2[overlap_info["overlap_length"]:]
            if non_overlap_words:
                return f"{text1} {' '.join(non_overlap_words)}"
            else:
                return text1
        else:
            # No overlap, just concatenate with space
            return f"{text1} {text2}"
    
    def _build_complete_text(self) -> str:
        """Build the complete text from stable and growing buffers.
        
        Returns:
            Complete accumulated text
        """
        if not self.stable_text:
            return self.growing_text
        
        if not self.growing_text:
            return self.stable_text
        
        return self._merge_texts(self.stable_text, self.growing_text)
    
    def _build_result(self, current_time: float, change_type: str, 
                     change_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Build result dictionary with complete text and metadata.
        
        Args:
            current_time: Current timestamp
            change_type: Type of change that occurred
            change_info: Additional change information
            
        Returns:
            Result dictionary
        """
        time_since_change = current_time - self.last_change_time
        
        return {
            "complete_text": self.complete_text,
            "stable_text": self.stable_text,
            "growing_text": self.growing_text,
            "change_type": change_type,
            "change_info": change_info or {},
            "is_stable_portion": bool(self.stable_text),
            "time_since_last_change": time_since_change,
            "update_count": self.update_count,
            "text_length": len(self.complete_text),
            "stable_length": len(self.stable_text),
            "growing_length": len(self.growing_text)
        }
    
    def finalize(self) -> str:
        """Finalize and return the complete accumulated text.
        
        Returns:
            Final complete text
        """
        # Move any remaining growing text to stable
        if self.growing_text and self.growing_text not in self.stable_text:
            if self.stable_text:
                self.stable_text = self._merge_texts(self.stable_text, self.growing_text)
            else:
                self.stable_text = self.growing_text
            
            self.complete_text = self.stable_text
            self.growing_text = ""
        
        logger.info(f"Text accumulation finalized: '{self.complete_text}'")
        return self.complete_text
    
    def reset(self) -> None:
        """Reset all text buffers and state."""
        self.stable_text = ""
        self.growing_text = ""
        self.complete_text = ""
        self.last_text = ""
        self.last_change_time = 0.0
        self.last_update_time = 0.0
        self.update_count = 0
        
        logger.debug("TextAccumulator reset")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of text accumulator.
        
        Returns:
            Status dictionary
        """
        current_time = time.time()
        time_since_change = current_time - self.last_change_time if self.last_change_time > 0 else 0.0
        
        return {
            "complete_text_length": len(self.complete_text),
            "stable_text_length": len(self.stable_text),
            "growing_text_length": len(self.growing_text),
            "update_count": self.update_count,
            "time_since_last_change": time_since_change,
            "has_stable_text": bool(self.stable_text),
            "has_growing_text": bool(self.growing_text)
        } 