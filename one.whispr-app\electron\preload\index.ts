import { contextBridge } from 'electron';
import { setupAPI as setupAuth } from './auth';
import { setupAPI as setupBackend } from './backend';

// Expose protected methods that allow the renderer process to use IPC
contextBridge.exposeInMainWorld('electron', {
  ...setupAuth(),
  ...setupBackend()
});

// Export types for TypeScript support (excluding setup functions)
export type {
  AuthCallbackData,
  User,
  UserSession,
  AuthSettings,
  AuthStatus,
  AuthSuccessData,
  AuthErrorData,
  AuthStateChangeData
} from './auth';

export type {
  BackendStatusData
} from './backend';
