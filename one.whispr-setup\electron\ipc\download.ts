import { ipcMain } from 'electron';

/**
 * Debug function to log service availability
 */
function debugServices(services: any): void {
  console.log('[IPC] Service availability check:', {
    hasMainAppDownloader: !!services.mainAppDownloader,
    hasBackendDownloader: !!services.backendDownloader,
    mainAppDownloaderType: services.mainAppDownloader?.constructor?.name,
    backendDownloaderType: services.backendDownloader?.constructor?.name,
    servicesKeys: Object.keys(services || {})
  });
}

/**
 * Setup IPC handlers for download operations (main app and backend)
 */
export function setupDownloadIpc(services: any): void {
  // Debug service availability
  debugServices(services);
  
  // Remove existing handlers first to prevent duplicates during hot reload
  ipcMain.removeHandler('download:start');
  ipcMain.removeHandler('download:cancel');
  ipcMain.removeHandler('download:check-needed');
  ipcMain.removeHandler('download:get-manifest');
  ipcMain.removeHandler('backend:check-update');
  ipcMain.removeHandler('backend:download');
  ipcMain.removeHandler('backend:cancel');

  // Main app download handlers
  ipcMain.handle('download:start', async () => {
    if (!services.mainAppDownloader) {
      throw new Error('Main app downloader service not available');
    }
    return services.mainAppDownloader.startDownload();
  });

  ipcMain.handle('download:cancel', () => {
    if (!services.mainAppDownloader) {
      throw new Error('Main app downloader service not available');
    }
    return services.mainAppDownloader.cancelDownload();
  });

  ipcMain.handle('download:check-needed', async () => {
    if (!services.mainAppDownloader) {
      throw new Error('Main app downloader service not available');
    }
    return services.mainAppDownloader.checkDownloadNeeded();
  });

  ipcMain.handle('download:get-manifest', () => {
    if (!services.mainAppDownloader) {
      throw new Error('Main app downloader service not available');
    }
    return services.mainAppDownloader.getManifest();
  });

  // Backend download handlers
  ipcMain.handle('backend:check-update', async () => {
    if (!services.backendDownloader) {
      throw new Error('Backend downloader service not available');
    }
    return services.backendDownloader.checkBackendUpdate();
  });

  ipcMain.handle('backend:download', async () => {
    console.log('[IPC] Backend download requested');
    if (!services.backendDownloader) {
      console.error('[IPC] Backend downloader service not available');
      throw new Error('Backend downloader service not available');
    }
    console.log('[IPC] Calling backend downloader...');
    const result = await services.backendDownloader.downloadBackend();
    console.log('[IPC] Backend download result:', result);
    return result;
  });

  ipcMain.handle('backend:cancel', () => {
    if (!services.backendDownloader) {
      throw new Error('Backend downloader service not available');
    }
    return services.backendDownloader.cancelDownload();
  });

  console.log('[IPC] Download handlers registered');
}
