/**
 * TryIt Types
 * Types related to transcription testing and final validation
 */

import type { TranscriptionState } from './core';

// ============================================================================
// TRYIT TYPES
// ============================================================================

export interface TryItState {
  transcriptionState: TranscriptionState;
  transcriptionText: string;
  isRecording: boolean;
  hasSuccessfulTest: boolean;
  error: string | null;
}

export interface TryItActions {
  startRecording: () => Promise<void>;
  stopRecording: () => Promise<void>;
  clearTranscription: () => void;
  clearError: () => void;
}

export interface TryItContextValue {
  state: TryItState;
  actions: TryItActions;
} 