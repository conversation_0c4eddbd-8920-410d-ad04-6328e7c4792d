const fs = require('fs-extra');
const path = require('path');

async function prepareMicrosoftBuild() {
  console.log('Preparing Microsoft Store build...');
  
  const mainAppPath = path.join(__dirname, '../../one.whispr-app/.release/win-unpacked');
  const appAsarPath = path.join(mainAppPath, 'resources', 'app.asar');
  const appAsarEmbeddedPath = path.join(mainAppPath, 'resources', 'app.asar.embedded');
  
  try {
    // Check if app.asar exists
    if (await fs.pathExists(appAsarPath)) {
      console.log('Found app.asar, renaming to app.asar.embedded...');
      
      // Remove existing app.asar.embedded if it exists
      if (await fs.pathExists(appAsarEmbeddedPath)) {
        await fs.remove(appAsarEmbeddedPath);
        console.log('Removed existing app.asar.embedded');
      }
      
      // Rename app.asar to app.asar.embedded
      await fs.move(appAsarPath, appAsarEmbeddedPath);
      console.log('Renamed app.asar to app.asar.embedded');
    } else {
      console.log('app.asar not found at:', appAsarPath);
    }
    
    console.log('Microsoft Store build preparation complete!');
  } catch (error) {
    console.error('Error preparing Microsoft Store build:', error);
    process.exit(1);
  }
}

async function restoreAfterBuild() {
  console.log('Restoring app.asar after Microsoft Store build...');
  
  const mainAppPath = path.join(__dirname, '../../one.whispr-app/.release/win-unpacked');
  const appAsarPath = path.join(mainAppPath, 'resources', 'app.asar');
  const appAsarEmbeddedPath = path.join(mainAppPath, 'resources', 'app.asar.embedded');
  
  try {
    // Restore app.asar.embedded back to app.asar
    if (await fs.pathExists(appAsarEmbeddedPath)) {
      console.log('Found app.asar.embedded, restoring to app.asar...');
      
      // Remove existing app.asar if it exists
      if (await fs.pathExists(appAsarPath)) {
        await fs.remove(appAsarPath);
        console.log('Removed existing app.asar');
      }
      
      // Rename app.asar.embedded back to app.asar
      await fs.move(appAsarEmbeddedPath, appAsarPath);
      console.log('Restored app.asar.embedded to app.asar');
    } else {
      console.log('app.asar.embedded not found at:', appAsarEmbeddedPath);
    }
    
    console.log('Restoration complete!');
  } catch (error) {
    console.error('Error restoring after Microsoft Store build:', error);
    process.exit(1);
  }
}

// Check command line argument to determine action
const action = process.argv[2];

if (action === 'prepare') {
  prepareMicrosoftBuild();
} else if (action === 'restore') {
  restoreAfterBuild();
} else {
  console.log('Usage: node prepare-microsoft-build.js [prepare|restore]');
  process.exit(1);
} 