"""
Model service for One.Whispr voice models.

This service provides high-level model management operations
including listing, downloading, and status tracking.
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Callable
from ..core.base import BaseService, Event
from ..helpers.models import (
    ModelRegistry, StorageManager, ModelMetadata, ModelStatus, VOICE_MODELS,
    ModelDownloader, DownloadProgress, DownloadStatus, ModelValidator, ValidationReport,
    ModelLoader, LoadedModel, LoadingState, LoadingProgress,
    MemoryManager, MemoryPressure, MemoryThresholds, MemoryEvent, MemoryStats,
    HardwareDetector, HardwareConfig, MemoryInfo
)
# Pre-initialize libraries for faster model loading
from ..helpers.models.model_loader import _initialize_libraries, get_initialization_status

logger = logging.getLogger("whispr.services.model")


class ModelService(BaseService):
    """Service for managing voice models."""
    
    def __init__(self, service_container=None):
        """Initialize the model service."""
        super().__init__(service_container)
        self.registry = VOICE_MODELS
        self.storage = StorageManager()
        self.downloader = ModelDownloader(self.storage)
        self.validator = ModelValidator(self.storage)
        self.loader = ModelLoader(self.storage)
        self.memory_manager = MemoryManager()
        self._active_model_id: Optional[str] = None
        self._loaded_model: Optional[LoadedModel] = None
        self._progress_callbacks: List[Callable] = []
        
        # Set up downloader progress callbacks
        self.downloader.add_progress_callback(self._on_download_progress)
        
        # Set up loader progress callbacks
        self.loader.add_progress_callback(self._on_loading_progress)
        
        # Start memory monitoring
        self.memory_manager.start_monitoring()
        
        # ML libraries are now pre-initialized early in main.py startup
        logger.info("ML libraries should already be initializing in background")
    
    async def initialize(self) -> bool:
        """Initialize the model service.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Initialize model statuses based on storage
            self._sync_model_statuses()
            
            self.logger.info("ModelService initialized successfully")
            return await super().initialize()
        except Exception as e:
            self.logger.error(f"Error initializing ModelService: {e}")
            return False
    
    def _sync_model_statuses(self):
        """Sync model statuses with actual storage state."""
        for model in self.registry.get_all_models():
            if self.storage.model_exists(model.id):
                # Check if all required files exist
                missing_files = []
                for file_info in model.files:
                    if file_info.required and not self.storage.file_exists(model.id, file_info.name):
                        missing_files.append(file_info.name)
                
                if not missing_files:
                    model.update_status(ModelStatus.DOWNLOADED)
                    model.storage_path = str(self.storage.get_model_directory(model.id))
                    
                    # Mark files as downloaded
                    for file_info in model.files:
                        if self.storage.file_exists(model.id, file_info.name):
                            file_path = str(self.storage.get_file_path(model.id, file_info.name))
                            model.mark_file_downloaded(file_info.name, file_path)
                else:
                    self.logger.warning(f"Model {model.id} is incomplete, missing files: {missing_files}")
                    model.update_status(ModelStatus.ERROR, f"Missing files: {', '.join(missing_files)}")
    
    # Model Information & Status
    def get_all_models(self) -> List[Dict[str, Any]]:
        """Get all available models with their current status.
        
        Returns:
            List of model dictionaries with metadata and status
        """
        models = []
        for model in self.registry.get_all_models():
            model_dict = model.to_dict()
            models.append(model_dict)
        
        return models
    
    def get_model(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific model by ID.
        
        Args:
            model_id: Model ID to retrieve
            
        Returns:
            Model dictionary or None if not found
        """
        model = self.registry.get_model(model_id)
        if model:
            return model.to_dict()
        return None
    
    def get_downloaded_models(self) -> List[Dict[str, Any]]:
        """Get all downloaded models.
        
        Returns:
            List of downloaded model dictionaries
        """
        downloaded = []
        for model in self.registry.get_all_models():
            if model.is_downloaded:
                downloaded.append(model.to_dict())
        
        return downloaded
    
    def is_model_downloaded(self, model_id: str) -> bool:
        """Check if a model is downloaded.
        
        Args:
            model_id: Model ID to check
            
        Returns:
            True if model is downloaded
        """
        model = self.registry.get_model(model_id)
        return model.is_downloaded if model else False
    
    def get_model_status(self, model_id: str) -> Optional[str]:
        """Get the status of a model.
        
        Args:
            model_id: Model ID to check
            
        Returns:
            Model status string or None if model not found
        """
        model = self.registry.get_model(model_id)
        return model.status.value if model else None
    
    def delete_model(self, model_id: str) -> bool:
        """Delete a downloaded model.
        
        Args:
            model_id: Model ID to delete
            
        Returns:
            True if deletion was successful
        """
        model = self.registry.get_model(model_id)
        if not model:
            self.logger.error(f"Model not found: {model_id}")
            return False
        
        if not model.is_downloaded:
            self.logger.warning(f"Model {model_id} is not downloaded")
            return True
        
        # Delete from storage
        success = self.storage.delete_model(model_id)
        
        if success:
            # Update model status
            model.update_status(ModelStatus.NOT_DOWNLOADED)
            model.storage_path = None
            model.download_progress = 0.0
            
            # Mark all files as not downloaded
            for file_info in model.files:
                file_info.downloaded = False
                file_info.download_path = None
            
            self.logger.info(f"Successfully deleted model: {model_id}")
            
            # If this was the active model, clear it
            if self._active_model_id == model_id:
                self._active_model_id = None
        
        return success
    
    def get_storage_info(self) -> Dict[str, Any]:
        """Get storage information.
        
        Returns:
            Dictionary with storage information
        """
        return self.storage.get_storage_info()
    
    def cleanup_storage(self):
        """Clean up empty directories in storage."""
        self.storage.cleanup_empty_directories()
    
    # Active Model Management
    def set_active_model(self, model_id: str) -> bool:
        """Set the active model.
        
        Args:
            model_id: Model ID to set as active
            
        Returns:
            True if successful
        """
        if not self.registry.has_model(model_id):
            self.logger.error(f"Unknown model: {model_id}")
            return False
        
        if not self.is_model_downloaded(model_id):
            self.logger.error(f"Model not downloaded: {model_id}")
            return False
        
        self._active_model_id = model_id
        self.logger.info(f"Set active model: {model_id}")
        return True
    
    def get_active_model(self) -> Optional[str]:
        """Get the currently active model ID from the active mode.
        
        Returns:
            Active model ID or None
        """
        try:
            active_mode = self.get_active_mode()
            if active_mode:
                return active_mode.get("voiceModel")
            return None
        except Exception as e:
            self.logger.error(f"Error getting active model from mode: {e}")
            return None
    
    def get_active_model_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the active model.
        
        Returns:
            Active model dictionary or None
        """
        active_model_id = self.get_active_model()
        if active_model_id:
            return self.get_model(active_model_id)
        return None
    
    # Progress Callbacks
    def add_progress_callback(self, callback: Callable):
        """Add a progress callback for download operations.
        
        Args:
            callback: Function to call with progress updates
        """
        if callback not in self._progress_callbacks:
            self._progress_callbacks.append(callback)
    
    def remove_progress_callback(self, callback: Callable):
        """Remove a progress callback.
        
        Args:
            callback: Function to remove
        """
        if callback in self._progress_callbacks:
            self._progress_callbacks.remove(callback)
    
    def _notify_progress(self, model_id: str, progress: float, message: str = ""):
        """Notify all progress callbacks.
        
        Args:
            model_id: Model ID being processed
            progress: Progress value (0.0 to 1.0)
            message: Optional progress message
        """
        for callback in self._progress_callbacks:
            try:
                callback(model_id, progress, message)
            except Exception as e:
                self.logger.error(f"Error in progress callback: {e}")
    
    def get_recommended_models(self) -> List[Dict[str, Any]]:
        """Get recommended models for new users.
        
        Returns:
            List of recommended model dictionaries
        """
        recommended_models = self.registry.get_recommended_models()
        return [model.to_dict() for model in recommended_models]
    
    # Download Management
    def _on_download_progress(self, progress: DownloadProgress):
        """Handle download progress updates from the downloader.
        
        Args:
            progress: Download progress information
        """
        # Update model metadata with progress
        model = self.registry.get_model(progress.model_id)
        if model:
            # Convert file progress to overall model progress
            # This is a simplified approach - could be more sophisticated
            model.update_download_progress(progress.progress_percent / 100.0)
        
        # Notify our own progress callbacks
        self._notify_progress(progress.model_id, progress.progress_percent / 100.0, 
                            f"Downloading {progress.file_name}: {progress.progress_percent:.1f}%")
        
        # Dispatch WebSocket event for real-time progress updates
        self._dispatch_progress_event(progress)
    
    def _dispatch_progress_event(self, progress: DownloadProgress):
        """Dispatch a WebSocket event for download progress.
        
        Args:
            progress: Download progress information
        """
        try:
            # Get WebSocket server
            websocket_server = self.get_service("websocket")
            if not websocket_server:
                self.logger.warning("WebSocket server not available for progress events")
                return
            
            # Format speed for display
            speed_str = None
            if progress.download_speed > 0:
                if progress.download_speed > 1024 * 1024:  # MB/s
                    speed_str = f"{progress.download_speed / (1024 * 1024):.1f} MB/s"
                elif progress.download_speed > 1024:  # KB/s
                    speed_str = f"{progress.download_speed / 1024:.1f} KB/s"
                else:  # B/s
                    speed_str = f"{progress.download_speed:.0f} B/s"
            
            # Create event data
            event_data = {
                "model_id": progress.model_id,
                "progress": progress.progress_percent,
                "status": progress.status.value,
                "bytes_downloaded": progress.bytes_downloaded,
                "total_bytes": progress.total_bytes,
                "speed": speed_str
            }
            
            self.logger.info(f"Dispatching download progress event: {event_data}")
            
            # Create and dispatch event
            event = Event("model_download_progress", event_data)
            
            # Use asyncio to dispatch the event if we're in an event loop
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Schedule the event dispatch as a task
                    asyncio.create_task(websocket_server.dispatch_event(event))
                    self.logger.debug(f"Scheduled progress event dispatch for model {progress.model_id}")
                else:
                    # Run in new event loop (should not happen in practice)
                    loop.run_until_complete(websocket_server.dispatch_event(event))
                    self.logger.debug(f"Dispatched progress event for model {progress.model_id}")
            except Exception as e:
                self.logger.error(f"Error dispatching progress event: {e}")
                
        except Exception as e:
            self.logger.error(f"Error creating progress event: {e}")
    
    def _on_loading_progress(self, progress: LoadingProgress):
        """Handle loading progress updates from the loader.
        
        Args:
            progress: Loading progress information
        """
        # Notify our own progress callbacks
        self._notify_progress(progress.model_id, progress.progress_percent / 100.0, 
                            f"Loading: {progress.current_step}")
        
        # Dispatch WebSocket event for real-time progress updates
        self._dispatch_loading_progress_event(progress)
    
    def _dispatch_loading_progress_event(self, progress: LoadingProgress):
        """Dispatch a WebSocket event for loading progress.
        
        Args:
            progress: Loading progress information
        """
        try:
            # Get WebSocket server
            websocket_server = self.get_service("websocket")
            if not websocket_server:
                self.logger.warning("WebSocket server not available for loading progress events")
                return
            
            # Create event data
            event_data = {
                "model_id": progress.model_id,
                "status": progress.state.value,
                "stage": progress.current_step,
                "progress": progress.progress_percent
            }
            
            self.logger.info(f"Dispatching loading progress event: {event_data}")
            
            # Create and dispatch event
            event = Event("model_loading_progress", event_data)
            
            # Use asyncio to dispatch the event if we're in an event loop
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Schedule the event dispatch as a task
                    asyncio.create_task(websocket_server.dispatch_event(event))
                    self.logger.debug(f"Scheduled loading progress event dispatch for model {progress.model_id}")
                else:
                    # Run in new event loop (should not happen in practice)
                    loop.run_until_complete(websocket_server.dispatch_event(event))
                    self.logger.debug(f"Dispatched loading progress event for model {progress.model_id}")
            except Exception as e:
                self.logger.error(f"Error dispatching loading progress event: {e}")
                
        except Exception as e:
            self.logger.error(f"Error creating loading progress event: {e}")
    
    async def download_model_async(self, model_id: str) -> bool:
        """Download a model asynchronously.
        
        Args:
            model_id: Model ID to download
            
        Returns:
            True if download was successful
        """
        model = self.registry.get_model(model_id)
        if not model:
            self.logger.error(f"Model not found: {model_id}")
            return False
        
        if model.is_downloaded:
            self.logger.info(f"Model already downloaded: {model_id}")
            return True
        
        try:
            success = await self.downloader.download_model(model)
            if success:
                # Update registry with the new metadata
                self.registry.update_model_metadata(model_id, model)
            return success
        except Exception as e:
            self.logger.error(f"Error downloading model {model_id}: {e}")
            model.update_status(ModelStatus.ERROR, str(e))
            return False
    
    def download_model(self, model_id: str) -> bool:
        """Download a model (synchronous wrapper).
        
        Args:
            model_id: Model ID to download
            
        Returns:
            True if download was successful
        """
        try:
            # Run the async download in the event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If we're already in an event loop, create a task
                task = loop.create_task(self.download_model_async(model_id))
                return True  # Return True to indicate download started
            else:
                # Run in new event loop
                return loop.run_until_complete(self.download_model_async(model_id))
        except Exception as e:
            self.logger.error(f"Error starting download for {model_id}: {e}")
            return False
    
    def cancel_download(self, model_id: str) -> bool:
        """Cancel an active download.
        
        Args:
            model_id: Model ID to cancel
            
        Returns:
            True if cancellation was successful
        """
        try:
            self.downloader.cancel_download(model_id)
            
            # Update model status
            model = self.registry.get_model(model_id)
            if model:
                model.update_status(ModelStatus.NOT_DOWNLOADED)
                model.download_progress = 0.0
            
            return True
        except Exception as e:
            self.logger.error(f"Error cancelling download for {model_id}: {e}")
            return False
    
    def is_downloading(self, model_id: str) -> bool:
        """Check if a model is currently being downloaded.
        
        Args:
            model_id: Model ID to check
            
        Returns:
            True if model is being downloaded
        """
        return self.downloader.is_downloading(model_id)
    
    def get_active_downloads(self) -> List[str]:
        """Get list of models currently being downloaded.
        
        Returns:
            List of model IDs being downloaded
        """
        return self.downloader.get_active_downloads()
    
    def get_download_progress(self, model_id: str) -> Optional[Dict[str, Any]]:
        """Get download progress for a model.
        
        Args:
            model_id: Model ID to check
            
        Returns:
            Progress information or None
        """
        model = self.registry.get_model(model_id)
        if not model:
            return None
        
        return {
            "model_id": model_id,
            "progress_percent": model.download_progress_percent,
            "status": model.status.value,
            "is_downloading": self.is_downloading(model_id),
            "error": model.error_message
        }
    
    # Model Validation
    async def validate_model(self, model_id: str) -> ValidationReport:
        """Validate a model installation.
        
        Args:
            model_id: Model ID to validate
            
        Returns:
            Validation report with detailed results
        """
        return await self.validator.validate_model(model_id)
    
    async def validate_all_models(self) -> Dict[str, ValidationReport]:
        """Validate all downloaded models.
        
        Returns:
            Dictionary mapping model IDs to validation reports
        """
        results = {}
        for model in self.get_downloaded_models():
            model_id = model['id']
            results[model_id] = await self.validate_model(model_id)
        
        return results
    
    def get_model_format_info(self, model_id: str) -> Dict[str, Any]:
        """Get format information for a model.
        
        Args:
            model_id: Model ID to get format info for
            
        Returns:
            Dictionary with format information
        """
        return self.validator.get_model_format_info(model_id)
    
    # Model Loading & Memory Management
    async def load_model(self, model_id: str) -> bool:
        """Load a model into memory for use.
        
        Args:
            model_id: Model ID to load
            
        Returns:
            True if model was loaded successfully
        """
        try:
            # Check if model is downloaded
            if not self.is_model_downloaded(model_id):
                self.logger.error(f"Cannot load model {model_id}: not downloaded")
                return False
            
            # Check memory requirements
            model = self.registry.get_model(model_id)
            if model:
                model_size = model.size_bytes
                # Include GPU checks when loading models since we'll need PyTorch anyway
                can_load, reason = self.memory_manager.check_memory_for_operation(model_size, include_gpu=True)
                if not can_load:
                    self.logger.error(f"Cannot load model {model_id}: {reason}")
                    return False
                
                # Prepare memory for loading
                await self.memory_manager.prepare_for_operation(model_size)
            
            # Load the model
            loaded_model = await self.loader.load_model(model_id)
            self._loaded_model = loaded_model
            self._active_model_id = model_id
            
            self.logger.info(f"Successfully loaded model: {model_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error loading model {model_id}: {e}")
            return False
    
    async def unload_model(self) -> bool:
        """Unload the currently loaded model.
        
        Returns:
            True if model was unloaded successfully
        """
        try:
            success = await self.loader.unload_model()
            if success:
                self._loaded_model = None
                self._active_model_id = None
                self.logger.info("Successfully unloaded model")
            return success
        except Exception as e:
            self.logger.error(f"Error unloading model: {e}")
            return False
    
    async def switch_model(self, model_id: str) -> bool:
        """Switch to a different model.
        
        Args:
            model_id: Model ID to switch to
            
        Returns:
            True if model was switched successfully
        """
        try:
            # Check if model is downloaded
            if not self.is_model_downloaded(model_id):
                self.logger.error(f"Cannot switch to model {model_id}: not downloaded")
                return False
            
            # Switch models (this will automatically unload current and load new)
            loaded_model = await self.loader.switch_model(model_id)
            self._loaded_model = loaded_model
            self._active_model_id = model_id
            
            self.logger.info(f"Successfully switched to model: {model_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error switching to model {model_id}: {e}")
            return False
    
    def get_loaded_model(self) -> Optional[Dict[str, Any]]:
        """Get information about the currently loaded model.
        
        Returns:
            Loaded model information or None
        """
        if self._loaded_model:
            return {
                "model_id": self._loaded_model.model_id,
                "device": self._loaded_model.device,
                "dtype": self._loaded_model.dtype,
                "memory_usage": self._loaded_model.memory_usage,
                "load_time": self._loaded_model.load_time,
                "model_size": self._loaded_model.model_size,
                "config": self._loaded_model.config
            }
        return None
    
    def get_loading_state(self) -> str:
        """Get the current loading state.
        
        Returns:
            Current loading state as string
        """
        return self.loader.get_loading_state().value
    
    def is_model_loaded(self, model_id: str) -> bool:
        """Check if a specific model is currently loaded.
        
        Args:
            model_id: Model ID to check
            
        Returns:
            True if the model is loaded
        """
        return self.loader.is_model_loaded(model_id)
    
    def get_memory_info(self, include_gpu: bool = False) -> Dict[str, Any]:
        """Get current memory usage information.
        
        Args:
            include_gpu: Whether to include GPU memory info (requires PyTorch import)
        
        Returns:
            Memory information dictionary
        """
        return self.memory_manager.to_dict(include_gpu=include_gpu)
    
    def get_hardware_info(self) -> Dict[str, Any]:
        """Get hardware configuration information.
        
        Returns:
            Hardware configuration dictionary
        """
        hardware_config = self.loader.get_hardware_config()
        return hardware_config.__dict__ if hardware_config else {}
    
    def get_library_initialization_status(self) -> Dict[str, Any]:
        """Get the status of background ML library initialization.
        
        Returns:
            Dictionary with initialization status information
        """
        return get_initialization_status()
    
    # Service Management
    async def cleanup(self):
        """Clean up service resources."""
        try:
            # Stop memory monitoring
            self.memory_manager.stop_monitoring()
            
            # Unload any loaded model
            if self._loaded_model:
                await self.unload_model()
            
            # Cancel any active downloads
            for model_id in self.get_active_downloads():
                self.cancel_download(model_id)
            
            self.logger.info("ModelService cleanup completed")
        except Exception as e:
            self.logger.error(f"Error during ModelService cleanup: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current service status.
        
        Returns:
            A dictionary containing status information
        """
        status = super().get_status()
        
        # Add model-specific status information
        all_models = self.registry.get_all_models()
        downloaded_count = len([m for m in all_models if m.is_downloaded])
        active_downloads = self.get_active_downloads()
        
        status.update({
            "total_models": len(all_models),
            "downloaded_models": downloaded_count,
            "active_downloads": len(active_downloads),
            "active_model": self._active_model_id,
            "loaded_model": self.get_loaded_model(),
            "loading_state": self.get_loading_state(),
            "memory_info": self.get_memory_info(),
            "hardware_info": self.get_hardware_info(),
            "storage_info": self.get_storage_info()
        })
        
        return status