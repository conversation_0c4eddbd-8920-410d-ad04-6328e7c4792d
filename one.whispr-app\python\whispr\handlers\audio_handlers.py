"""
Audio Handlers V2.0

Simple command routing for audio operations without business logic.
Clean response formatting and error handling.
"""

import logging
from typing import Dict, Any, Optional
import time

from whispr.core.response_utils import success_response, error_response, ErrorCodes

logger = logging.getLogger(__name__)

def _get_audio_service(context: Dict[str, Any]):
    """Get audio service from context."""
    try:
        service_container = context.get("service_container")
        if not service_container:
            logger.debug("Service container not found in context")
            return None

        audio_service = service_container.resolve("audio")
        if not audio_service:
            logger.debug("Audio service not found in service container (may not be initialized yet)")
            return None

        return audio_service

    except Exception as e:
        logger.debug(f"Failed to get audio service: {e}")
        return None

# Device Management Handlers
async def handle_get_devices(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get all available audio devices."""
    try:
        service_container = context.get('service_container')
        if not service_container:
            return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)

        audio_service = service_container.resolve("audio")
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)

        devices = audio_service.get_devices()
        return success_response({"devices": devices}, "Retrieved devices successfully")

    except Exception as e:
        logger.error(f"Failed to get devices: {e}")
        return error_response(f"Failed to get devices: {str(e)}", ErrorCodes.DEVICE_ERROR)


async def handle_refresh_devices(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Refresh device list and return updated devices."""
    try:
        service_container = context.get('service_container')
        if not service_container:
            return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)

        audio_service = service_container.resolve("audio")
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)

        # Check if force refresh is requested
        force = params.get("force", False)

        # Refresh devices
        devices = audio_service.refresh_devices(force=force)
        return success_response({"devices": devices}, "Refreshed devices successfully")

    except Exception as e:
        logger.error(f"Failed to refresh devices: {e}")
        return error_response(f"Failed to refresh devices: {str(e)}", ErrorCodes.DEVICE_ERROR)


async def handle_test_device(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Test an audio device."""
    try:
        service_container = context.get('service_container')
        if not service_container:
            return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)

        audio_service = service_container.resolve("audio")
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Get parameters
        device_id = params.get("deviceId")
        device_type = params.get("deviceType", "input")
        duration = params.get("duration", 1.0)
        
        if not device_id:
            return error_response("Device ID is required", ErrorCodes.PARAMETER_ERROR)
        
        # Test device
        result = audio_service.test_device(device_id, device_type, duration)
        
        if result.get("success", False):
            return success_response(
                {"test_result": result},
                f"Device {device_id} tested successfully"
            )
        else:
            return error_response(
                result.get("error", "Unknown test error"),
                ErrorCodes.DEVICE_ERROR
            )
        
    except Exception as e:
        logger.error(f"Failed to test device: {e}")
        return error_response(f"Failed to test device: {str(e)}", ErrorCodes.DEVICE_ERROR)


# Device setting handlers removed - devices are now configured via database push architecture
# The database pushes device settings to ConfigurationManager which services read from


async def handle_get_selected_devices(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get currently selected devices."""
    try:
        service_container = context.get('service_container')
        if not service_container:
            return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)

        audio_service = service_container.resolve("audio")
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)

        selected_devices = audio_service.get_selected_devices()
        return success_response({"selected_devices": selected_devices}, "Retrieved selected devices successfully")

    except Exception as e:
        logger.error(f"Failed to get selected devices: {e}")
        return error_response(f"Failed to get selected devices: {str(e)}", ErrorCodes.DEVICE_ERROR)


# Audio Capture Handlers
async def handle_start_capture(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Start audio capture."""
    try:
        audio_service = _get_audio_service(context)
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # System audio is now determined automatically from active mode's listenToSpeaker setting
        session_id = params.get("session_id")  # Optional recording session ID
        success = audio_service.start_capture(session_id=session_id)
        
        if success:
            status = audio_service.get_status()
            return success_response(
                {
                    "capturing": True, 
                    "system_audio": status.get("system_audio_enabled", False),
                    "recording": session_id is not None
                },
                "Audio capture started successfully"
            )
        else:
            return error_response("Failed to start audio capture", ErrorCodes.CAPTURE_ERROR)
            
    except Exception as e:
        logger.error(f"Failed to start audio capture: {e}")
        return error_response(f"Failed to start audio capture: {str(e)}", ErrorCodes.CAPTURE_ERROR)


async def handle_stop_capture(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Stop audio capture."""
    try:
        audio_service = _get_audio_service(context)
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = audio_service.stop_capture()
        
        if success:
            return success_response(
                {"capturing": False},
                "Audio capture stopped successfully"
            )
        else:
            return error_response("Failed to stop audio capture", ErrorCodes.CAPTURE_ERROR)
            
    except Exception as e:
        logger.error(f"Failed to stop audio capture: {e}")
        return error_response(f"Failed to stop audio capture: {str(e)}", ErrorCodes.CAPTURE_ERROR)


# Level Monitoring Handlers
async def handle_start_level_monitoring(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Start real-time level monitoring."""
    try:
        audio_service = _get_audio_service(context)
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Get IPC bridge for broadcasting levels
        ipc_bridge = None
        service_container = context.get('service_container')
        
        if service_container:
            ipc_bridge = service_container.resolve("ipc")
        
        # Create callback function to broadcast levels via IPC bridge
        def level_update_callback(levels: Dict[str, float]):
            """Callback for broadcasting audio level updates."""
            try:
                message = {
                    "type": "event",
                    "event": "audio_levels",
                    "data": {
                        "levels": levels,
                        "timestamp": time.time()
                    }
                }
                
                # Use the IPC bridge's thread-safe send_message_sync method
                if ipc_bridge:
                    ipc_bridge.send_message_sync(message)
                    
            except Exception as e:
                logger.error(f"Level callback error: {e}")
        
        # Start level monitoring with the callback
        success = audio_service.start_level_monitoring(callback=level_update_callback)
        
        if success:
            return success_response(
                {"level_monitoring": True},
                "Level monitoring started successfully"
            )
        else:
            return error_response("Failed to start level monitoring", ErrorCodes.CAPTURE_ERROR)
            
    except Exception as e:
        logger.error(f"Failed to start level monitoring: {e}")
        return error_response(f"Failed to start level monitoring: {str(e)}", ErrorCodes.CAPTURE_ERROR)


async def handle_stop_level_monitoring(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Stop level monitoring."""
    try:
        audio_service = _get_audio_service(context)
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        success = audio_service.stop_level_monitoring()
        
        if success:
            return success_response(
                {"level_monitoring": False},
                "Level monitoring stopped successfully"
            )
        else:
            return error_response("Failed to stop level monitoring", ErrorCodes.CAPTURE_ERROR)
            
    except Exception as e:
        logger.error(f"Failed to stop level monitoring: {e}")
        return error_response(f"Failed to stop level monitoring: {str(e)}", ErrorCodes.CAPTURE_ERROR)


async def handle_get_audio_levels(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get current audio levels."""
    try:
        audio_service = _get_audio_service(context)
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        levels = audio_service.get_audio_levels()
        return success_response({"levels": levels}, "Audio levels retrieved successfully")
        
    except Exception as e:
        logger.error(f"Failed to get audio levels: {e}")
        return error_response(f"Failed to get audio levels: {str(e)}", ErrorCodes.CAPTURE_ERROR)


# Status Handlers
async def handle_get_status(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get audio service status."""
    try:
        audio_service = _get_audio_service(context)
        if not audio_service:
            return error_response("Audio service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        status = audio_service.get_status()
        return success_response(status, "Audio status retrieved successfully")
        
    except Exception as e:
        logger.error(f"Failed to get audio status: {e}")
        return error_response(f"Failed to get audio status: {str(e)}", ErrorCodes.SERVICE_UNAVAILABLE)


# Configuration handlers removed - all config updates are handled via database push architecture
# Configuration flows: UI → Database → DatabaseManager → ConfigurationManager → Services


# Handler Registration
def register_handlers(command_handlers) -> None:
    """Register all audio-related command handlers."""
    logger.info("Registering audio handlers...")
    
    # Device Management Handlers
    command_handlers.register_function("audio.get_devices", handle_get_devices)
    command_handlers.register_function("audio.refresh_devices", handle_refresh_devices)
    command_handlers.register_function("audio.test_device", handle_test_device)
    # Note: audio.set_input_device and audio.set_output_device removed - handled via database push
    command_handlers.register_function("audio.get_selected_devices", handle_get_selected_devices)
    
    # Audio capture
    command_handlers.register_function("audio.start_capture", handle_start_capture)
    command_handlers.register_function("audio.stop_capture", handle_stop_capture)
    
    # Level monitoring
    command_handlers.register_function("audio.start_level_monitoring", handle_start_level_monitoring)
    command_handlers.register_function("audio.stop_level_monitoring", handle_stop_level_monitoring)
    command_handlers.register_function("audio.get_audio_levels", handle_get_audio_levels)
    
    # Status only (configuration handled via database push)
    command_handlers.register_function("audio.get_status", handle_get_status)
    
    logger.info("Audio handlers registered successfully")