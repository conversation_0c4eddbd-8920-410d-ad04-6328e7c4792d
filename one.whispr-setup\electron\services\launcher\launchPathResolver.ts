import { app } from 'electron';
import * as path from 'path';
import { MainAppDownloader } from '../download/mainAppDownloader';
import { MicrosoftStoreHandler } from '../microsoft/storeHandler';
import { IS_MICROSOFT } from '../../constants';

/**
 * Service for resolving the correct main app launch path based on environment
 */
export class LaunchPathResolver {
  private mainAppDownloader: MainAppDownloader;
  private microsoftStoreHandler: MicrosoftStoreHandler;

  constructor(
    mainAppDownloader: MainAppDownloader,
    microsoftStoreHandler: MicrosoftStoreHandler
  ) {
    this.mainAppDownloader = mainAppDownloader;
    this.microsoftStoreHandler = microsoftStoreHandler;
  }

  /**
   * Get the correct main app path based on environment and build type
   */
  public getMainAppPath(): string {
    const forceDownload = process.env.FORCE_DOWNLOAD === 'true';

    if (IS_MICROSOFT) {
      // Microsoft Store build - use AppData path (main app will be extracted there)
      const path = this.microsoftStoreHandler.getMainAppPath();
      console.log('[PATH_RESOLVER] Microsoft Store build - using AppData path:', path);
      return path;
    } else if (!app.isPackaged && !forceDownload) {
      // In development mode without forced download, point directly to the built exe in win-unpacked
      const path = this.getDevPath();
      console.log('[PATH_RESOLVER] Using development path:', path);
      return path;
    } else {
      // In production or forced download mode, use the downloaded files
      const path = this.getProductionPath();
      console.log('[PATH_RESOLVER] Using downloaded files path:', path);
      return path;
    }
  }

  /**
   * Get development mode path
   */
  private getDevPath(): string {
    return path.join(
      process.cwd(), 
      '..', 
      'one.whispr-app', 
      '.release', 
      'win-unpacked', 
      'One Whispr.exe'
    );
  }

  /**
   * Get production mode path
   */
  private getProductionPath(): string {
    return path.join(this.mainAppDownloader.getDownloadPath(), 'One Whispr.exe');
  }

  /**
   * Check if the current environment is development mode
   */
  public isDevelopmentMode(): boolean {
    const forceDownload = process.env.FORCE_DOWNLOAD === 'true';
    return !app.isPackaged && !forceDownload;
  }

  /**
   * Check if force download is enabled
   */
  public isForceDownloadEnabled(): boolean {
    return process.env.FORCE_DOWNLOAD === 'true';
  }
}
