import { getDatabase } from '../core/connection';
import { generateId } from '../core/id';
import { transaction } from '../core/connection';
import Database from 'better-sqlite3';
import { EventEmitter } from 'events';

// Database change events
export enum DatabaseChangeType {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE'
}

// Database change event interface
export interface DatabaseChangeEvent {
  type: DatabaseChangeType;
  table: string;
  id: string;
  data?: any;
}

// Global event emitter for database changes
export const dbEvents = new EventEmitter();

/**
 * Base entity interface
 * All database entities should have an id
 */
export interface BaseEntity {
  id: string;
}

/**
 * Base repository class that provides common CRUD operations
 * for entities stored in JSON format
 */
export abstract class BaseRepository<T extends BaseEntity> {
  protected db: Database.Database;
  protected tableName: string;
  
  constructor(tableName: string) {
    this.tableName = tableName;
    this.db = getDatabase();
    this.ensureTable();
  }
  
  /**
   * Ensure the table exists
   */
  protected abstract ensureTable(): void;
  
  /**
   * Convert entity to database format
   */
  protected serialize(entity: T): any {
    const { id, ...data } = entity;
    return {
      id,
      data: JSON.stringify(data)
    };
  }
  
  /**
   * Convert database row to entity
   */
  protected deserialize(row: any): T {
    try {
      if (!row) return null as any;
      
      const data = JSON.parse(row.data);
      return {
        id: row.id,
        ...data
      } as T;
    } catch (error) {
      console.error(`Error deserializing entity from ${this.tableName}:`, error);
      return null as any;
    }
  }
  
  /**
   * Find by ID
   */
  findById(id: string): T | null {
    try {
      // Get database and check that it's open
      const db = getDatabase();
      if (!db || !db.open) {
        console.error(`Database not open while finding ${this.tableName} by ID.`);
        return null;
      }
      
      const query = `SELECT * FROM ${this.tableName} WHERE id = ?`;
      const result = db.prepare(query).get(id);
      return result ? this.deserialize(result) : null;
    } catch (error) {
      console.error(`Error finding ${this.tableName} by ID:`, error);
      return null;
    }
  }
  
  /**
   * Find all entities
   */
  findAll(): T[] {
    try {
      const query = `SELECT * FROM ${this.tableName}`;
      const results = this.db.prepare(query).all();
      return results.map(row => this.deserialize(row));
    } catch (error) {
      console.error(`Error finding all ${this.tableName}:`, error);
      return [];
    }
  }
  
  /**
   * Create a new entity
   */
  create(data: Omit<T, 'id'>): T {
    try {
      const id = generateId();
      const entity = { id, ...data } as T;
      this.save(entity);
      return entity;
    } catch (error) {
      console.error(`Error creating ${this.tableName}:`, error);
      throw error;
    }
  }
  
  /**
   * Save an entity (insert or update)
   */
  save(entity: T): boolean {
    try {
      // Get database and check that it's open
      const db = getDatabase();
      if (!db || !db.open) {
        console.error(`Database not open while saving ${this.tableName}.`);
        return false;
      }
      
      const { id, data } = this.serialize(entity);
      
      // Use upsert (INSERT OR REPLACE)
      const query = `
        INSERT INTO ${this.tableName} (id, data) 
        VALUES (?, ?) 
        ON CONFLICT(id) DO UPDATE SET data = excluded.data
      `;
      
      const result = db.prepare(query).run(id, data);
      
      // Emit change event
      const eventType = result.changes > 1 ? DatabaseChangeType.UPDATE : DatabaseChangeType.INSERT;
      this.emitChange(eventType, entity);
      
      return true;
    } catch (error) {
      console.error(`Error saving ${this.tableName}:`, error);
      return false;
    }
  }
  
  /**
   * Update an entity
   */
  update(id: string, data: Partial<Omit<T, 'id'>>): T | null {
    try {
      // Get existing entity
      const existing = this.findById(id);
      if (!existing) return null;
      
      // Merge with new data
      const updated = { ...existing, ...data } as T;
      
      // Save the updated entity
      const success = this.save(updated);
      return success ? updated : null;
    } catch (error) {
      console.error(`Error updating ${this.tableName}:`, error);
      return null;
    }
  }
  
  /**
   * Delete an entity
   */
  delete(id: string): boolean {
    try {
      // Get the entity before deleting for event
      const entity = this.findById(id);
      if (!entity) return false;
      
      // Delete the entity
      const query = `DELETE FROM ${this.tableName} WHERE id = ?`;
      const result = this.db.prepare(query).run(id);
      
      // Emit delete event if something was deleted
      if (result.changes > 0) {
        this.emitChange(DatabaseChangeType.DELETE, entity);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`Error deleting ${this.tableName}:`, error);
      return false;
    }
  }
  
  /**
   * Save multiple entities in a transaction
   */
  saveAll(entities: T[]): boolean {
    try {
      return transaction(() => {
        entities.forEach(entity => this.save(entity));
        return true;
      });
    } catch (error) {
      return false;
    }
  }
  
  /**
   * Emit a change event
   */
  protected emitChange(type: DatabaseChangeType, entity: T): void {
    // Emit change event with proper structure
    dbEvents.emit('change', {
      type,
      table: this.tableName,
      tableName: this.tableName, // Add both for compatibility
      id: entity.id,
      data: entity,
      entity: entity // Add entity field for sync processing
    });
    
    // Also emit a table-specific event
    dbEvents.emit(`${this.tableName}:change`, {
      type,
      table: this.tableName,
      tableName: this.tableName,
      id: entity.id,
      data: entity,
      entity: entity
    });
  }
}
