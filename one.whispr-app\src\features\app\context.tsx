import { createContext, useState, useEffect, useContext, ReactNode, useCallback } from 'react';
import { 
  AuthUrls, 
  AuthData, 
  SetupStep, 
  VoiceModel,
  VoiceModelId,
  RecordingMode,
  Shortcuts,
  AudioContextProps,
  AuthContextProps,
  ModelContextProps,
  ShortcutsContextProps,
  SetupContextProps
} from './types';

// Updated voice models based on the new types
const VOICE_MODELS: VoiceModel[] = [
  {
    id: 'openai/whisper-tiny',
    name: 'Whisper Tiny',
    size: '75 MB',
    languages: 'Multilingual',
    isEnglishOnly: false,
    isDownloaded: false,
    type: 'Standard',
    speed: 'Fast'
  },
  {
    id: 'openai/whisper-base',
    name: 'Whisper Base',
    size: '142 MB',
    languages: 'Multilingual',
    isEnglishOnly: false,
    isDownloaded: false,
    type: 'Standard',
    speed: 'Fast'
  },
  {
    id: 'openai/whisper-base.en',
    name: 'Whisper Base',
    size: '142 MB',
    languages: 'English only',
    isEnglishOnly: true,
    isDownloaded: false,
    type: 'Standard',
    speed: 'Fast'
  },
  {
    id: 'openai/whisper-small',
    name: 'Whis<PERSON> <PERSON>',
    size: '466 MB',
    languages: 'Multilingual',
    isEnglishOnly: false,
    isDownloaded: false,
    type: 'Standard',
    speed: 'Medium'
  },
  {
    id: 'distil-whisper/distil-small.en',
    name: 'Distil Whisper Small',
    size: '370 MB',
    languages: 'English only',
    isEnglishOnly: true,
    isDownloaded: false,
    type: 'Distilled',
    speed: 'Fast'
  },
  {
    id: 'distil-whisper/distil-medium.en',
    name: 'Distil Whisper Medium',
    size: '770 MB',
    languages: 'English only',
    isEnglishOnly: true,
    isDownloaded: false,
    type: 'Distilled',
    speed: 'Medium'
  },
  {
    id: 'distil-whisper/distil-large-v3',
    name: 'Distil Whisper Large',
    size: '1.5 GB',
    languages: 'Multilingual',
    isEnglishOnly: false,
    isDownloaded: false,
    type: 'Distilled',
    speed: 'Medium'
  }
];

// Default shortcuts
const DEFAULT_SHORTCUTS: Shortcuts = {
  pushToTalk: ['Control', 'Alt'],
  toggle: ['Control', 'Alt'],
  cancel: ['Escape'],
  modeSwitch: ['Alt', 'Space'],
};

// Create individual contexts
const SetupContext = createContext<SetupContextProps | undefined>(undefined);
const AuthContext = createContext<AuthContextProps | undefined>(undefined);
const ModelContext = createContext<ModelContextProps | undefined>(undefined);
const ShortcutsContext = createContext<ShortcutsContextProps | undefined>(undefined);
const AudioContext = createContext<AudioContextProps | undefined>(undefined);

// Setup Provider
export function SetupProvider({ children }: { children: ReactNode }) {
  const [currentStep, setCurrentStep] = useState<SetupStep>('welcome');

  // Step navigation functions
  const goToNextStep = () => {
    const steps: SetupStep[] = ['welcome', 'download-model', 'audio-setup', 'shortcuts', 'complete'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  };
  
  const goToPreviousStep = () => {
    const steps: SetupStep[] = ['welcome', 'download-model', 'audio-setup', 'shortcuts', 'complete'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  };
  
  const goToStep = (step: SetupStep) => {
    setCurrentStep(step);
  };

  const value = {
    currentStep,
    goToNextStep,
    goToPreviousStep,
    goToStep
  };

  return <SetupContext.Provider value={value}>{children}</SetupContext.Provider>;
}

// Auth Provider
export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authUrls, setAuthUrls] = useState<AuthUrls | null>(null);
  const [loading, setLoading] = useState(false);

  // Fetch auth URLs on component mount
  useEffect(() => {
    async function fetchAuthUrls() {
      try {
        const urls = await window.electron.getAuthUrls();
        setAuthUrls(urls);
      } catch (error) {
        console.error('Failed to fetch auth URLs', error);
      }
    }
    
    fetchAuthUrls();
  }, []);
  
  // Setup auth callback listener
  useEffect(() => {
    const removeListener = window.electron.onAuthCallback((data: AuthData) => {
      if (data.token) {
        // In a real app, we would store the token here
        // For now, just set authenticated to true
        setIsAuthenticated(true);
      }
    });
    
    return () => removeListener();
  }, []);

  // Auth functions
  const login = async () => {
    if (!authUrls) return;
    
    setLoading(true);
    try {
      await window.electron.openExternalUrl(authUrls.login);
    } catch (error) {
      console.error('Failed to open login page', error);
    } finally {
      setLoading(false);
    }
  };
  
  const register = async () => {
    if (!authUrls) return;
    
    setLoading(true);
    try {
      await window.electron.openExternalUrl(authUrls.register);
    } catch (error) {
      console.error('Failed to open registration page', error);
    } finally {
      setLoading(false);
    }
  };
  
  const logout = () => {
    setIsAuthenticated(false);
  };
  
  // For development purposes - allow bypassing login
  const bypassLogin = () => {
    setIsAuthenticated(true);
  };

  const value = {
    isAuthenticated,
    loading,
    login,
    register,
    logout,
    bypassLogin
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

interface ModelStatusEvent {
  model: VoiceModelId;
}

interface ModelProgressEvent extends ModelStatusEvent {
  progress: number;
}

interface ModelErrorEvent extends ModelStatusEvent {
  error: string;
}

// Model Provider
export function ModelProvider({ children }: { children: ReactNode }) {
  const [availableModels, setAvailableModels] = useState<VoiceModel[]>(VOICE_MODELS);
  const [selectedModel, setSelectedModel] = useState<VoiceModel | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [isCheckingStatus, setIsCheckingStatus] = useState(true);
  const [currentDownloadingModel, setCurrentDownloadingModel] = useState<string | null>(null);
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [loadedModel, setLoadedModel] = useState<string | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  const [lastChecked, setLastChecked] = useState<number>(0); // Timestamp of last check
  const [hasInitialCheck, setHasInitialCheck] = useState<boolean>(false); // Track if initial check has happened
  
  // Function to refresh model status from the Python backend
  const refreshModelStatus = useCallback(async (force = false) => {
    // Avoid excessive refreshes - only refresh if it's been more than 2 seconds or if forced
    const now = Date.now();
    if (!force && now - lastChecked < 2000) {
      console.log("Skipping model status refresh - too soon since last check");
      return;
    }
    
    try {
      console.log("Refreshing model status...");
      setLastChecked(now);
      
      if (force) {
        setIsCheckingStatus(true);
      }
      
      // First, get the current model status to check loaded model information
      const statusResponse = await window.electron.sendCommand('python:getModelStatus');
      
      if (statusResponse.status === 'success') {
        const { 
          isLoading, 
          currentModel, 
          downloadProgress: currentProgress,
          modelLoaded,
          loadedModel: currentLoadedModel
        } = statusResponse.data;
        
        // Update loaded model information right away if the backend reports it
        if (currentLoadedModel) {
          console.log(`Backend reports loaded model: ${currentLoadedModel}`);
          setLoadedModel(currentLoadedModel);
          
          // Also immediately update our models list to mark this model as downloaded
          setAvailableModels(prevModels => 
            prevModels.map((model: VoiceModel) => 
              model.id === currentLoadedModel 
                ? { ...model, isDownloaded: true } 
                : model
            )
          );
        } else if (modelLoaded) {
          // If backend says a model is loaded but doesn't specify which one,
          // we should try to determine it from the models list
          console.log('Backend reports a model is loaded but doesn\'t specify which one');
        }
        
        // If a model is currently loading/downloading, update UI state
        if (isLoading) {
          setIsDownloading(true);
          setDownloadProgress(currentProgress || 0);
          setCurrentDownloadingModel(currentModel || null);
        } else {
          setIsDownloading(false);
          setCurrentDownloadingModel(null);
        }
      }
      
      // Then, get available models with their downloaded status
              const modelsResponse = await window.electron.sendCommand('models.get_all');
      
      if (modelsResponse.status === 'success' && Array.isArray(modelsResponse.data)) {
        // Get current loaded model information
        const backendLoadedModel = statusResponse.status === 'success' ? statusResponse.data.loadedModel : null;
        
        // Create a modified model list with updated download status
        const updatedModels = modelsResponse.data.map((model: VoiceModel) => {
          // Consider a model downloaded if either:
          // 1. The backend reports it as downloaded
          // 2. It matches the loaded model ID from getModelStatus
          const isModelLoaded = backendLoadedModel === model.id;
          
          return {
            ...model,
            // If backend says model is loaded, make sure isDownloaded is set to true
            isDownloaded: model.isDownloaded || isModelLoaded
          };
        });
        
        // Update the models with downloaded status
        setAvailableModels(updatedModels);
        
        // If no model is selected yet, select the loaded or downloaded model
        if (!selectedModel) {
          // First try to select based on loaded model from backend
          if (backendLoadedModel) {
            const loadedModel = updatedModels.find((m: VoiceModel) => m.id === backendLoadedModel);
            if (loadedModel) {
              setSelectedModel(loadedModel);
              console.log(`Selected model based on backend loaded model: ${loadedModel.id}`);
            }
          } else {
            // Fall back to any downloaded model
            const downloadedModel = updatedModels.find((m: VoiceModel) => m.isDownloaded);
            if (downloadedModel) {
              setSelectedModel(downloadedModel);
              console.log(`Selected model based on download status: ${downloadedModel.id}`);
            }
          }
        } else {
          // Update selected model status if it exists in the new list
          const updatedModel = updatedModels.find((m: VoiceModel) => m.id === selectedModel.id);
          if (updatedModel) {
            setSelectedModel(updatedModel);
            console.log(`Updated selected model status: ${updatedModel.id}, isDownloaded: ${updatedModel.isDownloaded}`);
          }
        }
        
        // Force update loadedModel if any models are downloaded and no loadedModel is set
        const anyDownloadedModels = updatedModels.some((m: VoiceModel) => m.isDownloaded);
        if (anyDownloadedModels && !loadedModel && !backendLoadedModel) {
          console.log('Found downloaded models, updating loaded model status');
          const firstDownloaded = updatedModels.find((m: VoiceModel) => m.isDownloaded);
          if (firstDownloaded) {
            setLoadedModel(firstDownloaded.id);
            console.log(`Set loadedModel to first downloaded model: ${firstDownloaded.id}`);
          }
        }
      }
      
      // Mark that initial check has happened
      if (!hasInitialCheck) {
        setHasInitialCheck(true);
      }
    } catch (error) {
      console.error('Failed to refresh model status:', error);
      if (force) {
        setLoadingError(`Failed to get model status: ${error instanceof Error ? error.message : String(error)}`);
      }
    } finally {
      if (force) {
        setIsCheckingStatus(false);
      }
    }
  }, [selectedModel, loadedModel, lastChecked, hasInitialCheck]);
  
  // Force a deep refresh of model status
  const forceModelStatusCheck = useCallback(async () => {
    console.log('Explicitly forcing complete model status check');
    try {
      setIsCheckingStatus(true);
      
      // First get the current model status to see if we have a model loaded
      const statusResponse = await window.electron.sendCommand('models.get_active');
      let backendLoadedModel = null;
      
      if (statusResponse.status === 'success') {
        backendLoadedModel = statusResponse.data.loadedModel;
        if (backendLoadedModel) {
          console.log(`Backend reports model already loaded: ${backendLoadedModel}`);
          setLoadedModel(backendLoadedModel);
        }
      }
      
      // Tell backend to scan model directory and update cache
      const scanResponse = await window.electron.sendCommand('models.get_all');
      
      if (scanResponse.status === 'success') {
        console.log('Model directory scan completed');
        
        // If we got models back directly from the scan, update them
        if (Array.isArray(scanResponse.data)) {
          const models = scanResponse.data;
          console.log('Scan returned models directly:', models);
          
          // If we have a loaded model from the backend, make sure it's marked as downloaded
          if (backendLoadedModel) {
            models.forEach((model: VoiceModel) => {
              if (model.id === backendLoadedModel) {
                model.isDownloaded = true;
              }
            });
          }
          
          // Update models with correct download status
          setAvailableModels(models);
          
          // If we found any downloaded models, select one
          const downloadedModel = models.find((m: VoiceModel) => m.isDownloaded);
          if (downloadedModel) {
            console.log(`Found downloaded model after scan: ${downloadedModel.id}`);
            setSelectedModel(downloadedModel);
            setLoadedModel(downloadedModel.id);
          }
        }
      }
      
      // Refresh model list after scan with force=true
      await refreshModelStatus(true);
      
      // Mark that initial check has happened
      if (!hasInitialCheck) {
        setHasInitialCheck(true);
      }
    } catch (error) {
      console.error('Failed to force model status check:', error);
      setLoadingError(`Failed to check model directory: ${error instanceof Error ? error.message : String(error)}`);
    } finally {
      setIsCheckingStatus(false);
    }
  }, [refreshModelStatus, hasInitialCheck]);
  
  // Check models status on mount - but only once
  useEffect(() => {
    if (!hasInitialCheck) {
      console.log("Initial model status check on mount");
      
      // Explicit immediate check for model status from the backend
      (async () => {
        // First directly check model status
        try {
          console.log("Directly checking model status on mount");
          const statusResponse = await window.electron.sendCommand('models.get_active');
          
          if (statusResponse.status === 'success') {
            const { loadedModel: currentLoadedModel } = statusResponse.data;
            
            if (currentLoadedModel) {
              console.log(`Direct check found loaded model: ${currentLoadedModel}`);
              setLoadedModel(currentLoadedModel);
              
              // Also immediately mark this model as downloaded in our models list
              setAvailableModels(prevModels => 
                prevModels.map((model: VoiceModel) => 
                  model.id === currentLoadedModel 
                    ? { ...model, isDownloaded: true } 
                    : model
                )
              );
            }
          }
        } catch (e) {
          console.error("Error checking model status directly:", e);
        }
        
        // Then do the full check
        forceModelStatusCheck();
      })();
      
      setHasInitialCheck(true);
    }
    
    // No periodic refresh interval - only check when explicitly needed
    
    return () => {
      // No interval to clear
    };
  }, [forceModelStatusCheck, hasInitialCheck]);

  // Set up event listeners for model status updates
  useEffect(() => {
    console.log('Setting up model status event listeners');
    
    const removeModelLoadingListener = window.electron.onBackendStatus('python:modelLoading', (data: ModelStatusEvent) => {
      console.log('Model loading from cache:', data);
      setIsDownloading(true);
      setDownloadProgress(10); // Start at 10% to indicate loading
      setCurrentDownloadingModel(data.model);
      // Reset any previous errors
      setLoadingError(null);
      setIsRetrying(false);
    });
    
    const removeModelDownloadingListener = window.electron.onBackendStatus('python:modelDownloading', (data: ModelStatusEvent) => {
      console.log('Model downloading from server:', data);
      setIsDownloading(true);
      setDownloadProgress(5); // Start at 5% for downloading
      setCurrentDownloadingModel(data.model);
      // Reset any previous errors
      setLoadingError(null);
      setIsRetrying(false);
    });
    
    const removeModelProgressListener = window.electron.onBackendStatus('python:modelDownloadProgress', (data: ModelProgressEvent) => {
      console.log('Model download progress:', data);
      setDownloadProgress(data.progress);
    });
    
    const removeModelLoadedListener = window.electron.onBackendStatus('python:modelLoaded', (data: any) => {
      console.log('Model loaded successfully:', data);
      setIsDownloading(false);
      setDownloadProgress(0);
      setCurrentDownloadingModel(null);
      setIsRetrying(false);
      
      // Set the loaded model directly
      if (data.model) {
        setLoadedModel(data.model);
        
        // Also update the availableModels to mark this model as downloaded
        setAvailableModels(prev => prev.map((m: VoiceModel) => 
          m.id === data.model ? {...m, isDownloaded: true} : m
        ));
      }
      
      // Update models list to reflect the newly downloaded model
      refreshModelStatus(true); // Force refresh after model loaded
    });
    
    const removeModelErrorListener = window.electron.onBackendStatus('python:modelLoadError', (data: ModelErrorEvent) => {
      console.log('Model load error:', data);
      setIsDownloading(false);
      setLoadingError(data.error || 'Unknown error occurred');
      setCurrentDownloadingModel(null);
    });
    
    // Global message listener for debugging
    const removeAllMessagesListener = window.electron.onBackendMessage((data: any) => {
      console.log('Received Python status message:', data);
    });
    
    // Return cleanup function
    return () => {
      console.log('Cleaning up model status event listeners');
      removeModelLoadingListener();
      removeModelDownloadingListener();
      removeModelProgressListener();
      removeModelLoadedListener();
      removeModelErrorListener();
      removeAllMessagesListener();
    };
  }, [refreshModelStatus]); // Only when refreshModelStatus changes
  
  // Log when listeners are set up
  useEffect(() => {
    console.log('Model status event listeners set up');
  }, []);

  // Helper methods to check model status
  const isModelDownloading = useCallback((modelId: string) => {
    return isDownloading && currentDownloadingModel === modelId;
  }, [isDownloading, currentDownloadingModel]);
  
  const getModelDownloadStatus = useCallback((modelId: string) => {
    if (isModelDownloading(modelId)) {
      return `Downloading (${downloadProgress}%)`;
    }
    
    const model = availableModels.find(m => m.id === modelId);
    if (!model) return 'Unknown';
    
    return model.isDownloaded ? 'Downloaded' : 'Not Downloaded';
  }, [availableModels, isModelDownloading, downloadProgress]);

  // Model functions
  const selectModel = (modelId: string) => {
    const model = availableModels.find(m => m.id === modelId) || null;
    setSelectedModel(model);
    
    // Clear any previous errors when selecting a new model
    setLoadingError(null);
  };
  
  const downloadModel = async () => {
    if (!selectedModel) return;
    
    try {
      // Check if the model is already downloaded
      if (selectedModel.isDownloaded || loadedModel === selectedModel.id) {
        console.log(`Model ${selectedModel.id} is already downloaded, loading it directly`);
        
        // Just load the model instead of downloading
        await loadModel(selectedModel.id);
        return;
      }
      
      // Reset state at the beginning
      setIsDownloading(true);
      setDownloadProgress(0);
      setCurrentDownloadingModel(selectedModel.id);
      setLoadingError(null);
      
      // First check current model status
      const statusResponse = await window.electron.sendCommand('models.get_active');
      
      if (statusResponse.status === 'success') {
        const { isLoading, currentModel, modelLoaded, loadedModel: backendLoadedModel } = statusResponse.data;
        
        // Check if the model is already loaded in the backend
        if (backendLoadedModel === selectedModel.id || 
            (modelLoaded && backendLoadedModel && selectedModel.id === backendLoadedModel)) {
          console.log(`Model ${selectedModel.id} is already loaded in backend`);
          
          // Force refresh after we discover it's already loaded
          refreshModelStatus(true);
          
          // Notify that the model is loaded
          setIsDownloading(false);
          setLoadedModel(selectedModel.id);
          
          // Update the model status in our list
          setAvailableModels(prev => prev.map((m: VoiceModel) => 
            m.id === selectedModel.id ? {...m, isDownloaded: true} : m
          ));
          
          return;
        }
        
        // If already downloading, just update UI state
        if (isLoading && currentModel) {
          console.log(`Model already downloading: ${currentModel}`);
          setCurrentDownloadingModel(currentModel);
          setDownloadProgress(10); // Set to initial progress
          return;
        }
      }
      
      // Initiate download
      console.log('Initiating model download for:', selectedModel.id);
      const response = await window.electron.sendCommand('models.download', {
        model_id: selectedModel.id
      });
      
      // Handle successful initiation
      if (response.status === 'success' || 
          (response.result && response.result.status === 'success')) {
        console.log('Model download initiated successfully:', response);
        return;
      } else {
        // Handle error from direct response
        const errorMessage = response.error || 
                            (response.result && response.result.message) || 
                            'Failed to start model download';
        setLoadingError(errorMessage);
        setIsDownloading(false);
        console.error('Error starting model download:', errorMessage);
        return;
      }
    } catch (error) {
      // Handle exception
      setLoadingError(error instanceof Error ? error.message : 'Unknown error');
      setIsDownloading(false);
      console.error('Exception during model download:', error);
      return;
    }
  };

  // Load a model after it's downloaded
  const loadModel = async (modelId?: string) => {
    // Use provided modelId or the currently selected model
    const idToLoad = modelId || selectedModel?.id;
    
    if (!idToLoad) {
      console.error('No model selected for loading');
      return false;
    }
    
    try {
      console.log('Loading model:', idToLoad);
      
      // Show loading state
      setIsDownloading(true);
      setCurrentDownloadingModel(idToLoad);
      setDownloadProgress(10);
      setLoadingError(null);
      
      // Send command to load the model
      const response = await window.electron.sendCommand('models.download', {
        model_id: idToLoad
      });
      
      if (response.status === 'success' || 
          (response.result && response.result.status === 'success')) {
        console.log('Model load command sent successfully');
        return true;
      } else {
        const errorMessage = response.error || 
                           (response.result && response.result.message) || 
                           'Failed to load model';
        setLoadingError(errorMessage);
        setIsDownloading(false);
        console.error('Error loading model:', errorMessage);
        return false;
      }
    } catch (error) {
      setLoadingError(error instanceof Error ? error.message : 'Unknown error');
      setIsDownloading(false);
      console.error('Exception loading model:', error);
      return false;
    }
  };

  // Cancel current download
  const cancelDownload = async () => {
    if (!isDownloading || !currentDownloadingModel) return;
    
    try {
      const response = await window.electron.sendCommand('models.download.cancel');
      
      if (response.status === 'success') {
        setIsDownloading(false);
        setCurrentDownloadingModel(null);
        setDownloadProgress(0);
      }
    } catch (error) {
      console.error('Error canceling download:', error);
      // Force reset state even if command fails
      setIsDownloading(false);
      setCurrentDownloadingModel(null);
      setDownloadProgress(0);
    }
  };
  
  // Retry download after error
  const retryDownload = async () => {
    if (!selectedModel || !loadingError) return;
    
    setIsRetrying(true);
    setLoadingError(null);
    
    try {
      await downloadModel();
    } catch (error) {
      setLoadingError(error instanceof Error ? error.message : 'Failed to retry download');
      setIsRetrying(false);
    }
  };

  const value = {
    availableModels,
    selectedModel, 
    isDownloading,
    downloadProgress,
    isCheckingStatus,
    currentDownloadingModel,
    loadingError,
    loadedModel,
    isRetrying,
    selectModel,
    downloadModel,
    loadModel,
    refreshModelStatus,
    forceModelStatusCheck,
    isModelDownloading,
    getModelDownloadStatus,
    cancelDownload,
    retryDownload
  };

  return <ModelContext.Provider value={value}>{children}</ModelContext.Provider>;
}

// Shortcuts Provider
export function ShortcutsProvider({ children }: { children: ReactNode }) {
  const [recordingMode, setRecordingMode] = useState<RecordingMode>('toggle');
  const [shortcuts, setShortcuts] = useState<Shortcuts>(DEFAULT_SHORTCUTS);

  // Shortcut functions
  const updateRecordingMode = (mode: RecordingMode) => {
    setRecordingMode(mode);
  };
  
  const updateShortcut = (action: keyof Shortcuts, keys: string[]) => {
    setShortcuts(prev => ({
      ...prev,
      [action]: keys
    }));
  };

  const value = {
    recordingMode,
    shortcuts,
    updateRecordingMode,
    updateShortcut
  };

  return <ShortcutsContext.Provider value={value}>{children}</ShortcutsContext.Provider>;
}

interface AudioDeviceInfo {
  id: string;
  name: string;
  label: string;
  isDefault: boolean;
  type: 'input' | 'output';
}

interface AudioLevelEvent {
  level: number;
}

interface AudioTestCompleteEvent {
  duration: number;
}

// Audio Provider
export function AudioProvider({ children }: { children: ReactNode }) {
  const [audioInputDevice, setAudioInputDevice] = useState<string>('default');
  const [audioOutputDevice, setAudioOutputDevice] = useState<string>('default');
  const [availableInputDevices, setAvailableInputDevices] = useState<AudioDeviceInfo[]>([]);
  const [availableOutputDevices, setAvailableOutputDevices] = useState<AudioDeviceInfo[]>([]);
  const [audioLevel, setAudioLevel] = useState(0); // 0-100 range
  const [isListening, setIsListening] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [transcribedText, setTranscribedText] = useState('');
  
  // Load audio devices on mount
  useEffect(() => {
    async function fetchAudioDevices() {
      try {
        const response = await window.electron.sendCommand('python:getAudioDevices');
        
        if (response.status === 'success') {
          const { input, output, selectedInput, selectedOutput } = response.data;
          
          setAvailableInputDevices(input || []);
          setAvailableOutputDevices(output || []);
          
          // Set selected devices
          if (selectedInput) {
            setAudioInputDevice(selectedInput.id || 'default');
          }
          
          if (selectedOutput) {
            setAudioOutputDevice(selectedOutput.id || 'default');
          }
        }
      } catch (error) {
        console.error('Failed to fetch audio devices:', error);
      }
    }
    
    fetchAudioDevices();
    
    // Set up event listeners
    const removeAudioLevelListener = window.electron.onBackendStatus('python:audioLevel', (data: AudioLevelEvent) => {
      setAudioLevel(data.level);
    });
    
    const removeAudioTestCompleteListener = window.electron.onBackendStatus('python:audioTestComplete', (data: AudioTestCompleteEvent) => {
      setIsRecording(false);
      // You could play back the audio or display a message here
      setTranscribedText(`Recorded ${data.duration.toFixed(1)} seconds of audio.`);
    });
    
    // Cleanup listeners
    return () => {
      removeAudioLevelListener();
      removeAudioTestCompleteListener();
      
      // Also stop any active monitoring
      stopListening();
    };
  }, []);

  const setInputDevice = useCallback(async (deviceId: string) => {
    try {
      const response = await window.electron.sendCommand('python:setAudioDevice', {
        device_id: deviceId,
        device_type: 'input',
        use_default: deviceId === 'default'
      });
      
      if (response.status === 'success') {
        setAudioInputDevice(deviceId);
      }
    } catch (error) {
      console.error('Failed to set audio input device:', error);
    }
  }, []);
  
  const setOutputDevice = useCallback(async (deviceId: string) => {
    try {
      const response = await window.electron.sendCommand('python:setAudioDevice', {
        device_id: deviceId,
        device_type: 'output',
        use_default: deviceId === 'default'
      });
      
      if (response.status === 'success') {
        setAudioOutputDevice(deviceId);
      }
    } catch (error) {
      console.error('Failed to set audio output device:', error);
    }
  }, []);
  
  const startListening = useCallback(async () => {
    if (isListening) return;
    
    try {
      const response = await window.electron.sendCommand('python:testAudioLevel', {
        start: true
      });
      
      if (response.status === 'success') {
        setIsListening(true);
        setTranscribedText('');
      }
    } catch (error) {
      console.error('Failed to start audio level monitoring:', error);
    }
  }, [isListening]);
  
  const stopListening = useCallback(async () => {
    if (!isListening) return;
    
    try {
      const response = await window.electron.sendCommand('python:testAudioLevel', {
        start: false
      });
      
      if (response.status === 'success') {
        setIsListening(false);
      }
    } catch (error) {
      console.error('Failed to stop audio level monitoring:', error);
      // Reset state even if command fails
      setIsListening(false);
    }
  }, [isListening]);
  
  const recordAudioTest = useCallback(async () => {
    if (isRecording) return;
    
    try {
      const response = await window.electron.sendCommand('python:recordAudioTest', {
        max_duration: 5.0 // Maximum 5 seconds
      });
      
      if (response.status === 'success') {
        setIsRecording(true);
        setTranscribedText('Recording audio test...');
      }
    } catch (error) {
      console.error('Failed to start audio test recording:', error);
    }
  }, [isRecording]);
  
  const stopAudioTest = useCallback(async () => {
    if (!isRecording) return;
    
    try {
      const response = await window.electron.sendCommand('python:stopAudioTest', {});
      
      if (response.status === 'success') {
        setIsRecording(false);
        setTranscribedText('Processing audio test...');
      }
    } catch (error) {
      console.error('Failed to stop audio test recording:', error);
      // Reset state even if command fails
      setIsRecording(false);
    }
  }, [isRecording]);

  const refreshDevices = useCallback(async () => {
    try {
      const response = await window.electron.sendCommand('python:getAudioDevices');
      
      if (response.status === 'success') {
        const { input, output } = response.data;
        setAvailableInputDevices(input || []);
        setAvailableOutputDevices(output || []);
      }
    } catch (error) {
      console.error('Failed to refresh audio devices:', error);
    }
  }, []);

  const value = {
    // Device information
    audioInputDevice,
    audioOutputDevice,
    availableInputDevices,
    availableOutputDevices,
    
    // Audio levels
    audioLevel,
    isListening,
    isRecording,
    transcribedText,
    
    // Functions
    setInputDevice,
    setOutputDevice,
    startListening,
    stopListening,
    recordAudioTest,
    stopAudioTest,
    refreshDevices
  };

  return <AudioContext.Provider value={value}>{children}</AudioContext.Provider>;
}

// Main App Provider that combines all contexts
export function AppProvider({ children }: { children: ReactNode }) {
  return (
    <AuthProvider>
      <SetupProvider>
        <ModelProvider>
          <ShortcutsProvider>
            <AudioProvider>
              {children}
            </AudioProvider>
          </ShortcutsProvider>
        </ModelProvider>
      </SetupProvider>
    </AuthProvider>
  );
}

// Custom hooks for accessing contexts
export const useSetup = () => {
  const context = useContext(SetupContext);
  if (context === undefined) {
    throw new Error('useSetup must be used within a SetupProvider');
  }
  return context;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const useModel = () => {
  const context = useContext(ModelContext);
  if (context === undefined) {
    throw new Error('useModel must be used within a ModelProvider');
  }
  return context;
};

export const useShortcuts = () => {
  const context = useContext(ShortcutsContext);
  if (context === undefined) {
    throw new Error('useShortcuts must be used within a ShortcutsProvider');
  }
  return context;
};

export const useAudio = () => {
  const context = useContext(AudioContext);
  if (context === undefined) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};
