"""
Transcription command handlers for One.Whispr.

This module implements the command handlers for transcription operations.
"""

import logging
from typing import Dict, Any, TYPE_CHECKING
import time

from whispr.core.response_utils import success_response, error_response, ErrorCodes

if TYPE_CHECKING:
    from whispr.core.handlers import CommandHandlers

# Configure logging
logger = logging.getLogger('whispr.handlers.transcription')


def _get_transcription_service(context: Dict[str, Any]):
    """Get the transcription service from the context.
    
    Args:
        context: Execution context
        
    Returns:
        TranscriptionService instance or None if not available
    """
    service_container = context.get('service_container')
    if not service_container:
        return None
    return service_container.resolve("transcription")


async def handle_start_transcription(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.start command.
    
    Args:
        params: Command parameters containing optional session_id and mode_id
        context: Execution context
        
    Returns:
        Response dictionary with success status and session information
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Extract parameters
        session_id = params.get("session_id")
        mode_id = params.get("mode_id")
        
        # Start transcription
        result = await transcription_service.start_transcription(
            session_id=session_id,
            mode_id=mode_id
        )
        
        # Return the result directly as the service already uses the standard format
        return result
        
    except Exception as e:
        logger.error(f"Error in handle_start_transcription: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_stop_transcription(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.stop command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with success status and session summary
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        result = await transcription_service.stop_transcription()
        # Return the result directly as the service already uses the standard format
        return result
        
    except Exception as e:
        logger.error(f"Error in handle_stop_transcription: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_transcription_status(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.get_status command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with current transcription status
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        result = await transcription_service.get_transcription_status()
        # Return the result directly as the service already uses the standard format
        return result
        
    except Exception as e:
        logger.error(f"Error in handle_get_transcription_status: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_test_transcription(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.test command.
    
    Args:
        params: Command parameters containing optional duration
        context: Execution context
        
    Returns:
        Response dictionary with test results
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Extract duration parameter
        duration = params.get("duration", 5.0)
        
        result = await transcription_service.test_transcription(duration_seconds=duration)
        # Return the result directly as the service already uses the standard format
        return result
        
    except Exception as e:
        logger.error(f"Error in handle_test_transcription: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_test_start_transcription(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.test_start command for setup flow shortcut testing.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with test start results
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Check if already in test mode
        if hasattr(transcription_service, '_test_mode_active') and transcription_service._test_mode_active:
            return error_response("Transcription test already in progress", ErrorCodes.SERVICE_ERROR)
        
        # Start test transcription session
        session_id = f"test_setup_{int(time.time())}"
        result = await transcription_service.start_transcription(session_id=session_id)
        
        if result.get("success"):
            # Mark as test mode
            transcription_service._test_mode_active = True
            transcription_service._test_session_id = session_id
            
            # Broadcast test start event for UI feedback
            service_container = context.get('service_container')
            if service_container:
                ipc_bridge = service_container.resolve("ipc")
                if ipc_bridge:
                    await ipc_bridge.send_message({
                        "type": "event",
                        "event": "transcription_test_started",
                        "data": {
                            "session_id": session_id,
                            "timestamp": time.time()
                        }
                    })
            
            logger.info(f"Transcription test started with session: {session_id}")
        
        # Return the result directly as the service already uses the standard format
        return result
        
    except Exception as e:
        logger.error(f"Error in handle_test_start_transcription: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_test_stop_transcription(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.test_stop command for setup flow shortcut testing.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with test results and transcription text
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        # Check if in test mode
        if not hasattr(transcription_service, '_test_mode_active') or not transcription_service._test_mode_active:
            return error_response("No transcription test in progress", ErrorCodes.SERVICE_ERROR)
        
        # Stop transcription and get results
        result = await transcription_service.stop_transcription()
        
        if result.get("success"):
            # Extract test results
            session_summary = result.get("session_summary", {})
            transcription_text = session_summary.get("final_text", "")
            confidence = session_summary.get("average_confidence", 0.0)
            duration = session_summary.get("duration_seconds", 0.0)
            
            # Clear test mode
            transcription_service._test_mode_active = False
            test_session_id = getattr(transcription_service, '_test_session_id', None)
            transcription_service._test_session_id = None
            
            # Broadcast test completion event
            service_container = context.get('service_container')
            if service_container:
                ipc_bridge = service_container.resolve("ipc")
                if ipc_bridge:
                    await ipc_bridge.send_message({
                        "type": "event",
                        "event": "transcription_test_completed",
                        "data": {
                            "session_id": test_session_id,
                            "transcription_text": transcription_text,
                            "confidence": confidence,
                            "duration_seconds": duration,
                            "success": True,
                            "timestamp": time.time()
                        }
                    })
            
            # Return test-specific response
            test_result = {
                "success": True,
                "transcription_text": transcription_text,
                "confidence": confidence,
                "duration_seconds": duration,
                "session_summary": session_summary,
                "test_session_id": test_session_id
            }
            
            logger.info(f"Transcription test completed: '{transcription_text}' (confidence: {confidence:.2f})")
            return test_result
        
        return result
        
    except Exception as e:
        logger.error(f"Error in handle_test_stop_transcription: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_buffer_status(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.get_buffer_status command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with buffer status information
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        buffer_status = transcription_service.get_buffer_status()
        return success_response(buffer_status)
        
    except Exception as e:
        logger.error(f"Error in handle_get_buffer_status: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_clear_buffers(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.clear_buffers command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with operation result
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        transcription_service.clear_transcription_buffers()
        return success_response("Transcription buffers cleared successfully")
        
    except Exception as e:
        logger.error(f"Error in handle_clear_buffers: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_pause_buffers(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.pause_buffers command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with operation result
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        transcription_service.pause_transcription_buffers()
        return success_response("Transcription buffers paused successfully")
        
    except Exception as e:
        logger.error(f"Error in handle_pause_buffers: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_resume_buffers(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.resume_buffers command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with operation result
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        transcription_service.resume_transcription_buffers()
        return success_response("Transcription buffers resumed successfully")
        
    except Exception as e:
        logger.error(f"Error in handle_resume_buffers: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_diarization_status(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.get_diarization_status command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with speaker diarization status
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        if not transcription_service.diarization_enabled:
            return success_response({
                "diarization_enabled": False,
                "message": "Speaker diarization is disabled"
            })
        
        # Get speaker diarization statistics
        diarization_stats = transcription_service.speaker_diarization.get_statistics()
        
        # Get active speakers
        active_speakers = transcription_service.speaker_diarization.get_active_speakers()
        
        return success_response({
            "diarization_enabled": True,
            "statistics": diarization_stats,
            "active_speakers": active_speakers,
            "total_speakers": len(transcription_service.speaker_diarization.speaker_profiles)
        })
        
    except Exception as e:
        logger.error(f"Error in handle_get_diarization_status: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_clear_speaker_history(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.clear_speaker_history command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with operation result
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        if not transcription_service.diarization_enabled:
            return error_response("Speaker diarization is disabled", ErrorCodes.SERVICE_ERROR)
        
        transcription_service.speaker_diarization.clear_speaker_history()
        
        return success_response("Speaker history cleared successfully")
        
    except Exception as e:
        logger.error(f"Error in handle_clear_speaker_history: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_process_text(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.process_text command.
    
    Args:
        params: Command parameters containing text and optional mode_id
        context: Execution context
        
    Returns:
        Response dictionary with processed text and pipeline results
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        if not transcription_service.text_processing_enabled or not transcription_service.text_processing_pipeline:
            return error_response("Text processing pipeline is disabled", ErrorCodes.SERVICE_ERROR)
        
        # Get parameters
        text = params.get('text', '')
        mode_id = params.get('mode_id')
        speaker_segments = params.get('speaker_segments')
        preserve_metadata = params.get('preserve_metadata', True)
        
        if not text.strip():
            return error_response("No text provided for processing", ErrorCodes.BAD_REQUEST)
        
        # Process text through pipeline
        result = transcription_service.text_processing_pipeline.process_text(
            text=text,
            mode_id=mode_id,
            speaker_segments=speaker_segments,
            preserve_metadata=preserve_metadata
        )
        
        return success_response({
            "original_text": result.original_text,
            "processed_text": result.final_text,
            "total_changes": result.total_changes,
            "processing_time_ms": result.total_processing_time_ms,
            "confidence_score": result.confidence_score,
            "stage_results": [
                {
                    "stage_name": stage.stage_name,
                    "changes_made": len(stage.changes_made),
                    "processing_time_ms": stage.processing_time_ms,
                    "success": stage.success
                } for stage in result.stage_results
            ]
        })
        
    except Exception as e:
        logger.error(f"Error in handle_process_text: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_get_text_processing_status(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.get_text_processing_status command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with text processing pipeline status
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        if not transcription_service.text_processing_enabled or not transcription_service.text_processing_pipeline:
            return success_response({
                "text_processing_enabled": False,
                "message": "Text processing pipeline is disabled"
            })
        
        # Get text processing statistics
        stats = transcription_service.text_processing_pipeline.get_statistics()
        
        return success_response({
            "text_processing_enabled": True,
            "statistics": stats,
            "configuration": transcription_service.text_processing_pipeline.config
        })
        
    except Exception as e:
        logger.error(f"Error in handle_get_text_processing_status: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


async def handle_clear_text_processing_caches(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle transcription.clear_text_processing_caches command.
    
    Args:
        params: Command parameters (currently unused)
        context: Execution context
        
    Returns:
        Response dictionary with operation result
    """
    try:
        transcription_service = _get_transcription_service(context)
        if not transcription_service:
            return error_response("TranscriptionService not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        if not transcription_service.text_processing_enabled or not transcription_service.text_processing_pipeline:
            return error_response("Text processing pipeline is disabled", ErrorCodes.SERVICE_ERROR)
        
        transcription_service.text_processing_pipeline.clear_caches()
        
        return success_response("Text processing caches cleared successfully")
        
    except Exception as e:
        logger.error(f"Error in handle_clear_text_processing_caches: {e}")
        return error_response(str(e), ErrorCodes.SERVICE_ERROR)


def register_handlers(command_handlers: 'CommandHandlers') -> None:
    """Register transcription handlers with the command handlers.
    
    Args:
        command_handlers: The CommandHandlers instance to register with
    """
    logger.debug("Registering transcription handlers")
    
    # Core transcription commands
    command_handlers.registry.register_function("transcription.start", handle_start_transcription)
    command_handlers.registry.register_function("transcription.stop", handle_stop_transcription)
    command_handlers.registry.register_function("transcription.get_status", handle_get_transcription_status)
    command_handlers.registry.register_function("transcription.test", handle_test_transcription)
    
    # Setup flow test commands
    command_handlers.registry.register_function("transcription.test_start", handle_test_start_transcription)
    command_handlers.registry.register_function("transcription.test_stop", handle_test_stop_transcription)
    
    # Buffer management commands
    command_handlers.registry.register_function("transcription.get_buffer_status", handle_get_buffer_status)
    command_handlers.registry.register_function("transcription.clear_buffers", handle_clear_buffers)
    command_handlers.registry.register_function("transcription.pause_buffers", handle_pause_buffers)
    command_handlers.registry.register_function("transcription.resume_buffers", handle_resume_buffers)
    
    # Speaker diarization commands
    command_handlers.registry.register_function("transcription.get_diarization_status", handle_get_diarization_status)
    command_handlers.registry.register_function("transcription.clear_speaker_history", handle_clear_speaker_history)
    
    # Text processing commands
    command_handlers.registry.register_function("transcription.process_text", handle_process_text)
    command_handlers.registry.register_function("transcription.get_text_processing_status", handle_get_text_processing_status)
    command_handlers.registry.register_function("transcription.clear_text_processing_caches", handle_clear_text_processing_caches)
    
    logger.info("Transcription handlers registered successfully") 