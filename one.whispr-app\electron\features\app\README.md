# Authentication System

This directory contains the complete OAuth authentication system for the Electron app. The system enables seamless social login through Google and Twitter/X with automatic user data storage and session management.

## Architecture

The authentication system consists of several key components:

### Core Files

- **`types.ts`** - Contains all authentication-related TypeScript interfaces
- **`repository.ts`** - Database repositories for users, sessions, and auth settings
- **`service.ts`** - Main authentication service with business logic
- **`protocol-handler.ts`** - <PERSON>les `whispr://` protocol URLs for OAuth callbacks
- **`ipc.ts`** - IPC handlers for renderer-main process communication

### Database Schema

The system uses the existing JSON-based database storage for cloud sync compatibility:

- **Users table** - Stores user profile information
- **User Sessions table** - Manages access tokens and session data
- **Auth Settings table** - Per-user authentication preferences

## Complete OAuth Flow

1. **User clicks social login button** in renderer process
2. **URL opens with action parameter** (`&action=google` or `&action=twitter`)
3. **Website auto-triggers OAuth** flow based on action parameter  
4. **OAuth provider redirects** to `whispr://` protocol URL with user data
5. **Protocol handler processes** the callback URL
6. **Auth service stores** user data and creates session
7. **Renderer receives** authentication success notification via IPC

## Usage Examples

### Check Authentication Status (Renderer)

```typescript
// Check if user is authenticated
const authStatus = await window.electronAPI.invoke('auth:getStatus');
if (authStatus.isAuthenticated) {
  console.log('Current user:', authStatus.user);
  console.log('Session expires:', authStatus.session.expiresAt);
}
```

### Get Access Token (Renderer)

```typescript
// Get current user's access token for API calls
const token = await window.electronAPI.invoke('auth:getAccessToken');
if (token) {
  // Use token for authenticated API requests
  fetch('https://api.example.com/user', {
    headers: { Authorization: `Bearer ${token}` }
  });
}
```

### Logout (Renderer)

```typescript
// Logout current user
const success = await window.electronAPI.invoke('auth:logout');
if (success) {
  // Redirect to login or update UI
}
```

### Listen for Auth State Changes (Renderer)

```typescript
// Listen for authentication state changes
window.electronAPI.on('auth-state-changed', (data) => {
  if (data.isAuthenticated) {
    console.log('User logged in:', data.user);
    // Update UI to show authenticated state
  } else {
    console.log('User logged out');
    // Update UI to show login state
  }
});

// Listen for authentication success
window.electronAPI.on('auth-success', (data) => {
  console.log('Authentication successful:', data.user);
  // Handle successful authentication
});

// Listen for authentication errors
window.electronAPI.on('auth-error', (data) => {
  console.error('Authentication error:', data.error);
  // Handle authentication failure
});
```

## Main Process Integration

The authentication system is automatically initialized when the app starts:

```typescript
// In features/index.ts
export async function initializeFeatures(): Promise<void> {
  // ...
  setupAppIPCHandlers(); // Includes auth IPC handlers and protocol registration
  // ...
}
```

### Manual Auth Service Usage (Main Process)

```typescript
import { AuthService } from './features/app/service';

const authService = new AuthService();

// Initialize on app start
authService.initialize();

// Check authentication status
const status = authService.isAuthenticated();

// Get current user
const user = authService.getCurrentUser();

// Logout
authService.logout();
```

## Session Management

- **Automatic cleanup** of expired sessions on app start
- **Device-specific sessions** with platform information
- **Session expiry** based on JWT token expiration
- **Multi-device support** through unique device IDs

## Security Features

- **JWT token parsing** without external dependencies
- **Provider detection** from token claims
- **Session isolation** per device
- **Automatic token expiry** handling
- **Secure protocol URL** parsing with validation

## Cloud Sync Compatibility

All authentication data is stored using the existing JSON storage system, ensuring:

- **Seamless cloud sync** of user preferences
- **Cross-device restoration** of auth settings
- **Event-driven sync** when auth data changes
- **Consistent data format** across all features

## Error Handling

The system includes comprehensive error handling:

- **Invalid JWT tokens** are rejected gracefully
- **Provider detection failures** fall back to manual detection
- **Database errors** are logged and don't crash the app
- **Protocol URL parsing** handles malformed URLs safely
- **Session expiry** is handled automatically

## Development Notes

- Uses existing `BaseRepository` pattern for consistency
- Integrates with existing IPC architecture
- Follows established TypeScript patterns
- Compatible with existing database migration system
- Designed for cloud sync from the ground up 