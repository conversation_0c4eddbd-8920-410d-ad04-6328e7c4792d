import { MainAppDownloader } from '../download/mainAppDownloader';
import { BackendDownloader } from '../download/backendDownloader';
import { MicrosoftStoreHandler } from '../microsoft/storeHandler';
import { LaunchPathResolver } from './launchPathResolver';
import { ProcessChecker } from './processChecker';
import { LaunchReadinessChecker, ReadinessResult } from './launchReadinessChecker';
import { MainAppLauncher } from './mainAppLauncher';

/**
 * App Launcher Service - orchestrates the main app launching process
 * This is the main entry point that coordinates all launcher-related services
 */
export class AppLauncher {
  private pathResolver: LaunchPathResolver;
  private processChecker: ProcessChecker;
  private readinessChecker: LaunchReadinessChecker;
  private mainAppLauncher: MainAppLauncher;
  private microsoftStoreHandler: MicrosoftStoreHandler;

  constructor(
    mainAppDownloader: MainAppDownloader,
    backendDownloader: BackendDownloader,
    microsoftStoreHandler: MicrosoftStoreHandler
  ) {
    this.microsoftStoreHandler = microsoftStoreHandler;

    // Initialize services
    this.pathResolver = new LaunchPathResolver(mainAppDownloader, microsoftStoreHandler);
    this.processChecker = new ProcessChecker();
    this.readinessChecker = new LaunchReadinessChecker(
      mainAppDownloader,
      backendDownloader,
      microsoftStoreHandler,
      this.pathResolver
    );
    this.mainAppLauncher = new MainAppLauncher(this.processChecker, this.pathResolver);

    console.log('[APP_LAUNCHER] Launcher initialized with path:', this.pathResolver.getMainAppPath());
  }

  /**
   * Check if everything is ready to launch (main app + backend)
   */
  public async checkLaunchReady(): Promise<ReadinessResult> {
    return this.readinessChecker.checkLaunchReady();
  }

  /**
   * Launch the main app
   */
  public async launchMainApp(): Promise<boolean> {
    return this.mainAppLauncher.launchMainApp();
  }

  /**
   * Handle Microsoft Store download (copy from embedded resources)
   */
  public async handleMicrosoftStoreDownload(progressCallback?: (progress: number) => void): Promise<boolean> {
    return this.microsoftStoreHandler.copyMainAppToAppData(progressCallback);
  }

  /**
   * Legacy method for compatibility - now uses the integrated download flow
   */
  public async handleMicrosoftStoreSetup(): Promise<boolean> {
    return this.handleMicrosoftStoreDownload();
  }

  /**
   * Check if main app is already running
   */
  public async isMainAppAlreadyRunning(): Promise<boolean> {
    return this.processChecker.isMainAppAlreadyRunning();
  }

  /**
   * Get the main app path
   */
  public getMainAppPath(): string {
    return this.pathResolver.getMainAppPath();
  }

  /**
   * Check if currently launching
   */
  public isCurrentlyLaunching(): boolean {
    return this.mainAppLauncher.isCurrentlyLaunching();
  }

  /**
   * Clean up resources
   */
  public cleanup(): void {
    this.mainAppLauncher.cleanup();
  }
}