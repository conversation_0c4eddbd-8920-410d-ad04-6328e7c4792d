{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "module": "ES2020", "moduleResolution": "bundler", "resolveJsonModule": true, "jsx": "react-jsx", "strict": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@src/*": ["src/*"]}}, "exclude": ["node_modules"]}