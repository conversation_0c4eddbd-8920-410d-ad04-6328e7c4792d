import { UserRepository, UserSessionRepository, AuthSettingsRepository } from './repository';
import { User, UserSession, AuthCallbackData } from './types';
import { notifyAuthSuccess, notifyAuthFailure, notifyAuthStateChange } from './protocol-handler';

/**
 * Simple JWT decoder to extract payload without verification
 */
function decodeJWT(token: string): any {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = parts[1];
    const decoded = Buffer.from(payload, 'base64url').toString('utf8');
    return JSON.parse(decoded);
  } catch {
    return null;
  }
}

/**
 * Authentication service for managing user authentication and sessions
 */
export class AuthService {
  private userRepo: UserRepository;
  private sessionRepo: UserSessionRepository;
  private settingsRepo: AuthSettingsRepository;

  constructor() {
    this.userRepo = new UserRepository();
    this.sessionRepo = new UserSessionRepository();
    this.settingsRepo = new AuthSettingsRepository();
  }

  /**
   * Process OAuth authentication callback
   */
  async processOAuthCallback(callbackData: AuthCallbackData): Promise<{
    success: boolean;
    user?: User;
    session?: UserSession;
    error?: string;
  }> {
    try {
      console.log('Processing OAuth callback for email:', callbackData.email);
      
      // Decode JWT to get provider information
      const decodedToken = decodeJWT(callbackData.token);
      if (!decodedToken) {
        throw new Error('Invalid JWT token');
      }
      
      // Extract provider ID from token (use email as fallback)
      const providerId = decodedToken.sub || decodedToken.user_id || decodedToken.id || callbackData.email;
      console.log('Using provider ID:', providerId ? '***' : 'none');
      
      // Create or update user
      const user = this.userRepo.upsertFromOAuth({
        email: callbackData.email,
        firstName: callbackData.firstName,
        lastName: callbackData.lastName,
        fullName: callbackData.fullName,
        avatarUrl: decodedToken.picture || decodedToken.avatar_url,
        providerId,
      });
      
      // Deactivate any existing sessions for this user
      this.sessionRepo.deactivateUserSessions(user.id);
      
      // Create new session
      const session = this.sessionRepo.createFromToken(user.id, callbackData.token, {
        deviceInfo: {
          platform: process.platform,
          deviceId: `electron-${Date.now()}`,
        },
      });
      
      // Ensure auth settings exist
      this.settingsRepo.getOrCreateForUser(user.id);
      
      // Clean up expired sessions
      this.sessionRepo.cleanupExpiredSessions();
      
      // Notify renderer of successful authentication
      notifyAuthSuccess({
        user,
        session,
        token: callbackData.token,
      });
      
      // Notify auth state change
      notifyAuthStateChange(true, user);
      
      console.log('OAuth authentication successful for user:', user.email);
      
      return {
        success: true,
        user,
        session,
      };
      
    } catch (error) {
      console.error('OAuth authentication failed:', error);
      
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
      
      // Notify renderer of authentication failure
      notifyAuthFailure(errorMessage);
      
      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): { isAuthenticated: boolean; user?: User; session?: UserSession } {
    try {
      const user = this.userRepo.getActiveUser();
      if (!user) {
        return { isAuthenticated: false };
      }
      
      const session = this.sessionRepo.getActiveSession(user.id);
      if (!session) {
        // User exists but no active session - deactivate user
        user.isActive = false;
        user.updatedAt = new Date().toISOString();
        this.userRepo.save(user);
        return { isAuthenticated: false };
      }
      
      // Update last accessed time
      this.sessionRepo.updateLastAccessed(session.id);
      
      return {
        isAuthenticated: true,
        user,
        session,
      };
      
    } catch (error) {
      console.error('Error checking authentication status:', error);
      return { isAuthenticated: false };
    }
  }

  /**
   * Get current user's access token
   */
  getAccessToken(): string | null {
    try {
      const authStatus = this.isAuthenticated();
      return authStatus.session?.accessToken || null;
    } catch (error) {
      console.error('Error getting access token:', error);
      return null;
    }
  }

  /**
   * Logout current user
   */
  logout(): boolean {
    try {
      // Deactivate all users and sessions
      this.userRepo.deactivateAllUsers();
      
      const users = this.userRepo.findAll();
      users.forEach(user => {
        this.sessionRepo.deactivateUserSessions(user.id);
      });
      
      // Clean up expired sessions
      this.sessionRepo.cleanupExpiredSessions();
      
      // Notify renderer of logout
      notifyAuthStateChange(false);
      
      console.log('User logged out successfully');
      return true;
      
    } catch (error) {
      console.error('Error during logout:', error);
      return false;
    }
  }

  /**
   * Update user activity (extend session)
   */
  updateActivity(): boolean {
    try {
      const authStatus = this.isAuthenticated();
      if (!authStatus.isAuthenticated || !authStatus.session) {
        return false;
      }
      
      return this.sessionRepo.updateLastAccessed(authStatus.session.id);
      
    } catch (error) {
      console.error('Error updating user activity:', error);
      return false;
    }
  }

  /**
   * Get current user
   */
  getCurrentUser(): User | null {
    const authStatus = this.isAuthenticated();
    return authStatus.user || null;
  }

  /**
   * Get user's auth settings
   */
  getUserSettings(userId?: string): any {
    try {
      const targetUserId = userId || this.getCurrentUser()?.id;
      if (!targetUserId) return null;
      
      return this.settingsRepo.getOrCreateForUser(targetUserId);
    } catch (error) {
      console.error('Error getting user settings:', error);
      return null;
    }
  }

  /**
   * Initialize authentication service
   * Checks for existing valid sessions and updates auth state
   */
  initialize(): void {
    try {
      console.log('Initializing authentication service...');
      
      // Clean up expired sessions on startup
      const cleanedCount = this.sessionRepo.cleanupExpiredSessions();
      if (cleanedCount > 0) {
        console.log(`Cleaned up ${cleanedCount} expired sessions`);
      }
      
      // Check current authentication status
      const authStatus = this.isAuthenticated();
      
      // Notify renderer of initial auth state
      notifyAuthStateChange(authStatus.isAuthenticated, authStatus.user);
      
      if (authStatus.isAuthenticated) {
        console.log('User is already authenticated:', authStatus.user?.email);
      } else {
        console.log('No authenticated user found');
      }
      
    } catch (error) {
      console.error('Error initializing authentication service:', error);
    }
  }
} 