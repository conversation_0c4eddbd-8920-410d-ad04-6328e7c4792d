"""
Silero VAD Integration

Clean wrapper around Silero VAD for voice activity detection.
Processes 100ms chunks and provides speech probability scores.
"""

import logging
import threading
from typing import Optional, Dict, Any, List
import numpy as np

logger = logging.getLogger(__name__)

try:
    from silero_vad import load_silero_vad
    SILERO_AVAILABLE = True
except ImportError:
    SILERO_AVAILABLE = False
    logger.warning("Silero VAD not available. Install with: pip install silero-vad")


class SileroVAD:
    """Dynamic Silero VAD wrapper for 100ms chunk processing with adaptive thresholding."""

    def __init__(self, sample_rate: int = 16000, base_threshold: float = 0.5,
                 vad_enabled: bool = True, config: Optional[Dict[str, Any]] = None):
        """Initialize Silero VAD with dynamic thresholding.

        Args:
            sample_rate: Audio sample rate (8000 or 16000 supported)
            base_threshold: Base threshold for dynamic adjustment
            vad_enabled: Whether VAD filtering is enabled
            config: Optional configuration dictionary for additional settings
        """
        self.sample_rate = sample_rate
        self.base_threshold = base_threshold
        self.vad_enabled = vad_enabled
        self.config = config or {}

        if not self.vad_enabled:
            logger.info("VAD is disabled")
        
        # Validate sample rate
        if sample_rate not in [8000, 16000]:
            raise ValueError("Silero VAD only supports 8000Hz and 16000Hz sample rates")
        
        # Model state
        self.model = None
        self.is_initialized = False
        
        # Chunk processing
        self.window_size_samples = 512 if sample_rate == 16000 else 256  # 32ms chunks for Silero
        self.chunks_per_100ms = int(0.1 * sample_rate) // self.window_size_samples  # ~3 chunks per 100ms
        
        # Dynamic thresholding
        self.adaptive_threshold = base_threshold
        self.probability_history = []
        self.history_size = 30  # Keep last 3 seconds at 100ms chunks (shorter for responsiveness)
        self.noise_floor = 0.1  # Minimum threshold to avoid false positives
        self.adaptation_rate = 0.15  # How quickly to adapt threshold (increased for responsiveness)
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Statistics
        self.total_chunks = 0
        self.speech_chunks = 0
        self.adaptation_count = 0
        
        logger.debug(f"Dynamic SileroVAD initialized: {sample_rate}Hz, base_threshold={base_threshold}")
    
    def initialize(self) -> bool:
        """Initialize the Silero VAD model.
        
        Returns:
            True if initialization successful
        """
        with self.lock:
            try:
                if not SILERO_AVAILABLE:
                    logger.error("Silero VAD dependencies not available")
                    return False
                
                if self.is_initialized:
                    return True
                
                logger.info("Loading Silero VAD model...")
                
                # Load model - silero-vad handles torch internally
                self.model = load_silero_vad()
                
                # Reset model states
                self.model.reset_states()
                
                self.is_initialized = True
                logger.info("Silero VAD model loaded successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to initialize Silero VAD: {e}")
                return False
    
    def process_chunk(self, audio_chunk: np.ndarray) -> Dict[str, Any]:
        """Process a 100ms audio chunk for voice activity detection.
        
        Args:
            audio_chunk: Audio data (1600 samples at 16kHz)
            
        Returns:
            Dictionary with VAD results
        """
        with self.lock:
            try:
                # Return early if VAD is disabled in settings
                if not self.vad_enabled:
                    return {
                        "is_speech": True,  # Always consider as speech when VAD disabled
                        "probability": 1.0,
                        "avg_probability": 1.0,
                        "confidence": "disabled",
                        "window_count": 0,
                        "adaptive_threshold": self.base_threshold,
                        "base_threshold": self.base_threshold,
                        "vad_enabled": False
                    }
                
                if not self.is_initialized:
                    if not self.initialize():
                        return self._default_result()
                
                # Ensure audio is correct type and size
                if not isinstance(audio_chunk, np.ndarray):
                    audio_chunk = np.array(audio_chunk, dtype=np.float32)
                else:
                    audio_chunk = audio_chunk.astype(np.float32)
                
                # Expected chunk size for 100ms
                expected_size = int(0.1 * self.sample_rate)
                if len(audio_chunk) != expected_size:
                    logger.debug(f"Unexpected chunk size: {len(audio_chunk)}, expected {expected_size}")
                    # Pad or truncate to expected size
                    if len(audio_chunk) < expected_size:
                        padded = np.zeros(expected_size, dtype=np.float32)
                        padded[:len(audio_chunk)] = audio_chunk
                        audio_chunk = padded
                    else:
                        audio_chunk = audio_chunk[:expected_size]
                
                # Split 100ms chunk into ~32ms windows for Silero VAD
                speech_probs = []
                
                for i in range(0, len(audio_chunk), self.window_size_samples):
                    window = audio_chunk[i:i + self.window_size_samples]
                    
                    # Skip incomplete windows
                    if len(window) < self.window_size_samples:
                        continue
                    
                    # Get speech probability from Silero VAD
                    try:
                        # Convert numpy array to torch tensor
                        import torch
                        window_tensor = torch.from_numpy(window.copy())
                        
                        # Get speech probability
                        speech_prob = self.model(window_tensor, self.sample_rate).item()
                        speech_probs.append(speech_prob)
                        
                    except Exception as e:
                        logger.debug(f"Error processing VAD window: {e}")
                        continue
                
                # Calculate overall speech probability for the 100ms chunk
                if speech_probs:
                    avg_prob = np.mean(speech_probs)
                    max_prob = np.max(speech_probs)
                    
                    # Use max probability as it's more sensitive to speech segments
                    final_prob = max_prob
                else:
                    final_prob = 0.0
                
                # Update probability history for dynamic thresholding
                self.probability_history.append(final_prob)
                if len(self.probability_history) > self.history_size:
                    self.probability_history.pop(0)
                
                # Adapt threshold based on recent history
                self._adapt_threshold()
                
                # Determine if speech is detected using adaptive threshold
                is_speech = final_prob >= self.adaptive_threshold
                
                # Update statistics
                self.total_chunks += 1
                if is_speech:
                    self.speech_chunks += 1
                
                return {
                    "is_speech": is_speech,
                    "probability": final_prob,
                    "avg_probability": np.mean(speech_probs) if speech_probs else 0.0,
                    "confidence": "high" if final_prob > 0.7 else "medium" if final_prob > 0.3 else "low",
                    "window_count": len(speech_probs),
                    "adaptive_threshold": self.adaptive_threshold,
                    "base_threshold": self.base_threshold,
                    "vad_enabled": self.vad_enabled
                }
                
            except Exception as e:
                logger.error(f"Error in VAD processing: {e}")
                return self._default_result()
    
    def _adapt_threshold(self) -> None:
        """Adapt the threshold based on recent probability history."""
        if len(self.probability_history) < 5:  # Need some history to adapt (reduced from 10)
            return
        
        try:
            # Calculate statistics from recent history
            recent_probs = np.array(self.probability_history)
            
            # Calculate noise floor (10th percentile - lower to be less aggressive)
            noise_level = np.percentile(recent_probs, 10)
            
            # Calculate median as baseline rather than 75th percentile
            # This is much less aggressive and more responsive to speech
            median_prob = np.percentile(recent_probs, 50)
            
            # If recent probabilities are very low (mostly silence), lower threshold
            # If recent probabilities show speech activity, use median + small margin
            if median_prob < 0.2:  # Mostly silence
                target_threshold = max(self.noise_floor, self.base_threshold * 0.7)  # Lower threshold
            else:  # Some speech activity
                target_threshold = max(self.base_threshold, median_prob * 0.8)  # Reasonable threshold
            
            # More reasonable bounds
            min_threshold = self.noise_floor
            max_threshold = 0.6  # Reduced from 0.8 to be less strict
            
            target_threshold = max(min_threshold, min(max_threshold, target_threshold))
            
            # Gradually adapt towards target (smooth changes)
            adaptation_step = self.adaptation_rate * (target_threshold - self.adaptive_threshold)
            self.adaptive_threshold += adaptation_step
            
            # Ensure it stays within bounds
            self.adaptive_threshold = max(min_threshold, min(max_threshold, self.adaptive_threshold))
            
            self.adaptation_count += 1
            
            # Log significant threshold changes
            if abs(self.adaptive_threshold - self.base_threshold) > 0.1:
                if self.adaptation_count % 30 == 0:  # Log every 3 seconds (increased frequency)
                    logger.debug(f"VAD adaptive threshold: {self.adaptive_threshold:.3f} (base: {self.base_threshold:.3f}, noise: {noise_level:.3f})")
        
        except Exception as e:
            logger.debug(f"Error adapting VAD threshold: {e}")
    
    def _default_result(self) -> Dict[str, Any]:
        """Return default VAD result when processing fails."""
        return {
            "is_speech": False,
            "probability": 0.0,
            "avg_probability": 0.0,
            "confidence": "low",
            "window_count": 0,
            "adaptive_threshold": self.adaptive_threshold,
            "base_threshold": self.base_threshold,
            "vad_enabled": self.vad_enabled
        }
    
    def reset_states(self) -> None:
        """Reset VAD model states and adaptive threshold history."""
        with self.lock:
            try:
                if self.model is not None:
                    self.model.reset_states()
                
                # Reset adaptive threshold state
                self.adaptive_threshold = self.base_threshold
                self.probability_history.clear()
                self.adaptation_count = 0
                
                logger.debug("VAD model states and adaptive threshold reset")
            except Exception as e:
                logger.error(f"Error resetting VAD states: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get VAD processing statistics.
        
        Returns:
            Dictionary with processing statistics
        """
        with self.lock:
            speech_ratio = self.speech_chunks / self.total_chunks if self.total_chunks > 0 else 0.0
            avg_probability = np.mean(self.probability_history) if self.probability_history else 0.0
            
            return {
                "total_chunks": self.total_chunks,
                "speech_chunks": self.speech_chunks,
                "speech_ratio": speech_ratio,
                "base_threshold": self.base_threshold,
                "adaptive_threshold": self.adaptive_threshold,
                "avg_probability": avg_probability,
                "history_size": len(self.probability_history),
                "adaptation_count": self.adaptation_count,
                "sample_rate": self.sample_rate,
                "initialized": self.is_initialized
            }
    
    def set_base_threshold(self, threshold: float) -> None:
        """Update the base threshold for dynamic adaptation.
        
        Args:
            threshold: New base threshold value (0.0 - 1.0)
        """
        if 0.0 <= threshold <= 1.0:
            self.base_threshold = threshold
            self.adaptive_threshold = threshold  # Reset adaptive threshold
            logger.info(f"VAD base threshold updated to {threshold}")
        else:
            logger.warning(f"Invalid threshold {threshold}, must be between 0.0 and 1.0")
    
    def cleanup(self) -> None:
        """Cleanup VAD resources."""
        with self.lock:
            try:
                if self.model is not None:
                    self.model.reset_states()
                self.model = None
                self.is_initialized = False
                logger.info("Silero VAD cleaned up")
            except Exception as e:
                logger.error(f"Error during VAD cleanup: {e}")
    
    def __del__(self):
        """Cleanup on object destruction."""
        try:
            self.cleanup()
        except:
            pass 