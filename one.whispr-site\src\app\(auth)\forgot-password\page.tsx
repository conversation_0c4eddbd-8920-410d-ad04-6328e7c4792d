'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { AuthCard } from '../_components/ui/AuthCard';
import { Auth<PERSON>lowController, AuthFlowFooterContent } from '../_lib/auth-flow';
import { passwordResetSteps } from '../_lib/auth-steps';
import { SearchParamsWrapper } from '@/components/suspense/SearchParamsWrapper';
import { FiLoader } from 'react-icons/fi';
import { requestPasswordReset } from './actions';

// Client component that handles the password reset form and steps
function ForgotPasswordContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const step = searchParams.get('step') || 'email';
  const email = searchParams.get('email') || '';
  const token = searchParams.get('token') || '';
  
  // Auto-skip to OTP step if email is provided and we're on the email step
  useEffect(() => {
    // Only proceed if we have an email and we're on the email step
    if (email && step === 'email') {
      // The trigger param indicates we came directly from login
      const trigger = searchParams.get('trigger');
      
      // Show loading state immediately by redirecting to OTP step
      // This prevents showing the email form momentarily
      router.push(`/forgot-password?step=otp&email=${encodeURIComponent(email)}&autoSubmitted=true${trigger ? `&trigger=${trigger}` : ''}`);
      
      // Then process the actual password reset request in the background
      const autoSubmit = async () => {
        try {
          const formData = new FormData();
          formData.append('email', email);
          
          await requestPasswordReset(formData);
          // No need for another redirect since we already moved to the OTP step
        } catch (error) {
          console.error('Error auto-submitting password reset:', error);
        }
      };
      
      autoSubmit();
    }
  }, [email, step, router]);
  
  // Extract all search params
  const params: Record<string, string | null | any> = {};
  searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  // Custom handler for OTP verification
  const handleOtpVerified = () => {
    router.push(`/forgot-password?step=reset&email=${encodeURIComponent(email)}&token=verified`);
  };
  
  // Add the custom callback to params for the OTP step
  if (step === 'otp') {
    params.onVerify = handleOtpVerified;
  }

  // Generate footer content based on step
  const footerContent: AuthFlowFooterContent = {
    email: (
      <p className="text-base text-muted-foreground">
        Remember your password?{' '}
        <Link 
          href="/login" 
          className="font-medium text-primary hover:underline underline-offset-4"
        >
          Sign In
        </Link>
      </p>
    ),
    otp: (
      <p className="text-base text-muted-foreground">
        Can't find your email? Check your spam folder.
      </p>
    ),
    reset: (
      <p className="text-base text-muted-foreground">
        Remember your password?{' '}
        <Link 
          href="/login" 
          className="font-medium text-primary hover:underline underline-offset-4"
        >
          Sign In
        </Link>
      </p>
    )
  };

  return (
    <AuthFlowController
      steps={passwordResetSteps}
      currentStep={step}
      params={{ email, token, onVerify: step === 'otp' ? handleOtpVerified : undefined }}
      footerContent={footerContent}
      renderCard={({ title, children, footerContent }) => (
        <AuthCard title={title} footerContent={footerContent}>
          {children}
        </AuthCard>
      )}
    />
  );
}

// Main page component with the SearchParamsWrapper
export default function ForgotPasswordPage() {
  return (
    <SearchParamsWrapper
      fallback={
        <AuthCard title="Loading..." footerContent={null}>
          <div className="flex justify-center items-center py-8">
            <FiLoader className="animate-spin text-primary w-6 h-6" />
          </div>
        </AuthCard>
      }
    >
      <ForgotPasswordContent />
    </SearchParamsWrapper>
  );
} 