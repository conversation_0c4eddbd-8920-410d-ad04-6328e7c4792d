"""
Model registry for One.Whispr voice models.

This module contains the registry of all supported voice models
with their metadata and file specifications.
"""

import logging
from typing import Dict, List, Optional
from .model_metadata import ModelMetadata, ModelFile

logger = logging.getLogger("whispr.models.registry")

# Voice models registry based on models_simplified.md
VOICE_MODELS_DATA = [
    {
        "id": "openai/whisper-tiny",
        "name": "Whisper Tiny",
        "size": "144 MB",
        "languages": "Multilingual",
        "isEnglishOnly": False,
        "isRecommended": False,
        "type": "Standard",
        "files": [
            {
                "name": "model.safetensors",
                "size": 151061672,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/model.safetensors",
                "required": True
            },
            {
                "name": "config.json",
                "size": 1983,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/config.json",
                "required": True
            },
            {
                "name": "tokenizer.json",
                "size": 2480466,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/tokenizer.json",
                "required": True
            },
            {
                "name": "generation_config.json",
                "size": 3747,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/generation_config.json",
                "required": True
            },
            {
                "name": "tokenizer_config.json",
                "size": 282683,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/tokenizer_config.json",
                "required": True
            },
            {
                "name": "preprocessor_config.json",
                "size": 184990,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/preprocessor_config.json",
                "required": True
            },
            {
                "name": "added_tokens.json",
                "size": 34604,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/added_tokens.json",
                "required": True
            },
            {
                "name": "normalizer.json",
                "size": 52666,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/normalizer.json",
                "required": True
            },
            {
                "name": "merges.txt",
                "size": 493869,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/merges.txt",
                "required": True
            },
            {
                "name": "special_tokens_map.json",
                "size": 2194,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/special_tokens_map.json",
                "required": True
            },
            {
                "name": "vocab.json",
                "size": 835550,
                "url": "https://huggingface.co/openai/whisper-tiny/resolve/main/vocab.json",
                "required": True
            }
        ]
    },
    {
        "id": "openai/whisper-base",
        "name": "Whisper Base",
        "size": "277 MB",
        "languages": "Multilingual",
        "isEnglishOnly": False,
        "isRecommended": True,
        "type": "Standard",
        "files": [
            {
                "name": "model.safetensors",
                "size": 290403936,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/model.safetensors",
                "required": True
            },
            {
                "name": "config.json",
                "size": 1983,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/config.json",
                "required": True
            },
            {
                "name": "tokenizer.json",
                "size": 2480466,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/tokenizer.json",
                "required": True
            },
            {
                "name": "generation_config.json",
                "size": 3807,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/generation_config.json",
                "required": True
            },
            {
                "name": "tokenizer_config.json",
                "size": 282683,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/tokenizer_config.json",
                "required": True
            },
            {
                "name": "preprocessor_config.json",
                "size": 184990,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/preprocessor_config.json",
                "required": True
            },
            {
                "name": "added_tokens.json",
                "size": 34604,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/added_tokens.json",
                "required": True
            },
            {
                "name": "normalizer.json",
                "size": 52666,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/normalizer.json",
                "required": True
            },
            {
                "name": "merges.txt",
                "size": 493869,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/merges.txt",
                "required": True
            },
            {
                "name": "special_tokens_map.json",
                "size": 2194,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/special_tokens_map.json",
                "required": True
            },
            {
                "name": "vocab.json",
                "size": 835550,
                "url": "https://huggingface.co/openai/whisper-base/resolve/main/vocab.json",
                "required": True
            }
        ]
    },
    {
        "id": "openai/whisper-base.en",
        "name": "Whisper Base",
        "size": "277 MB",
        "languages": "English only",
        "isEnglishOnly": True,
        "isRecommended": False,
        "type": "Standard",
        "files": [
            {
                "name": "model.safetensors",
                "size": 290401888,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/model.safetensors",
                "required": True
            },
            {
                "name": "config.json",
                "size": 1937,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/config.json",
                "required": True
            },
            {
                "name": "tokenizer.json",
                "size": 2405679,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/tokenizer.json",
                "required": True
            },
            {
                "name": "generation_config.json",
                "size": 1531,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/generation_config.json",
                "required": True
            },
            {
                "name": "tokenizer_config.json",
                "size": 805,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/tokenizer_config.json",
                "required": True
            },
            {
                "name": "preprocessor_config.json",
                "size": 184990,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/preprocessor_config.json",
                "required": True
            },
            {
                "name": "added_tokens.json",
                "size": 34604,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/added_tokens.json",
                "required": True
            },
            {
                "name": "normalizer.json",
                "size": 52666,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/normalizer.json",
                "required": True
            },
            {
                "name": "merges.txt",
                "size": 456318,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/merges.txt",
                "required": True
            },
            {
                "name": "special_tokens_map.json",
                "size": 1830,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/special_tokens_map.json",
                "required": True
            },
            {
                "name": "vocab.json",
                "size": 798156,
                "url": "https://huggingface.co/openai/whisper-base.en/resolve/main/vocab.json",
                "required": True
            }
        ]
    },
    {
        "id": "openai/whisper-small",
        "name": "Whisper Small",
        "size": "922 MB",
        "languages": "Multilingual",
        "isEnglishOnly": False,
        "isRecommended": False,
        "type": "Standard",
        "files": [
            {
                "name": "model.safetensors",
                "size": 966995080,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/model.safetensors",
                "required": True
            },
            {
                "name": "config.json",
                "size": 1967,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/config.json",
                "required": True
            },
            {
                "name": "tokenizer.json",
                "size": 2480466,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/tokenizer.json",
                "required": True
            },
            {
                "name": "generation_config.json",
                "size": 3868,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/generation_config.json",
                "required": True
            },
            {
                "name": "tokenizer_config.json",
                "size": 282683,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/tokenizer_config.json",
                "required": True
            },
            {
                "name": "preprocessor_config.json",
                "size": 184990,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/preprocessor_config.json",
                "required": True
            },
            {
                "name": "added_tokens.json",
                "size": 34604,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/added_tokens.json",
                "required": True
            },
            {
                "name": "normalizer.json",
                "size": 52666,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/normalizer.json",
                "required": True
            },
            {
                "name": "merges.txt",
                "size": 493869,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/merges.txt",
                "required": True
            },
            {
                "name": "special_tokens_map.json",
                "size": 2194,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/special_tokens_map.json",
                "required": True
            },
            {
                "name": "vocab.json",
                "size": 835550,
                "url": "https://huggingface.co/openai/whisper-small/resolve/main/vocab.json",
                "required": True
            }
        ]
    },
    {
        "id": "distil-whisper/distil-small.en",
        "name": "Distil Whisper Small",
        "size": "317 MB",
        "languages": "English only",
        "isEnglishOnly": True,
        "isRecommended": True,
        "type": "Distilled",
        "files": [
            {
                "name": "model.safetensors",
                "size": 332297344,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/model.safetensors",
                "required": True
            },
            {
                "name": "config.json",
                "size": 2262,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/config.json",
                "required": True
            },
            {
                "name": "tokenizer.json",
                "size": 2405466,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/tokenizer.json",
                "required": True
            },
            {
                "name": "generation_config.json",
                "size": 1918,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/generation_config.json",
                "required": True
            },
            {
                "name": "tokenizer_config.json",
                "size": 282499,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/tokenizer_config.json",
                "required": True
            },
            {
                "name": "preprocessor_config.json",
                "size": 339,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/preprocessor_config.json",
                "required": True
            },
            {
                "name": "added_tokens.json",
                "size": 34604,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/added_tokens.json",
                "required": True
            },
            {
                "name": "normalizer.json",
                "size": 52666,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/normalizer.json",
                "required": True
            },
            {
                "name": "merges.txt",
                "size": 456318,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/merges.txt",
                "required": True
            },
            {
                "name": "special_tokens_map.json",
                "size": 2169,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/special_tokens_map.json",
                "required": True
            },
            {
                "name": "vocab.json",
                "size": 999186,
                "url": "https://huggingface.co/distil-whisper/distil-small.en/resolve/main/vocab.json",
                "required": True
            }
        ]
    },
    {
        "id": "distil-whisper/distil-medium.en",
        "name": "Distil Whisper Medium",
        "size": "752 MB",
        "languages": "English only",
        "isEnglishOnly": True,
        "isRecommended": False,
        "type": "Distilled",
        "files": [
            {
                "name": "model.safetensors",
                "size": 788798920,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/model.safetensors",
                "required": True
            },
            {
                "name": "config.json",
                "size": 2264,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/config.json",
                "required": True
            },
            {
                "name": "tokenizer.json",
                "size": 2405678,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/tokenizer.json",
                "required": True
            },
            {
                "name": "generation_config.json",
                "size": 1311,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/generation_config.json",
                "required": True
            },
            {
                "name": "tokenizer_config.json",
                "size": 282661,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/tokenizer_config.json",
                "required": True
            },
            {
                "name": "preprocessor_config.json",
                "size": 339,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/preprocessor_config.json",
                "required": True
            },
            {
                "name": "added_tokens.json",
                "size": 34604,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/added_tokens.json",
                "required": True
            },
            {
                "name": "normalizer.json",
                "size": 52666,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/normalizer.json",
                "required": True
            },
            {
                "name": "merges.txt",
                "size": 456318,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/merges.txt",
                "required": True
            },
            {
                "name": "special_tokens_map.json",
                "size": 2058,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/special_tokens_map.json",
                "required": True
            },
            {
                "name": "vocab.json",
                "size": 798156,
                "url": "https://huggingface.co/distil-whisper/distil-medium.en/resolve/main/vocab.json",
                "required": True
            }
        ]
    },
    {
        "id": "distil-whisper/distil-large-v3",
        "name": "Distil Whisper Large V3",
        "size": "1.4 GB",
        "languages": "English only",
        "isEnglishOnly": True,
        "isRecommended": False,
        "type": "Distilled",
        "files": [
            {
                "name": "model.safetensors",
                "size": 1512874472,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/model.safetensors",
                "required": True
            },
            {
                "name": "config.json",
                "size": 1372,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/config.json",
                "required": True
            },
            {
                "name": "tokenizer.json",
                "size": 2480617,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/tokenizer.json",
                "required": True
            },
            {
                "name": "generation_config.json",
                "size": 4247,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/generation_config.json",
                "required": True
            },
            {
                "name": "tokenizer_config.json",
                "size": 282843,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/tokenizer_config.json",
                "required": True
            },
            {
                "name": "preprocessor_config.json",
                "size": 340,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/preprocessor_config.json",
                "required": True
            },
            {
                "name": "added_tokens.json",
                "size": 34648,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/added_tokens.json",
                "required": True
            },
            {
                "name": "normalizer.json",
                "size": 52666,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/normalizer.json",
                "required": True
            },
            {
                "name": "merges.txt",
                "size": 493869,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/merges.txt",
                "required": True
            },
            {
                "name": "special_tokens_map.json",
                "size": 2072,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/special_tokens_map.json",
                "required": True
            },
            {
                "name": "vocab.json",
                "size": 1036558,
                "url": "https://huggingface.co/distil-whisper/distil-large-v3/resolve/main/vocab.json",
                "required": True
            }
        ]
    }
]


class ModelRegistry:
    """Registry for managing voice model definitions and metadata."""

    _instance = None
    _initialized = False

    def __new__(cls):
        """Ensure only one instance of ModelRegistry exists (singleton pattern)."""
        if cls._instance is None:
            cls._instance = super(ModelRegistry, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """Initialize the model registry."""
        # Only initialize once
        if ModelRegistry._initialized:
            return

        self.logger = logging.getLogger("whispr.models.ModelRegistry")
        self._models: Dict[str, ModelMetadata] = {}
        self._initialize_models()
        ModelRegistry._initialized = True
    
    def _initialize_models(self):
        """Initialize models from the registry data."""
        for model_data in VOICE_MODELS_DATA:
            try:
                # Create ModelFile objects
                files = []
                for file_data in model_data["files"]:
                    model_file = ModelFile(
                        name=file_data["name"],
                        size=file_data["size"],
                        url=file_data["url"],
                        required=file_data.get("required", True)
                    )
                    files.append(model_file)
                
                # Create ModelMetadata
                metadata = ModelMetadata(
                    id=model_data["id"],
                    name=model_data["name"],
                    size=model_data["size"],
                    languages=model_data["languages"],
                    is_english_only=model_data["isEnglishOnly"],
                    is_recommended=model_data["isRecommended"],
                    model_type=model_data["type"],
                    files=files
                )
                
                self._models[model_data["id"]] = metadata
                self.logger.debug(f"Registered model: {model_data['id']}")
                
            except Exception as e:
                self.logger.error(f"Error initializing model {model_data.get('id', 'unknown')}: {e}")
        
        self.logger.info(f"Initialized {len(self._models)} models in registry")
    
    def get_model(self, model_id: str) -> Optional[ModelMetadata]:
        """Get model metadata by ID."""
        return self._models.get(model_id)
    
    def get_all_models(self) -> List[ModelMetadata]:
        """Get all registered models."""
        return list(self._models.values())
    
    def get_model_ids(self) -> List[str]:
        """Get all registered model IDs."""
        return list(self._models.keys())
    
    def has_model(self, model_id: str) -> bool:
        """Check if a model is registered."""
        return model_id in self._models
    
    def get_recommended_models(self) -> List[ModelMetadata]:
        """Get all recommended models."""
        return [model for model in self._models.values() if model.is_recommended]
    
    def update_model_metadata(self, model_id: str, metadata: ModelMetadata):
        """Update model metadata in the registry."""
        if model_id in self._models:
            self._models[model_id] = metadata
            self.logger.debug(f"Updated metadata for model: {model_id}")
        else:
            self.logger.warning(f"Attempted to update unknown model: {model_id}")


# Global registry instance
VOICE_MODELS = ModelRegistry() 