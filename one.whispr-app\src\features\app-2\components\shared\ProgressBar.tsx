/**
 * Progress Bar
 * Step progress indicator component matching app-latest design
 * Shows current step progress with visual indicators
 */

import { Progress } from '@src/components/ui/progress';
import { cn } from '@src/lib/utils';

// ============================================================================
// TYPES
// ============================================================================

interface ProgressBarProps {
  currentStep: number;
  totalSteps: number;
  className?: string;
  showStepIndicator?: boolean;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

// ============================================================================
// COMPONENT
// ============================================================================

export function ProgressBar({
  currentStep,
  totalSteps,
  className,
  showStepIndicator = true,
  showPercentage = false,
  size = 'md'
}: ProgressBarProps) {
  const progress = Math.round((currentStep / totalSteps) * 100);
  
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  return (
    <div className={cn('w-full space-y-2', className)}>
      {(showStepIndicator || showPercentage) && (
        <div className="flex justify-between items-center text-sm">
          {showStepIndicator && (
            <span className="text-muted-foreground">
              Step {currentStep} of {totalSteps}
            </span>
          )}
          {showPercentage && (
            <span className="text-muted-foreground">
              {progress}%
            </span>
          )}
        </div>
      )}
      
      <Progress 
        value={progress} 
        className={cn(sizeClasses[size])}
      />
    </div>
  );
}

export default ProgressBar; 