"""
Database command handlers for One.Whispr.

This module contains handlers for database synchronization commands.
"""

import logging
from typing import Dict, Any, TYPE_CHECKING

from whispr.core.response_utils import success_response, error_response, ErrorCodes

if TYPE_CHECKING:
    from whispr.core.handlers import CommandHandlers

# Configure logging
logger = logging.getLogger('whispr.handlers.database')


async def handle_database_initial_sync(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle database initial sync from the client.
    
    Args:
        params: The database data
        context: The execution context
        
    Returns:
        Success response with sync status
    """
    service_container = context.get('service_container')
    if service_container:
        # Get database manager from service container
        db_manager = service_container.resolve("db")
        if db_manager:
            try:
                # Process the initial sync
                database_data = params.get('database', {})
                result = await db_manager.process_initial_sync(database_data)
                logger.info("Database initial sync processed by DatabaseManager")
                
                # Ensure we manually respond to the client (since the websocket broadcast might silently fail)
                websocket = context.get('websocket')
                message_id = context.get('message_id')
                if websocket and message_id:
                    try:
                        import json
                        response = {
                            "type": "response",
                            "id": message_id,
                            "result": result
                        }
                        await websocket.send(json.dumps(response))
                        logger.info("Manually sent database sync response to client")
                    except Exception as e:
                        logger.error(f"Failed to manually send response: {e}")
                
                return result
            except Exception as e:
                logger.error(f"Error processing initial sync: {e}")
                return error_response(f"Error processing initial sync: {str(e)}", ErrorCodes.SERVICE_ERROR)
        else:
            logger.warning("DatabaseManager not available in service container")
            return error_response("DatabaseManager not available", ErrorCodes.SERVICE_UNAVAILABLE)
    else:
        logger.warning("No service container available for database sync")
        return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)


async def handle_database_changes(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Handle database changes from the client.
    
    Args:
        params: The database changes
        context: The execution context
        
    Returns:
        Success response with change status
    """
    service_container = context.get('service_container')
    if service_container:
        # Get database manager from service container
        db_manager = service_container.resolve("db")
        if db_manager:
            # Process the database changes
            changes = params.get('changes', {})
            result = db_manager.process_changes(changes)
            logger.info("Database changes processed by DatabaseManager")
            return result
        else:
            logger.warning("DatabaseManager not available in service container")
            return error_response("DatabaseManager not available", ErrorCodes.SERVICE_UNAVAILABLE)
    else:
        logger.warning("No service container available for database changes")
        return error_response("Service container not available", ErrorCodes.SERVICE_UNAVAILABLE)


def register_handlers(command_handlers: 'CommandHandlers') -> None:
    """Register database handlers with the command handlers.
    
    Args:
        command_handlers: The CommandHandlers instance to register with
    """
    logger.debug("Registering database handlers")
    
    command_handlers.registry.register_function("database.initial_sync", handle_database_initial_sync)
    command_handlers.registry.register_function("database.changes", handle_database_changes)
    
    logger.info("Database handlers registered successfully") 