/**
 * Model Selector
 * Complete model selection component matching app-latest ModelStep design
 * Handles model grouping, selection, navigation, and progress tracking
 */

import React, { useCallback, useMemo } from 'react';
import { Card, CardContent } from '@src/components/ui/card';
import { ScrollArea } from '@src/components/ui/scroll-area';
import { Button } from '@src/components/ui/button';
import { Alert, AlertDescription } from '@src/components/ui/alert';
import { 
  HiCpuChip, 
  HiArrowPath, 
  HiExclamationCircle,
  HiXMark,
  HiCheckCircle,
  HiPlay,
  HiArrowDownTray
} from 'react-icons/hi2';
import { ModelCard } from './ModelCard';
import type { VoiceModel, DownloadProgress, LoadingProgress } from '../../types/model';

// ============================================================================
// TYPES
// ============================================================================

interface ModelSelectorProps {
  models: VoiceModel[];
  selectedModel: VoiceModel | null;
  onModelSelect: (model: VoiceModel) => void;
  downloadProgress?: DownloadProgress | null;
  loadingProgress?: LoadingProgress | null;
  loading?: boolean;
  refreshing?: boolean;
  error?: string | null;

  // Model operations
  onDownload?: (model: VoiceModel) => Promise<void>;
  onLoad?: (model: VoiceModel) => Promise<void>;
  onCancel?: () => Promise<void>;
  onRefresh?: () => Promise<void>;

  // Navigation props
  onNext?: () => void;
  onPrevious?: () => void;

  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function ModelSelector({
  models,
  selectedModel,
  onModelSelect,
  downloadProgress,
  loadingProgress,
  loading = false,
  refreshing = false,
  error = null,
  onDownload,
  onLoad,
  onCancel,
  onRefresh,
  onNext,
  onPrevious,
  className
}: ModelSelectorProps) {
  // Group models by language support (memoized)
  const { multilingualModels, englishOnlyModels } = useMemo(() => ({
    multilingualModels: models.filter(model => model.isMultilingual),
    englishOnlyModels: models.filter(model => !model.isMultilingual)
  }), [models]);

  // Render model card (memoized)
  const renderModelCard = useCallback((model: VoiceModel) => {
    return (
      <ModelCard
        key={model.id}
        model={model}
        isSelected={selectedModel?.id === model.id}
        onSelect={onModelSelect}
        downloadProgress={downloadProgress}
        loadingProgress={loadingProgress}
      />
    );
  }, [selectedModel, onModelSelect, downloadProgress, loadingProgress]);

  // Determine button states (memoized)
  const buttonStates = useMemo(() => {
    if (!selectedModel) {
      return { 
        continueDisabled: true, 
        continueText: 'Select a Model', 
        showCancel: false,
        showRefresh: true
      };
    }
    
    const isCurrentlyDownloading = downloadProgress?.modelId === selectedModel.id;
    const isCurrentlyLoading = loadingProgress?.modelId === selectedModel.id;
    
    if (isCurrentlyDownloading) {
      return { 
        continueDisabled: true, 
        continueText: 'Downloading...', 
        showCancel: true,
        showRefresh: false
      };
    }
    
    if (isCurrentlyLoading) {
      return { 
        continueDisabled: true, 
        continueText: 'Loading...', 
        showCancel: false,
        showRefresh: false
      };
    }
    
    // Check if model is already loaded
    if (selectedModel.isLoaded) {
      return { 
        continueDisabled: false, 
        continueText: 'Continue', 
        showCancel: false,
        showRefresh: true
      };
    }
    
    if (!selectedModel.isDownloaded) {
      return { 
        continueDisabled: false, 
        continueText: 'Download', 
        showCancel: false,
        showRefresh: true
      };
    }
    
    if (!selectedModel.isLoaded) {
      return { 
        continueDisabled: false, 
        continueText: 'Load & Continue', 
        showCancel: false,
        showRefresh: true
      };
    }
    
    return { 
      continueDisabled: false, 
      continueText: 'Continue', 
      showCancel: false,
      showRefresh: true
    };
  }, [selectedModel, downloadProgress, loadingProgress]);

  // Handle continue action
  const handleContinue = async () => {
    if (!selectedModel) return;

    // If model is already loaded, proceed to next step
    if (selectedModel.isLoaded) {
      console.log(`Model ${selectedModel.id} is already loaded, proceeding to next step`);
      onNext?.();
      return;
    }

    // If model is not downloaded, download it first
    if (!selectedModel.isDownloaded && onDownload) {
      await onDownload(selectedModel);
      return;
    }

    // If downloaded but not loaded, load it (user-initiated)
    if (!selectedModel.isLoaded && onLoad) {
      await onLoad(selectedModel);
      return;
    }

    // Model is ready, proceed to next step
    onNext?.();
  };

  return (
    <Card className={`bg-card border-border ${className || ''}`}>
      <CardContent className="space-y-6">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="animate-in slide-in-from-top-2 duration-300">
            <HiExclamationCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Loading State - only show during initial load, not during refresh */}
        {loading && !refreshing ? (
          <div className="text-center py-12">
            <HiArrowPath className="w-8 h-8 animate-spin mx-auto mb-4 text-primary" />
            <p className="text-muted-foreground">Loading available models...</p>
          </div>
        ) : models.length > 0 ? (
          <ScrollArea className="h-96 pr-4">
            <div className="space-y-6">
              {/* Multilingual Models */}
              {multilingualModels.length > 0 && (
                <div className="duration-500">
                  <div className="flex items-center gap-2 mb-3">
                    <HiCpuChip className="w-4 h-4 text-muted-foreground" />
                    <h4 className="text-sm font-medium text-muted-foreground">
                      Multilingual Models
                    </h4>
                  </div>
                  <div className="space-y-3">
                    {multilingualModels.map(renderModelCard)}
                  </div>
                </div>
              )}

              {/* English-only Models */}
              {englishOnlyModels.length > 0 && (
                <div className="duration-500">
                  <div className="flex items-center gap-2 mb-3">
                    <h4 className="text-sm font-medium text-muted-foreground">
                      English-only Models
                    </h4>
                  </div>
                  <div className="space-y-3">
                    {englishOnlyModels.map(renderModelCard)}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        ) : (
          <div className="text-center py-12">
            <HiExclamationCircle className="w-8 h-8 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground mb-4">No models available</p>
            <Button
              onClick={onRefresh}
              variant="outline"
              size="sm"
              className="gap-2"
              disabled={loading}
            >
              <HiArrowPath className="w-4 h-4" />
              Retry
            </Button>
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between pt-4">
          <Button 
            onClick={onPrevious} 
            variant="outline" 
            disabled={!onPrevious || downloadProgress !== null || loadingProgress !== null}
            className="transition-all duration-200"
          >
            Previous
          </Button>
          
          <div className="flex gap-3">
            {/* Refresh button - only show when appropriate */}
            {buttonStates.showRefresh && (
              <Button
                onClick={onRefresh}
                variant="ghost"
                disabled={loading}
                className="gap-2 transition-all duration-200"
              >
                <HiArrowPath className="w-4 h-4" />
                Refresh
              </Button>
            )}
            
            {/* Cancel download button - appears in place of continue when downloading */}
            {buttonStates.showCancel && (
              <Button 
                onClick={onCancel}
                variant="destructive" 
                className="gap-2 transition-all duration-200"
              >
                <HiXMark className="w-4 h-4" />
                Cancel Download
              </Button>
            )}
            
            {/* Continue/Download/Load button */}
            {!buttonStates.showCancel && (
              <Button 
                onClick={handleContinue}
                disabled={buttonStates.continueDisabled}
                className="gap-2 transition-all duration-200"
              >
                {loadingProgress?.modelId === selectedModel?.id ? (
                  <HiPlay className="w-4 h-4 animate-pulse" />
                ) : downloadProgress?.modelId === selectedModel?.id ? (
                  <HiArrowPath className="w-4 h-4 animate-spin" />
                ) : selectedModel?.isDownloaded ? (
                  selectedModel.isLoaded ? (
                    <HiCheckCircle className="w-4 h-4" />
                  ) : (
                    <HiPlay className="w-4 h-4" />
                  )
                ) : (
                  <HiArrowDownTray className="w-4 h-4" />
                )}
                {buttonStates.continueText}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default ModelSelector; 