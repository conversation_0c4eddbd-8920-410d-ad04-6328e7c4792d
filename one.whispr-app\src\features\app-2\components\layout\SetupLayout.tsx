/**
 * Setup Layout
 * Full-height centered layout matching app-latest design
 * Provides the main container structure with floating progress bar
 */

import React from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface SetupLayoutProps {
  children: React.ReactNode;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function SetupLayout({ children, className }: SetupLayoutProps) {
  return (
    <div className={`h-screen w-full flex flex-col bg-background relative ${className || ''}`}>
      {/* Step Content - Full height for perfect centering */}
      <div className="h-full bg-sidebar">
        <div className="h-[calc(100%-72px)] bg-background border-7 border-sidebar rounded-3xl overflow-hidden mt-10">
          {children}
        </div>
      </div>
    </div>
  );
}

export default SetupLayout; 