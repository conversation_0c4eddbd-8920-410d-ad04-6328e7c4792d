"""
Audio Service

Clean service wrapper around audio helpers with configuration management.
Provides high-level audio operations and device management.
"""

import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable
import json

from ..core.base import ConfigurableService, Event
from ..helpers.audio.device_manager import AudioDeviceManager
from ..helpers.audio.realtime_level_monitor import RealtimeLevelMonitor
from ..helpers.audio.audio_manager import AudioManager, AudioConsumerType, AudioChunk
from ..helpers.audio.wav_recorder import WavRecorder
from ..helpers.audio.silero_vad import SileroVAD
from ..config.manager import ConfigurationMixin
from ..config.settings import AUDIO_SETTINGS

logger = logging.getLogger(__name__)


class AudioService(ConfigurableService, ConfigurationMixin):
    """
    Clean service layer for audio operations.

    Responsibilities:
    - Device configuration and persistence
    - Service lifecycle management
    - High-level audio operations
    - Integration with other services
    - Configuration validation
    """

    def __init__(self, service_container=None):
        """Initialize the audio service.

        Args:
            service_container: The service container for dependency resolution
        """
        super().__init__(service_container)
        
        # Core components (keep existing ones that work)
        self.device_manager = AudioDeviceManager()
        self.level_monitor = RealtimeLevelMonitor()
        
        # Audio management (will be initialized in initialize())
        self.audio_manager = None
        self.wav_recorder = None
        
        # VAD instances (pre-initialized during startup)
        self.mic_vad = None
        self.system_vad = None
        
        # State
        self.is_initialized = False
        self.is_capturing = False
        self.is_level_monitoring = False
        
        # Callbacks
        self.transcription_callback = None
        self.ui_level_callback = None

    def _get_static_defaults(self) -> Dict[str, Any]:
        """Get static default configuration for AudioService."""
        return AUDIO_SETTINGS.copy()

    async def initialize(self) -> bool:
        """Initialize the audio service and all components."""
        try:
            self.logger.info("Initializing AudioService...")

            # Load configuration first
            self._load_configuration()

            # Initialize device manager
            self.device_manager.refresh_devices()

            # Pre-initialize VAD instances during startup to avoid first-use delays
            self.logger.info("Pre-initializing VAD instances...")

            # Get VAD configuration
            mic_threshold = self.get_config('mic_vad_threshold', 0.5)
            system_threshold = self.get_config('system_vad_threshold', 0.4)
            vad_enabled = self.get_config('enable_vad_filtering', True)

            # Pass configuration to VAD instances
            self.mic_vad = SileroVAD(
                sample_rate=16000,
                base_threshold=mic_threshold,
                vad_enabled=vad_enabled,
                config=self.get_config()
            )
            self.system_vad = SileroVAD(
                sample_rate=16000,
                base_threshold=system_threshold,
                vad_enabled=vad_enabled,
                config=self.get_config()
            )
            
            mic_init = self.mic_vad.initialize()
            system_init = self.system_vad.initialize()
            
            if mic_init and system_init:
                self.logger.info("VAD instances pre-initialized successfully")
            else:
                self.logger.warning(f"VAD initialization: mic={'ok' if mic_init else 'failed'}, system={'ok' if system_init else 'failed'}")
            
            # Initialize audio manager with pre-initialized VAD instances
            self.audio_manager = AudioManager(
                buffer_seconds=60.0,
                mic_vad=self.mic_vad,
                system_vad=self.system_vad
            )
            self.wav_recorder = WavRecorder()
            
            # Setup audio consumers
            self._setup_audio_consumers()
            
            self.is_initialized = True
            self.logger.info("AudioService initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize AudioService: {e}")
            return False
    
    async def cleanup(self) -> bool:
        """Cleanup audio service and release resources."""
        try:
            self.logger.info("Cleaning up AudioService...")
            
            # Stop all operations
            recording_result = self.stop_capture()
            if recording_result:
                self.logger.info(f"Stopped recording during cleanup: {recording_result.get('session_id')}")
            self.stop_level_monitoring()
            
            # Cleanup AudioManager
            if self.audio_manager:
                self.audio_manager.stop_capture()
            
            # Cleanup pre-initialized VAD instances
            if self.mic_vad:
                self.mic_vad.cleanup()
            if self.system_vad:
                self.system_vad.cleanup()
            
            # Cleanup level monitor
            self.level_monitor.stop_monitoring()
            
            # Cleanup WAV recorder
            if self.wav_recorder:
                self.wav_recorder.cleanup()
            
            self.is_initialized = False
            self.logger.info("AudioService cleanup completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup AudioService: {e}")
            return False
    
    # Device Management
    def get_devices(self) -> List[Dict[str, Any]]:
        """Get all available audio devices."""
        try:
            devices = self.device_manager.get_devices()
            return devices
        except Exception as e:
            self.logger.error(f"Failed to get audio devices: {e}")
            return []
    
    def refresh_devices(self, force: bool = False) -> List[Dict[str, Any]]:
        """Refresh device list and return updated devices."""
        try:
            device_info = self.device_manager.refresh_devices(force=force)
            return device_info.get("input", []) + device_info.get("output", [])
        except Exception as e:
            self.logger.error(f"Failed to refresh audio devices: {e}")
            return []
    
    def test_device(self, device_id: str, device_type: str = 'input', duration: float = 1.0) -> Dict[str, Any]:
        """Test an audio device."""
        try:
            result = self.device_manager.test_device(device_id, device_type, duration)
            return result
        except Exception as e:
            self.logger.error(f"Failed to test device {device_id}: {e}")
            return {"success": False, "error": str(e)}
    
    def get_selected_devices(self) -> Dict[str, Any]:
        """Get currently selected devices (always fresh from ConfigurationService)."""
        try:
            # Always pull fresh settings
            settings = self.get_settings()
            
            return {
                "input": {
                    "device_id": settings.get("selectedInputDevice"),
                    "use_default": settings.get("useDefaultInputDevice", True)
                },
                "output": {
                    "device_id": settings.get("selectedOutputDevice"), 
                    "use_default": settings.get("useDefaultOutputDevice", True)
                }
            }
        except Exception as e:
            self.logger.error(f"Error getting selected devices: {e}")
            return {
                "input": {"device_id": None, "use_default": True},
                "output": {"device_id": None, "use_default": True}
            }
    
    # Audio Capture & Recording
    def start_capture(self, session_id: Optional[str] = None) -> bool:
        """Start audio capture.
        
        System audio inclusion is automatically determined from the active mode's listenToSpeaker setting.
        If session_id is provided, also starts WAV recording to file.
        
        Args:
            session_id: Optional session ID for recording. If provided, starts recording to file.
            
        Returns:
            True if capture started successfully
        """
        try:
            if self.is_capturing:
                self.logger.warning("Audio capture already started")
                return True
                
            if not self.is_initialized:
                self.logger.error("AudioService not initialized")
                return False
            
            # Always get system audio setting from active mode's listenToSpeaker configuration
            include_system_audio = False
            active_mode = self.get_active_mode()
            if active_mode:
                mode_config = active_mode.get("configuration", {})
                include_system_audio = mode_config.get("listenToSpeaker", False)
            
            self.logger.info(f"Starting audio capture: system_audio={include_system_audio}, recording={session_id is not None}")
            
            # Start WAV recording if session_id provided
            if session_id:
                recording_path = self._get_recording_path(session_id)
                recording_started = self.wav_recorder.start_recording(session_id, recording_path)
                if not recording_started:
                    self.logger.error(f"Failed to start WAV recording for session: {session_id}")
                    return False
                self.logger.info(f"WAV recording started: {recording_path}")
            
            # Start audio capture
            success = self.audio_manager.start_capture(
                include_system_audio=include_system_audio,
                selected_devices=self.get_selected_devices()
            )
            
            if not success:
                self.logger.error("Failed to start audio capture")
                # Stop recording if it was started
                if session_id and self.wav_recorder.is_recording:
                    self.wav_recorder.cancel_recording()
                return False
            
            self.is_capturing = True
            self.logger.info("Audio capture started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start audio capture: {e}")
            # Stop recording if it was started
            if session_id and self.wav_recorder and self.wav_recorder.is_recording:
                self.wav_recorder.cancel_recording()
            return False
    
    def stop_capture(self) -> Optional[Dict[str, Any]]:
        """Stop audio capture and return recording metadata if recording was active."""
        try:
            if not self.is_capturing:
                self.logger.warning("Audio capture not started")
                return None
                
            self.logger.info("Stopping audio capture")
            
            # Stop audio capture
            if self.audio_manager:
                self.audio_manager.stop_capture()
            
            # Stop WAV recording and get metadata
            recording_metadata = None
            if self.wav_recorder and self.wav_recorder.is_recording:
                recording_metadata = self.wav_recorder.stop_recording()
            
            self.is_capturing = False
            self.logger.info("Audio capture stopped successfully")
            
            return recording_metadata
            
        except Exception as e:
            self.logger.error(f"Failed to stop audio capture: {e}")
            return None
    
    def get_recent_audio(self, duration_seconds: float = 5.0):
        """Get recent audio data for processing."""
        try:
            if self.audio_manager:
                return self.audio_manager.get_recent_audio(duration_seconds)
            return None
        except Exception as e:
            self.logger.error(f"Failed to get recent audio: {e}")
            return None
    
    # Level Monitoring
    def start_level_monitoring(self, callback: Optional[Callable] = None) -> bool:
        """Start real-time level monitoring."""
        try:
            if self.is_level_monitoring:
                self.logger.warning("Level monitoring already started")
                return True
                
            # Get selected input device
            selected_devices = self.get_selected_devices()
            input_device_info = selected_devices.get("input", {})
            
            # Determine device ID to use
            device_id = None
            if not input_device_info.get("use_default", True):
                device_id = input_device_info.get("device_id")
                if device_id:
                    try:
                        device_id = int(device_id)
                    except (ValueError, TypeError):
                        self.logger.warning(f"Invalid device ID: {device_id}, using default")
                        device_id = None
            
            # Set level callback if provided
            if callback:
                self.level_monitor.set_level_callback(callback)
            
            # Configure and start monitoring
            self.level_monitor.set_devices(input_device_id=device_id)
            success = self.level_monitor.start_monitoring(monitor_mic=True)
            
            if success:
                self.is_level_monitoring = True
                self.logger.info("Level monitoring started successfully")
            else:
                self.logger.error("Failed to start level monitoring")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to start level monitoring: {e}")
            return False
    
    def stop_level_monitoring(self) -> bool:
        """Stop level monitoring."""
        try:
            if not self.is_level_monitoring:
                self.logger.warning("Level monitoring not started")
                return True
                
            self.logger.info("Stopping level monitoring")
            self.level_monitor.stop_monitoring()
            self.is_level_monitoring = False
            self.logger.info("Level monitoring stopped successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop level monitoring: {e}")
            return False
    
    def get_audio_levels(self) -> Dict[str, float]:
        """Get current audio levels."""
        try:
            if self.is_level_monitoring:
                # Check if device has changed and hot-swap if needed
                self._check_and_update_level_monitor_device()
                return self.level_monitor.get_levels()
            else:
                return {"microphone": 0.0}
        except Exception as e:
            self.logger.error(f"Failed to get audio levels: {e}")
            return {"microphone": 0.0}
    
    def _check_and_update_level_monitor_device(self) -> None:
        """Check if the level monitor device needs to be updated and hot-swap if necessary."""
        try:
            if not self.is_level_monitoring:
                return
            
            # Get current device configuration
            selected_devices = self.get_selected_devices()
            input_device_info = selected_devices.get("input", {})
            
            # Determine what device ID should be used
            new_device_id = None
            if not input_device_info.get("use_default", True):
                device_id_str = input_device_info.get("device_id")
                if device_id_str:
                    try:
                        new_device_id = int(device_id_str)
                    except (ValueError, TypeError):
                        new_device_id = None
            
            # Get current device from level monitor
            current_device_id = self.level_monitor.get_current_device()
            
            # Check if device has changed
            if current_device_id != new_device_id:
                self.logger.info(f"Level monitor device changed: {current_device_id} → {new_device_id}")
                success = self.level_monitor.update_devices(input_device_id=new_device_id)
                if success:
                    self.logger.info("Level monitor device updated successfully")
                else:
                    self.logger.warning("Failed to update level monitor device")
                    
        except Exception as e:
            self.logger.error(f"Error checking level monitor device: {e}")
    
    # Callback Management
    def set_transcription_callback(self, callback: Callable) -> None:
        """Set callback for transcription audio chunks."""
        self.transcription_callback = callback
        
        # Re-setup consumers
        if self.is_initialized:
            self._setup_audio_consumers()
        
        self.logger.info("Transcription callback set")
    
    def set_ui_level_callback(self, callback: Callable) -> None:
        """Set callback for UI level updates."""
        self.ui_level_callback = callback
        
        # Re-setup consumers
        if self.is_initialized:
            self._setup_audio_consumers()
        
        self.logger.info("UI level callback set")

    # Status
    def get_status(self) -> Dict[str, Any]:
        """Get audio service status."""
        try:
            selected_devices = self.get_selected_devices()
            
            # Determine if system audio is enabled from active mode
            system_audio_enabled = False
            active_mode = self.get_active_mode()
            if active_mode:
                mode_config = active_mode.get("configuration", {})
                system_audio_enabled = mode_config.get("listenToSpeaker", False)
            
            status = {
                "initialized": self.is_initialized,
                "capturing": self.is_capturing,
                "level_monitoring": self.is_level_monitoring,
                "system_audio_enabled": system_audio_enabled,
                "selected_input_device": selected_devices.get("input", {}).get("device_id"),
                "selected_output_device": selected_devices.get("output", {}).get("device_id"),
                "device_count": len(self.get_devices())
            }
            
            # Add recording status if WAV recorder is available
            if self.wav_recorder:
                status["recording"] = self.wav_recorder.is_recording
                if self.wav_recorder.is_recording:
                    status["recording_session_id"] = self.wav_recorder.current_session_id
            
            # Add audio manager status if available
            if self.audio_manager:
                audio_status = self.audio_manager.get_status()
                status.update({
                    "audio_manager": audio_status
                })
            
            return status
            
        except Exception as e:
            self.logger.error(f"Failed to get status: {e}")
            return {
                "initialized": False,
                "capturing": False,
                "level_monitoring": False,
                "error": str(e)
            }
    
    # Helper Methods
    def _setup_audio_consumers(self):
        """Setup audio consumer callbacks in AudioManager."""
        if not self.audio_manager:
            return
        
        # Set up callbacks directly
        if self.transcription_callback:
            self.audio_manager.set_consumer_callback(AudioConsumerType.TRANSCRIPTION, self.transcription_callback)
        
        if self.ui_level_callback:
            self.audio_manager.set_consumer_callback(AudioConsumerType.UI_LEVELS, self.ui_level_callback)
        
        if self.wav_recorder:
            self.audio_manager.set_consumer_callback(AudioConsumerType.RECORDING, self.wav_recorder.write_chunk)
    
    def _get_recording_path(self, session_id: str) -> Path:
        """Get the recording file path for a session."""
        # Get recordings directory from settings or use default
        # Get the current file path and navigate to the project root
        current_file = Path(__file__)
        # Go up from python/whispr/services/audio_service.py to one.whispr-app
        project_root = current_file.parent.parent.parent.parent
        recordings_dir = project_root / ".dist" / "recordings"
        
        # Ensure the directory exists
        recordings_dir.mkdir(parents=True, exist_ok=True)

        return recordings_dir / f"{session_id}.wav"
    
    def get_audio_constants(self) -> Dict[str, Any]:
        """Get audio-related constants for the frontend."""
        return {
            "sample_rate": 16000,
            "chunk_duration": 0.1,  # 100ms chunks
            "supported_formats": ["wav"],
            "max_recording_duration": 3600  # 1 hour
        } 