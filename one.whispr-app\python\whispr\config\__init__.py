"""
Configuration management for One.Whispr.

This module provides configuration management capabilities including
loading, saving, and updating application settings.
"""

from .manager import ConfigurationManager
from .settings import (
    DEFAULT_CONFIG,
    APP_SETTINGS,
    WEBSOCKET_SETTINGS,
    AUDIO_SETTINGS,
    DATABASE_SETTINGS,
    LOGGING_SETTINGS
)

__all__ = [
    'ConfigurationManager',
    'DEFAULT_CONFIG',
    'APP_SETTINGS',
    'WEBSOCKET_SETTINGS',
    'AUDIO_SETTINGS',
    'DATABASE_SETTINGS',
    'LOGGING_SETTINGS'
]
