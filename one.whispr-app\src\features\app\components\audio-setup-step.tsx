import React from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@src/components/ui/card';
import { Button } from '@src/components/ui/button';
import { Label } from '@src/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@src/components/ui/select';
import { useSetup, useAudio } from '../context';
import { RiMicLine, RiVolumeUpLine, RiInformationLine, RiRefreshLine, RiRecordCircleLine, RiPulseLine } from 'react-icons/ri';
import { useState, useEffect, useCallback } from 'react';

export function AudioSetupStep(): React.ReactElement {
  const { 
    goToNextStep, 
    goToPreviousStep
  } = useSetup();
  
  const {
    audioLevel,
    startListening,
    stopListening,
    recordAudioTest,
    stopAudioTest,
    setInputDevice,
    setOutputDevice,
    audioInputDevice,
    audioOutputDevice,
    availableInputDevices,
    availableOutputDevices,
    isListening,
    isRecording,
    transcribedText,
    refreshDevices
  } = useAudio();
  
  // Initialize selectedDevices from context
  const [selectedInputDevice, setSelectedInputDevice] = useState(audioInputDevice);
  const [selectedOutputDevice, setSelectedOutputDevice] = useState(audioOutputDevice);
  
  // Memoize the startListening and stopListening functions
  const startListeningCallback = useCallback(() => {
    startListening();
  }, [startListening]);
  
  const stopListeningCallback = useCallback(() => {
    stopListening();
  }, [stopListening]);
  
  // Refresh devices on mount
  useEffect(() => {
    refreshDevices();
  }, [refreshDevices]);
  
  // Start listening automatically when the component mounts
  useEffect(() => {
    startListeningCallback();
    return () => {
      stopListeningCallback();
    };
  }, [startListeningCallback, stopListeningCallback]);
  
  // Keep local state in sync with context
  useEffect(() => {
    setSelectedInputDevice(audioInputDevice);
  }, [audioInputDevice]);
  
  useEffect(() => {
    setSelectedOutputDevice(audioOutputDevice);
  }, [audioOutputDevice]);
  
  // Handler for select changes
  const handleInputDeviceChange = useCallback((value: string) => {
    if (value !== selectedInputDevice) {
      setSelectedInputDevice(value);
      setInputDevice(value);
    }
  }, [selectedInputDevice, setInputDevice]);
  
  const handleOutputDeviceChange = useCallback((value: string) => {
    if (value !== selectedOutputDevice) {
      setSelectedOutputDevice(value);
      setOutputDevice(value);
    }
  }, [selectedOutputDevice, setOutputDevice]);
  
  // Handler for recording test
  const handleRecordTest = useCallback(() => {
    if (isRecording) {
      stopAudioTest();
    } else {
      recordAudioTest();
    }
  }, [isRecording, recordAudioTest, stopAudioTest]);
  
  // Handler for refreshing devices
  const handleRefreshDevices = useCallback(() => {
    refreshDevices();
  }, [refreshDevices]);
  
  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center space-y-1">
        <CardTitle className="text-2xl">Audio Setup</CardTitle>
        <CardDescription>
          Configure your microphone and speaker settings
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="space-y-4">
          {/* Microphone Input Device */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="microphone" className="flex items-center gap-1.5 text-sm">
                <RiMicLine size={16} /> Microphone Device
                {isListening && <RiPulseLine size={16} className="text-green-500 animate-pulse ml-1" title="Listening" />}
              </Label>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-7 w-7 p-0 rounded-full" 
                onClick={handleRefreshDevices}
                title="Refresh devices"
              >
                <RiRefreshLine size={14} />
              </Button>
            </div>
            <Select value={selectedInputDevice} onValueChange={handleInputDeviceChange}>
              <SelectTrigger id="microphone" className="w-full">
                <SelectValue placeholder="Select microphone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Default Microphone</SelectItem>
                {availableInputDevices.map(device => (
                  <SelectItem key={device.id} value={device.id}>
                    {device.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          {/* Speaker Output Device */}
          <div className="space-y-2">
            <Label htmlFor="speaker" className="flex items-center gap-1.5 text-sm">
              <RiVolumeUpLine size={16} /> Speaker Device
            </Label>
            <Select value={selectedOutputDevice} onValueChange={handleOutputDeviceChange}>
              <SelectTrigger id="speaker" className="w-full">
                <SelectValue placeholder="Select speaker" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="default">Default Speaker</SelectItem>
                {availableOutputDevices.map(device => (
                  <SelectItem key={device.id} value={device.id}>
                    {device.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="space-y-2">
            <Label className="flex items-center gap-2 text-sm">
              <RiVolumeUpLine size={16} /> Audio Level
            </Label>
            <div className="border p-3.5">
              <div className="h-8 flex items-stretch gap-[2.5px] px-0.5">
                {Array.from({ length: 20 }).map((_, index) => {
                  const threshold = (index + 1) * (100 / 20);
                  const isActive = audioLevel >= threshold;
                  
                  return (
                    <div 
                      key={index}
                      className={`flex-1 rounded-md transition-all duration-75 bg-primary ${
                        isActive ? 'opacity-100' : 'opacity-10'
                      }`}
                    />
                  );
                })}
              </div>
              <div className="flex justify-between text-xs text-muted-foreground mt-2.5 px-0.5">
                <span>Low</span>
                <span>Medium</span>
                <span>High</span>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col space-y-2">
            <Button 
              variant={isRecording ? "destructive" : "secondary"} 
              onClick={handleRecordTest}
              className="w-full"
            >
              <RiRecordCircleLine className="mr-2" size={16} />
              {isRecording ? "Stop Recording" : "Record Test"}
            </Button>
            
            {transcribedText && (
              <div className="p-2 border rounded-md bg-muted/30 text-sm">
                {transcribedText}
              </div>
            )}
          </div>
          
          <div className="bg-muted/50 rounded-md p-4 mt-4 flex items-start gap-2">
            <RiInformationLine size={18} className="text-blue-500 flex-shrink-0 mt-0.5" />
            <p className="text-sm text-muted-foreground">
              Speak into your microphone to check the audio levels. The meter should respond to your voice.
              Make sure your normal speaking voice registers in the middle range for best results.
              Record a test to verify your microphone is working correctly.
            </p>
          </div>
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={goToPreviousStep}>
          Back
        </Button>
        <Button onClick={goToNextStep}>
          Continue
        </Button>
      </CardFooter>
    </Card>
  );
} 