import { BaseRepository } from '../../database/repositories/base';
import { SchemaWithMigrations } from '../../database/schemas/manager';
import { VocabularyItem, DEFAULT_VOCABULARY_ITEMS } from './types';

/**
 * Repository for vocabulary items
 */
export class VocabularyRepository extends BaseRepository<VocabularyItem> {
  constructor() {
    super('vocabulary');
    this.initializeDefaults();
  }

  /**
   * Initialize with default vocabulary items if empty
   */
  private initializeDefaults(): void {
    try {
      const existing = this.findAll();
      if (existing.length === 0) {
        // Create default vocabulary items
        DEFAULT_VOCABULARY_ITEMS.forEach(item => {
          this.save(item);
        });

        console.log('Initialized default vocabulary items');
      }
    } catch (error) {
      console.error('Error initializing default vocabulary items:', error);
    }
  }

  /**
   * Find vocabulary items by mode
   */
  findByMode(modeId: string): VocabularyItem[] {
    try {
      const allItems = this.findAll();
      return allItems.filter(item => 
        item.isActive && 
        (item.modes.length === 0 || item.modes.includes(modeId)) // Empty modes = applies to all
      );
    } catch (error) {
      console.error('Error finding vocabulary items by mode:', error);
      return [];
    }
  }

  /**
   * Search vocabulary items by word or pronunciation variations
   */
  searchByWord(searchTerm: string): VocabularyItem[] {
    try {
      const term = searchTerm.toLowerCase();
      const allItems = this.findAll();
      return allItems.filter(item => 
        item.isActive && 
        (item.word.toLowerCase().includes(term) ||
         item.pronunciation.some(pron => pron.phonetic.toLowerCase().includes(term)))
      );
    } catch (error) {
      console.error('Error searching vocabulary items:', error);
      return [];
    }
  }

  /**
   * Get active vocabulary items ordered by frequency
   */
  getActiveByFrequency(): VocabularyItem[] {
    try {
      return this.findAll()
        .filter(item => item.isActive)
        .sort((a, b) => (b.frequency || 0) - (a.frequency || 0));
    } catch (error) {
      console.error('Error getting vocabulary items by frequency:', error);
      return [];
    }
  }

  /**
   * Update word usage frequency
   */
  updateUsage(id: string): VocabularyItem | null {
    try {
      const item = this.findById(id);
      if (!item) return null;

      return this.update(id, {
        frequency: (item.frequency || 0) + 1,
        lastUsed: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error updating vocabulary usage:', error);
      return null;
    }
  }

  /**
   * Get vocabulary statistics
   */
  getStatistics(): {
    total: number;
    active: number;
    inactive: number;
    byMode: Record<string, number>;
    totalFrequency: number;
  } {
    try {
      const allItems = this.findAll();
      const active = allItems.filter(item => item.isActive);
      const inactive = allItems.filter(item => !item.isActive);

      // Count by mode
      const byMode: Record<string, number> = {};
      active.forEach(item => {
        if (item.modes.length === 0) {
          byMode['all_modes'] = (byMode['all_modes'] || 0) + 1;
        } else {
          item.modes.forEach(modeId => {
            byMode[modeId] = (byMode[modeId] || 0) + 1;
          });
        }
      });

      // Calculate total frequency
      const totalFrequency = active.reduce((sum, item) => sum + (item.frequency || 0), 0);

      return {
        total: allItems.length,
        active: active.length,
        inactive: inactive.length,
        byMode,
        totalFrequency
      };
    } catch (error) {
      console.error('Error getting vocabulary statistics:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        byMode: {},
        totalFrequency: 0
      };
    }
  }

  /**
   * Ensure the table exists with proper schema
   */
  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS vocabulary (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Get schema definition for this repository
   */
  static getSchema(): SchemaWithMigrations {
    return {
      name: 'vocabulary',
      createStatement: `
        CREATE TABLE IF NOT EXISTS vocabulary (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `CREATE INDEX IF NOT EXISTS idx_vocabulary_id ON vocabulary(id);`,
          down: `DROP INDEX IF EXISTS idx_vocabulary_id;`
        }
      ]
    };
  }
} 