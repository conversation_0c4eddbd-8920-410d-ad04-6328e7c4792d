export interface AuthUrls {
  login: string;
  register: string;
}

export interface AuthData {
  token?: string;
  email?: string;
}

// Setup steps for the application
export type SetupStep = 'welcome' | 'download-model' | 'audio-setup' | 'shortcuts' | 'complete';

// Voice model type options
export type VoiceModelId = 
  | 'openai/whisper-tiny'
  | 'openai/whisper-base'
  | 'openai/whisper-base.en'
  | 'openai/whisper-small'
  | 'distil-whisper/distil-small.en'
  | 'distil-whisper/distil-medium.en'
  | 'distil-whisper/distil-large-v3';

// Voice model related types
export interface VoiceModel {
  id: VoiceModelId;
  name: string;
  size: string; // e.g., "1.2 GB"
  languages: 'English only' | 'Multilingual';
  isEnglishOnly: boolean;
  isDownloaded: boolean;
  downloadProgress?: number; // 0-100
  // For display purposes
  type: 'Standard' | 'Distilled';
  speed: 'Fast' | 'Medium' | 'Slow';
}

// Recording mode type
export type RecordingMode = 'toggle' | 'pushToTalk';

// Shortcut related types
export interface Shortcuts {
  pushToTalk: string[];
  toggle: string[];
  cancel: string[];
  modeSwitch: string[];
}

// Context interfaces for the separated contexts
export interface SetupContextProps {
  currentStep: SetupStep;
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  goToStep: (step: SetupStep) => void;
}

export interface AuthContextProps {
  isAuthenticated: boolean;
  loading: boolean;
  login: () => Promise<void>;
  register: () => Promise<void>;
  logout: () => void;
  bypassLogin: () => void; // Development option to bypass login
}

export interface ModelContextProps {
  availableModels: VoiceModel[];
  selectedModel: VoiceModel | null;
  isDownloading: boolean;
  downloadProgress: number;
  isCheckingStatus: boolean;
  currentDownloadingModel: string | null;
  loadingError: string | null;
  loadedModel: string | null;
  isRetrying: boolean;
  selectModel: (modelId: string) => void;
  downloadModel: () => Promise<void>;
  loadModel: (modelId?: string) => Promise<boolean>;
  refreshModelStatus: () => Promise<void>;
  forceModelStatusCheck: () => Promise<void>;
  isModelDownloading: (modelId: string) => boolean;
  getModelDownloadStatus: (modelId: string) => string;
  cancelDownload: () => Promise<void>;
  retryDownload: () => Promise<void>;
}

export interface ShortcutsContextProps {
  recordingMode: RecordingMode;
  shortcuts: Shortcuts;
  updateRecordingMode: (mode: RecordingMode) => void;
  updateShortcut: (action: keyof Shortcuts, keys: string[]) => void;
}

export interface AudioDevice {
  id: string;
  name: string;
  label: string;
  isDefault: boolean;
  type: 'input' | 'output';
}

export interface AudioContextProps {
  // Device information
  audioInputDevice: string;
  audioOutputDevice: string;
  availableInputDevices: AudioDevice[];
  availableOutputDevices: AudioDevice[];
  
  // Audio levels and state
  audioLevel: number;
  isListening: boolean;
  isRecording: boolean;
  transcribedText: string;
  
  // Functions
  setInputDevice: (deviceId: string) => Promise<void>;
  setOutputDevice: (deviceId: string) => Promise<void>;
  startListening: () => Promise<void>;
  stopListening: () => Promise<void>;
  recordAudioTest: () => Promise<void>;
  stopAudioTest: () => Promise<void>;
  refreshDevices: () => Promise<void>;
}