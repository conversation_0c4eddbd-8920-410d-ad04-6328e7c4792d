/**
 * TryIt Context
 * Basic context structure for future transcription testing functionality
 */

import React, { createContext, useContext, useState } from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface TryItState {
  isReady: boolean;
}

interface TryItContextValue {
  state: TryItState;
  // Future functionality will be added here
}

// ============================================================================
// CONTEXT
// ============================================================================

const TryItContext = createContext<TryItContextValue | null>(null);

// ============================================================================
// PROVIDER
// ============================================================================

interface TryItProviderProps {
  children: React.ReactNode;
}

export function TryItProvider({ children }: TryItProviderProps) {
  const [state] = useState<TryItState>({
    isReady: true,
  });

  const contextValue: TryItContextValue = {
    state,
  };

  return (
    <TryItContext.Provider value={contextValue}>
      {children}
    </TryItContext.Provider>
  );
}

// ============================================================================
// HOOK
// ============================================================================

export function useTryIt(): TryItContextValue {
  const context = useContext(TryItContext);
  if (!context) {
    throw new Error('useTryIt must be used within a TryItProvider');
  }
  return context;
} 