'use client';

import { useState, useEffect, useRef } from 'react';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { OTPInput } from '@/components/ui/otp';
import { EmailDisplay } from '../ui/EmailDisplay';
import { useAuthForm } from '../hooks/useAuthForm';
import { useAuthNavigation } from '../hooks/useAuthNavigation';
import { AuthMode } from '../utils/auth-utils';
import { verifyLoginOTP } from '../../login/actions';
import { verifyRegistrationOTP } from '../../register/actions';

// OTP validation schema
const otpSchema = z.object({
  otp: z.string().min(6, { message: 'Please enter a valid verification code' }),
});

type OtpFormValues = z.infer<typeof otpSchema>;

interface OtpFormProps {
  email: string;
  mode?: AuthMode;
  callbackUrl?: string | null;
  onVerify?: () => void;
}

/**
 * OTP verification form used across all auth flows
 */
export function OtpForm({ 
  email, 
  mode = 'login',
  callbackUrl,
  onVerify 
}: OtpFormProps) {
  const navigation = useAuthNavigation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const submittedRef = useRef(false);
  
  // Check if auto-submitted via URL parameter
  const searchParams = new URLSearchParams(
    typeof window !== 'undefined' ? window.location.search : ''
  );
  const isAutoSubmitted = searchParams.get('autoSubmitted') === 'true';
  const trigger = searchParams.get('trigger');
  
  // If this is auto-submitted from login, trigger password reset
  useEffect(() => {
    if (isAutoSubmitted && trigger === 'login' && mode === 'reset') {
      const triggerPasswordReset = async () => {
        try {
          // Import the action and trigger password reset
          const { requestPasswordReset } = await import('../../forgot-password/actions');
          
          const formData = new FormData();
          formData.append('email', email);
          
          await requestPasswordReset(formData);
        } catch (error) {
          console.error('Error triggering password reset:', error);
        }
      };
      
      triggerPasswordReset();
    }
  }, [isAutoSubmitted, trigger, email, mode]);
  
  // Setup form with our hook
  const { form, loading, submitHandler, setLoading } = useAuthForm<OtpFormValues>({
    schema: otpSchema,
    defaultValues: {
      otp: '',
    },
    onSubmit: handleOtpSubmit,
  });

  // Handle OTP verification
  async function handleOtpSubmit(data: OtpFormValues) {
    // Prevent multiple simultaneous submissions
    if (isSubmitting) return;
    
    // Set submission flags
    setIsSubmitting(true);
    submittedRef.current = true;
    
    // Create FormData for server action
    const formData = new FormData();
    formData.append('email', email);
    formData.append('otp', data.otp);
    
    try {
      // Handle different flows based on mode
      if (onVerify && mode === 'reset') {
        // For password reset, check the OTP validity first
        const { verifyResetOTP } = await import('../../forgot-password/actions');
        const result = await verifyResetOTP(formData);
        
        if (result.success) {
          // Use callback for custom handling
          onVerify();
        } else {
          // Handle error
          form.setError('otp', { 
            type: 'manual', 
            message: result.error || 'Invalid verification code' 
          });
          // Reset submission flags on error
          setIsSubmitting(false);
          submittedRef.current = false;
        }
      } else if (mode === 'reset') {
        // Standard reset flow without callback
        const { verifyResetOTP } = await import('../../forgot-password/actions');
        const result = await verifyResetOTP(formData);
        
        if (result.success) {
          navigation.navigateToPasswordReset('reset', { 
            email, 
            token: 'verified',
            callbackUrl
          });
        } else {
          // Handle error
          form.setError('otp', { 
            type: 'manual', 
            message: result.error || 'Invalid verification code' 
          });
          // Reset submission flags on error
          setIsSubmitting(false);
          submittedRef.current = false;
        }
      } else if (mode === 'login') {
        // Use login OTP verification server action
        const result = await verifyLoginOTP(formData);
        
        if (result.success) {
          // Supabase session is now managed by the server, just redirect
          navigation.handleAuthSuccess('session-active', email, 'login', callbackUrl);
        } else {
          // Handle error
          form.setError('otp', { 
            type: 'manual', 
            message: result.error || 'Invalid verification code' 
          });
          // Reset submission flags on error
          setIsSubmitting(false);
          submittedRef.current = false;
        }
      } else if (mode === 'register') {
        // Use registration OTP verification server action
        const result = await verifyRegistrationOTP(formData);
        
        if (result.success) {
          // Supabase session is now managed by the server, just redirect
          navigation.handleAuthSuccess('session-active', email, 'register', callbackUrl);
        } else {
          // Handle error
          form.setError('otp', { 
            type: 'manual', 
            message: result.error || 'Invalid verification code' 
          });
          // Reset submission flags on error
          setIsSubmitting(false);
          submittedRef.current = false;
        }
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      form.setError('otp', { 
        type: 'manual', 
        message: 'An unexpected error occurred' 
      });
      // Reset submission flags on error
      setIsSubmitting(false);
      submittedRef.current = false;
    }
  }

  // Watch for OTP completion and auto-submit
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      // Only auto-submit if not already submitting and OTP is complete
      if (name === 'otp' && value.otp?.length === 6 && !submittedRef.current) {
        setLoading(true);
        submittedRef.current = true; // Prevent multiple submissions
        form.handleSubmit(handleOtpSubmit)();
      }
    });
    return () => subscription.unsubscribe();
  }, [form, handleOtpSubmit, setLoading]);

  return (
    <div className="space-y-6">
      <EmailDisplay email={email} prefix="Enter the code sent to:" className="" />
      
      {isAutoSubmitted && (
        <div className="text-center text-sm text-primary mb-4">
          A verification code has been sent to your email.
        </div>
      )}
      
      <Form {...form}>
        <form onSubmit={submitHandler} className="space-y-4">
          <FormField
            control={form.control}
            name="otp"
            render={({ field }) => (
              <FormItem className="mx-auto">
                <FormControl>
                  <OTPInput
                    length={6}
                    value={field.value}
                    onChange={field.onChange}
                    autoFocus={true}
                    disabled={loading || isSubmitting}
                  />
                </FormControl>
                <FormMessage className="text-center mt-2" />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
} 