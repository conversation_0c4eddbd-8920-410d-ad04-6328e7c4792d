import { BaseEntity } from '../../database/repositories/base';
import { nanoid } from 'nanoid';

export interface PronunciationVariation {
  id?: string;
  phonetic: string;
  isPreferred?: boolean;
}

export interface VocabularyItem extends BaseEntity {
  id: string;
  word: string; // The main vocabulary word
  pronunciation: PronunciationVariation[]; // Array of all pronunciation/spelling variations
  modes: string[]; // Array of mode IDs where this vocabulary applies (empty = all modes)
  isActive: boolean; // Whether the vocabulary item is active
  order_index?: number; // Manual ordering for 50-word selection (lower = higher priority)
  frequency?: number; // Usage frequency for prioritization
  lastUsed?: string; // ISO timestamp of last usage
}

// Default vocabulary items - all variations in pronunciation array
export const DEFAULT_VOCABULARY_ITEMS: VocabularyItem[] = [
  {
    id: nanoid(),
    word: 'Ijaz',
    pronunciation: [
      { id: nanoid(), phonetic: 'i jazz', isPreferred: true },
      { id: nanoid(), phonetic: 'eye jazz' }
    ],
    modes: [], // Applies to all modes
    isActive: true,
    order_index: 1,
    frequency: 0
  },
  {
    id: nanoid(),
    word: 'One.Whispr',
    pronunciation: [
      { id: nanoid(), phonetic: 'one whisper', isPreferred: true },
      { id: nanoid(), phonetic: 'onewhisper' },
      { id: nanoid(), phonetic: 'one whispr' }
    ],
    modes: [], // Applies to all modes
    isActive: true,
    order_index: 2,
    frequency: 0
  }
]; 