import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { app } from 'electron';

// Database connection instance
let db: Database.Database | null = null;

/**
 * Get the path to the main database file
 */
export function getMainDatabasePath(): string {
  // Ensure user data directory exists
  const userDataPath = app.getPath('userData');
  if (!fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
  }
  
  // Ensure db directory exists
  const dbDir = path.join(userDataPath, 'db');
  if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
  }
  
  // Database stored in user data directory
  return path.join(userDataPath, 'db', 'whispr.db');
}

/**
 * Get the database connection
 * Will initialize if necessary
 */
export function getDatabase(): Database.Database {
  if (!db) {
    try {
      const dbPath = getMainDatabasePath();
      console.log(`Opening database at: ${dbPath}`);
      
      // Create database connection with WAL mode
      db = new Database(dbPath, {
        verbose: console.log
      });
      
      // Set pragmas for better performance
      db.pragma('journal_mode = WAL');
      db.pragma('synchronous = NORMAL');
      db.pragma('foreign_keys = ON');
    } catch (error) {
      console.error('Error opening database:', error);
      throw error;
    }
  }
  
  return db;
}

/**
 * Reset the database
 */
export function resetDatabase(): void {
  // Get database connection and reset
  const db = getDatabase();
  // Run a query to drop all tables
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").all();
  
  db.exec('PRAGMA foreign_keys = OFF;');
  for (const table of tables) {
    db.exec(`DROP TABLE IF EXISTS ${(table as {name: string}).name};`);
  }
  db.exec('PRAGMA foreign_keys = ON;');
}

/**
 * Close the database connection
 */
export function closeDatabase(): void {
  if (db) {
    try {
      db.close();
      db = null;
    } catch (error) {
      console.error('Error closing database:', error);
    }
  }
}

/**
 * Execute a function in a database transaction
 */
export function transaction<T>(fn: () => T): T {
  const db = getDatabase();
  const txFn = db.transaction(fn);
  return txFn();
}

// Backend database synchronization
import { sendRequest, isBackendConnected } from '../../../python/main';
import { dbEvents, DatabaseChangeEvent, DatabaseChangeType } from '../repositories/base';
import { getAllSchemas } from '../schemas/manager';

// Extended interface for database change events
interface ExtendedDatabaseChangeEvent extends DatabaseChangeEvent {
  tableName: string;
  entity: any;
}

// Flag to prevent recursive sync events
let isSyncingWithBackend = false;

/**
 * Initialize database synchronization with backend
 */
export function initializeBackendSync(): void {
  // Subscribe to database change events
  dbEvents.on('change', async (event: ExtendedDatabaseChangeEvent) => {
    if (isSyncingWithBackend) return; // Prevent recursive events
    
    try {
      isSyncingWithBackend = true;
      
      const { type, tableName, entity } = event;
      
      // Format the change as expected by the Python backend
      const changesByTable: Record<string, { 
        upserts: Array<{id: any, data: string}>,
        deletes: Array<any>
      }> = {};
      
      // Initialize the table entry
      changesByTable[tableName] = {
        upserts: [],
        deletes: []
      };
      
      if (type === DatabaseChangeType.DELETE) {
        changesByTable[tableName].deletes.push(entity.id);
      } else {
        // For INSERT and UPDATE
        const record = {
          id: entity.id,
          data: JSON.stringify(entity)
        };
        changesByTable[tableName].upserts.push(record);
      }
      
      // Check if backend is connected
      if (!isBackendConnected()) {
        console.warn(`Backend not connected, change will be included in next initial sync`);
        isSyncingWithBackend = false;
        return;
      }
      
      console.log(`Sending immediate database change for table: ${tableName}`);
      
      // Send the change immediately
      const result = await sendRequest('database.changes', {
        changes: changesByTable  // Send changes directly, not wrapped in args
      });
      
      if (!result.success) {
        console.error('Error syncing database change with backend:', result.error);
      } else {
        console.log(`Successfully synced database change for table: ${tableName}`);
      }
    } catch (error) {
      console.error('Error sending immediate database change to backend:', error);
    } finally {
      isSyncingWithBackend = false;
    }
  });
}

/**
 * Send the entire database to Python for initial sync
 */
export async function sendInitialSync(): Promise<void> {
  try {
    console.log('Preparing database for initial sync with backend');
    isSyncingWithBackend = true;
    
    // Check if backend is connected
    if (!isBackendConnected()) {
      console.warn('Backend not connected, skipping initial database sync');
      isSyncingWithBackend = false;
      return;
    }
    
    const db = getDatabase();
    const tables: Array<{name: string, records: Array<any>}> = [];
    
    // Get all registered schemas to know how to handle each table
    const schemas = getAllSchemas();
    
    // Process each registered schema/table
    for (const [tableName, schema] of Object.entries(schemas)) {
      try {
        // Check if table exists before querying
        const tableExists = db.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name=?
        `).get(tableName);
        
        if (!tableExists) {
          console.warn(`Table ${tableName} from schema ${schema.name} does not exist yet, skipping`);
          continue;
        }
        
        // Get all records from the table
        const rows = db.prepare(`SELECT * FROM ${tableName}`).all();
        
        // Process each row - all BaseRepository tables use JSON data field
        const records: Array<any> = [];
        for (const row of rows) {
          const id = (row as any).id;
          
          try {
            // Parse JSON data field (BaseRepository pattern)
            const data = JSON.parse((row as any).data);
            // Include the ID in the record data
            records.push({ id, ...data });
          } catch (error) {
            console.error(`Error parsing data for ${tableName}/${id}:`, error);
          }
        }
        
        // Add table to the tables array
        tables.push({
          name: tableName,
          records: records
        });
        
        console.log(`Processed ${records.length} records from ${tableName} (schema: ${schema.name})`);
      } catch (error) {
        console.error(`Error processing table ${tableName} from schema ${schema.name}:`, error);
        // Add empty table even if there's an error
        tables.push({
          name: tableName,
          records: []
        });
      }
    }
    
    // Also handle any tables that exist but aren't registered (for backward compatibility)
    const allTables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'").all();
    const processedTableNames = new Set(tables.map(t => t.name));
    
    for (const table of allTables) {
      const tableName = (table as {name: string}).name;
      
      // Skip internal tables and already processed tables
      if (tableName === '_schema_migrations' || processedTableNames.has(tableName)) continue;
      
      console.warn(`Found unregistered table: ${tableName}, processing with fallback method`);
      
      // Get all records from the table
      const rows = db.prepare(`SELECT * FROM ${tableName}`).all();
      
      // Process each row with fallback logic
      const records: Array<any> = [];
      for (const row of rows) {
        const id = (row as any).id;
        let data: any;
        
        try {
          // Try JSON data field first (BaseRepository pattern)
          if ((row as any).data && typeof (row as any).data === 'string') {
            data = JSON.parse((row as any).data);
            records.push({ id, ...data });
          } else {
            // Fallback: use the row as is
            const rowObj = row as Record<string, any>;
            records.push(rowObj);
          }
        } catch (error) {
          console.error(`Error parsing data for ${tableName}/${id}:`, error);
        }
      }
      
      // Add unregistered table to the tables array
      tables.push({
        name: tableName,
        records: records
      });
    }
    
    console.log(`Sending initial sync with ${tables.length} tables`);
    
    // Prepare the database structure that Python expects
    const database = {
      tables: tables,
      schemas: {} // TODO: Add schema information if needed
    };
    
    try {
      // Send the database to Python with extended timeout
      const result = await sendRequest('database.initial_sync', {
        database: database,  // Send database directly, not wrapped in args
        timeout: 60000 // 60 second timeout for initial sync
      });
      
      if (!result.success) {
        console.error('Error during initial database sync with Python:', result.error);
        throw new Error(result.error);
      }
      
      console.log('Initial database sync with Python completed successfully');
    } catch (error) {
      console.error('Error sending initial database sync to Python:', error);
      
      // Even if we got a timeout, continue anyway since the Python side
      // likely processed the database successfully but couldn't send a response
      console.log('[MAIN] Initial database sync completed');
    }
    
  } catch (error) {
    console.error('Error sending initial database sync to Python:', error);
    // We'll continue even if there's an error since the app should still work
    // without the initial sync
    console.log('[MAIN] Initial database sync completed with errors');
  } finally {
    isSyncingWithBackend = false;
  }
} 