# Standardized Response Utilities

This document explains the standardized response format and utilities for One.Whispr handlers.

## Overview

All handlers in One.Whispr should use a consistent response format to ensure predictable API behavior and easier debugging. The `response_utils.py` module provides standardized utilities for creating both success and error responses.

## Response Format

### Success Response

A successful response should have the following format:

```json
{
  "success": true,
  "error": null,
  "data": { /* Optional data payload */ },
  "message": "Optional success message"
}
```

### Error Response

An error response should have the following format:

```json
{
  "success": false,
  "error": "Error message describing what went wrong",
  "error_code": "ERROR_CODE",
  "data": null  // Or optional error-specific data
}
```

## Usage

### Importing Response Utilities

In your handler file, import the utilities:

```python
from whispr.core.response_utils import success_response, error_response, ErrorCodes
```

### Creating Success Responses

```python
# Simple success response
return success_response()

# Success with message
return success_response(message="Operation completed successfully")

# Success with data
return success_response({"id": 123, "name": "<PERSON><PERSON>"}, "<PERSON><PERSON> created successfully")
```

### Creating Error Responses

```python
# Simple error
return error_response("Something went wrong")

# Error with code
return error_response("Device not found", ErrorCodes.RESOURCE_NOT_FOUND)

# Error with data
return error_response(
    "Validation failed", 
    ErrorCodes.VALIDATION_ERROR,
    {"field_errors": {"name": "Name is required"}}
)
```

## Standard Error Codes

The `ErrorCodes` class provides standard error codes to use across the application:

- `UNKNOWN_ERROR`: Generic error without specific categorization
- `PARAMETER_ERROR`: Missing or invalid parameters
- `VALIDATION_ERROR`: Input validation failure
- `SERVICE_UNAVAILABLE`: A required service is not available
- `SERVICE_ERROR`: An error occurred in a service
- `RESOURCE_NOT_FOUND`: A requested resource doesn't exist
- `RESOURCE_EXISTS`: A resource already exists (for creation operations)
- `PERMISSION_DENIED`: The operation is not permitted
- `DEVICE_ERROR`: Error related to audio or input/output devices
- `DEVICE_NOT_FOUND`: A specific device was not found
- `CAPTURE_ERROR`: Error related to audio capture
- `CONFIGURATION_ERROR`: Error in configuration settings

## Best Practices

1. Always use these utilities instead of creating response dictionaries manually
2. Use specific error codes to help the frontend handle errors appropriately
3. Include useful error messages that explain what went wrong
4. Log errors on the server side before returning error responses
5. Include relevant data in success responses, but keep error responses minimal

## Example Handler

```python
async def handle_get_device(params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
    """Get a device by ID."""
    try:
        device_id = params.get("deviceId")
        if not device_id:
            return error_response("Device ID is required", ErrorCodes.PARAMETER_ERROR)
        
        device_service = _get_device_service(context)
        if not device_service:
            return error_response("Device service not available", ErrorCodes.SERVICE_UNAVAILABLE)
        
        device = device_service.get_device(device_id)
        if not device:
            return error_response(f"Device {device_id} not found", ErrorCodes.RESOURCE_NOT_FOUND)
        
        return success_response({"device": device}, "Device retrieved successfully")
        
    except Exception as e:
        logger.error(f"Failed to get device: {e}")
        return error_response(f"Failed to get device: {str(e)}", ErrorCodes.UNKNOWN_ERROR) 