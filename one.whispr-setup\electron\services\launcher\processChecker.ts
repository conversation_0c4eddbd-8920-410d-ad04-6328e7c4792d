/**
 * Service for checking and managing main app processes
 */
export class ProcessChecker {
  /**
   * Check if main app is already running by looking for the process with full path
   */
  public async isMainAppAlreadyRunning(): Promise<boolean> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      // Use wmic to get processes with full command line (includes path)
      const { stdout } = await execAsync('wmic process where "name=\'One Whispr.exe\'" get ProcessId,CommandLine /format:csv');
      const lines = stdout.split('\n').filter(line => line.includes('One Whispr.exe'));

      let foundMainApp = false;

      for (const line of lines) {
        // Parse CSV line format: Node,CommandLine,ProcessId
        const columns = line.split(',');
        if (columns.length >= 3) {
          const commandLine = columns[1]?.trim() || '';
          const pidStr = columns[2]?.trim() || '';
          const pid = parseInt(pidStr);

          if (isNaN(pid) || pid === process.pid) continue;

          // Check if this process is running from main app location
          // Microsoft Store: LocalState\MainApp\
          // Normal build: \MainApp\ (in AppData\Roaming\OneWhispr\MainApp)
          if (commandLine.includes('\\MainApp\\') && !commandLine.includes('WindowsApps')) {
            foundMainApp = true;
            console.log('[PROCESS_CHECKER] Found main app process:', pid, 'from MainApp directory');
          } else if (commandLine.includes('WindowsApps')) {
            console.log('[PROCESS_CHECKER] Found setup app process:', pid, 'from WindowsApps');
          } else {
            console.log('[PROCESS_CHECKER] Found One Whispr process at unknown location:', pid, 'path:', commandLine);
          }
        }
      }

      if (foundMainApp) {
        console.log('[PROCESS_CHECKER] Main app is already running from the correct directory');
      } else {
        console.log('[PROCESS_CHECKER] No main app processes found, only setup app is running');
      }

      return foundMainApp;
    } catch (error) {
      console.warn('[PROCESS_CHECKER] Error checking if main app process is running:', error);
      return false;
    }
  }

  /**
   * Get all running "One Whispr.exe" processes with details
   */
  public async getRunningProcesses(): Promise<Array<{ pid: number; isMainApp: boolean; isSetupApp: boolean; path: string }>> {
    try {
      const { exec } = await import('child_process');
      const { promisify } = await import('util');
      const execAsync = promisify(exec);

      const { stdout } = await execAsync('wmic process where "name=\'One Whispr.exe\'" get ProcessId,CommandLine /format:csv');
      const lines = stdout.split('\n').filter(line => line.includes('One Whispr.exe'));

      const processes = [];

      for (const line of lines) {
        const columns = line.split(',');
        if (columns.length >= 3) {
          const commandLine = columns[1]?.trim() || '';
          const pidStr = columns[2]?.trim() || '';
          const pid = parseInt(pidStr);

          if (isNaN(pid)) continue;

          const isSetupApp = pid === process.pid || commandLine.includes('WindowsApps');
          const isMainApp = !isSetupApp && commandLine.includes('\\MainApp\\');

          processes.push({
            pid,
            isMainApp,
            isSetupApp,
            path: commandLine
          });
        }
      }

      return processes;
    } catch (error) {
      console.warn('[PROCESS_CHECKER] Error getting running processes:', error);
      return [];
    }
  }
}
