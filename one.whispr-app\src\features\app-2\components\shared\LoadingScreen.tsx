/**
 * Loading Screen
 * Full-screen loading state component matching app-latest design
 * Used for initial loading and major state changes
 */

import { LoadingSpinner } from './LoadingSpinner';
import { cn } from '@src/lib/utils';

// ============================================================================
// TYPES
// ============================================================================

interface LoadingScreenProps {
  message?: string;
  className?: string;
  size?: 'md' | 'lg' | 'xl';
}

// ============================================================================
// COMPONENT
// ============================================================================

export function LoadingScreen({
  message = 'Loading...',
  className,
  size = 'lg'
}: LoadingScreenProps) {
  return (
    <div className={cn(
      'flex items-center justify-center min-h-[400px] px-6',
      className
    )}>
      <div className="text-center space-y-4">
        <LoadingSpinner size={size} color="primary" />
        {message && (
          <p className="text-muted-foreground">
            {message}
          </p>
        )}
      </div>
    </div>
  );
}

export default LoadingScreen; 