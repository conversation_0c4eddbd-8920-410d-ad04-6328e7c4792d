"""
Audio Manager

Simple manager that coordinates audio capture and consumer dispatch.
AudioCapture handles microphone + system audio mixing and 100ms chunking.
AudioManager dispatches chunks directly to consumers (TRANSCRIPTION, PYANNOTE, UI_LEVELS, RECORDING).
"""

import logging
import time
import threading
from enum import Enum
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass
import numpy as np

# Import components to avoid circular imports being needed later
from .audio_capture import AudioCapture

logger = logging.getLogger(__name__)


class AudioConsumerType(Enum):
    """Types of audio consumers."""
    TRANSCRIPTION = "transcription"
    UI_LEVELS = "ui_levels"
    RECORDING = "recording"


@dataclass
class AudioChunk:
    """Audio chunk data structure."""
    data: np.ndarray
    timestamp: float
    sample_rate: int
    duration: float
    consumer_type: AudioConsumerType
    chunk_id: int
    channels: int = 1


class AudioManager:
    """Simple audio manager that coordinates capture and consumer dispatch."""
    
    def __init__(self, buffer_seconds: float = 60.0, mic_vad=None, system_vad=None):
        """Initialize the audio manager.
        
        Args:
            buffer_seconds: Size of audio buffer for recent audio retrieval
            mic_vad: Pre-initialized VAD instance for microphone (optional)
            system_vad: Pre-initialized VAD instance for system audio (optional)
        """
        self.buffer_seconds = buffer_seconds
        
        # Store VAD instances for passing to AudioCapture
        self.mic_vad = mic_vad
        self.system_vad = system_vad
        
        # Core components (will be created when needed)
        self.audio_capture = None
        
        # State
        self.is_capturing = False
        self.current_config = {}
        
        # Consumer callbacks
        self.consumer_callbacks = {}
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Audio buffer for recent audio retrieval
        self.audio_buffer = []
        self.buffer_max_samples = int(16000 * buffer_seconds)  # Assuming 16kHz
        
        # Chunk counter for metadata
        self.chunk_counter = 0
        
        logger.debug(f"AudioManager initialized with {buffer_seconds}s buffer")
    
    def start_capture(self, include_system_audio: bool = False, selected_devices: Optional[Dict] = None) -> bool:
        """Start audio capture.
        
        Args:
            include_system_audio: Whether to capture system audio
            selected_devices: Selected audio devices configuration
            
        Returns:
            True if capture started successfully
        """
        with self.lock:
            try:
                if self.is_capturing:
                    logger.warning("Audio capture already running")
                    return True
                
                # Create audio capture with pre-initialized VAD instances
                self.audio_capture = AudioCapture(
                    mic_vad=self.mic_vad,
                    system_vad=self.system_vad
                )
                
                # Set up capture callback for direct consumer dispatch
                self.audio_capture.set_chunk_callback(self._handle_audio_chunk)
                
                # Reset chunk counter
                self.chunk_counter = 0
                
                # Start capture
                success = self.audio_capture.start_capture(
                    enable_system_audio=include_system_audio,
                    selected_devices=selected_devices
                )
                
                if success:
                    self.is_capturing = True
                    self.current_config = {
                        "include_system_audio": include_system_audio,
                        "selected_devices": selected_devices
                    }
                    logger.info("Audio capture started successfully")
                else:
                    logger.error("Failed to start audio capture")
                
                return success
                
            except Exception as e:
                logger.error(f"Failed to start audio capture: {e}")
                return False
    
    def stop_capture(self) -> bool:
        """Stop audio capture.
        
        Returns:
            True if capture stopped successfully
        """
        with self.lock:
            try:
                if not self.is_capturing:
                    logger.warning("Audio capture not running")
                    return True
                
                # Stop audio capture
                if self.audio_capture:
                    self.audio_capture.stop_capture()
                    self.audio_capture = None
                
                self.is_capturing = False
                self.current_config = {}
                
                logger.info("Audio capture stopped successfully")
                return True
                
            except Exception as e:
                logger.error(f"Failed to stop audio capture: {e}")
                return False
    
    def set_consumer_callback(self, consumer_type: AudioConsumerType, callback: Callable) -> None:
        """Set callback for a specific consumer type.
        
        Args:
            consumer_type: The type of consumer
            callback: Callback function to receive audio chunks
        """
        with self.lock:
            self.consumer_callbacks[consumer_type] = callback
            logger.info(f"Set callback for consumer: {consumer_type.value}")
    

    def get_recent_audio(self, duration_seconds: float) -> Optional[np.ndarray]:
        """Get recent audio data.
        
        Args:
            duration_seconds: Duration of audio to retrieve in seconds
            
        Returns:
            Recent audio data as numpy array or None if not available
        """
        with self.lock:
            try:
                if not self.audio_buffer:
                    return None
                
                # Calculate required samples
                required_samples = int(16000 * duration_seconds)
                
                # Get recent samples from buffer
                if len(self.audio_buffer) >= required_samples:
                    recent_audio = np.array(self.audio_buffer[-required_samples:])
                    return recent_audio
                else:
                    # Return all available audio
                    return np.array(self.audio_buffer)
                
            except Exception as e:
                logger.error(f"Failed to get recent audio: {e}")
                return None
    
    def get_status(self) -> Dict[str, Any]:
        """Get audio manager status.
        
        Returns:
            Dictionary with current status information
        """
        with self.lock:
            status = {
                "is_capturing": self.is_capturing,
                "buffer_size_seconds": self.buffer_seconds,
                "buffer_current_samples": len(self.audio_buffer),
                "consumer_count": len(self.consumer_callbacks),
                "current_config": self.current_config.copy()
            }
            
            # Add component status
            if self.audio_capture:
                status["capture_status"] = self.audio_capture.get_status()
                
            # Add chunk processing status
            status["chunks_processed"] = self.chunk_counter
            
            return status
    
    def _handle_audio_chunk(self, audio_data: np.ndarray, timestamp: float, vad_info: Dict[str, Any]) -> None:
        """Handle audio chunk from capture and dispatch to consumers with VAD filtering.
        
        Args:
            audio_data: Audio data as numpy array (already 100ms mixed chunks from AudioCapture)
            timestamp: Timestamp of the audio chunk
            vad_info: VAD information from individual sources
        """
        try:
            # Add to buffer for recent audio retrieval
            self._add_to_buffer(audio_data)
            
            # Dispatch to consumers with VAD-aware filtering
            self._dispatch_to_consumers(audio_data, timestamp, vad_info)
                
        except Exception as e:
            logger.error(f"Failed to handle audio chunk: {e}")
    
    def _dispatch_to_consumers(self, audio_data: np.ndarray, timestamp: float, vad_info: Dict[str, Any]) -> None:
        """Dispatch audio chunk to consumers with VAD-aware filtering.
        
        Args:
            audio_data: Audio chunk data (100ms)
            timestamp: Timestamp of the audio chunk
            vad_info: VAD information from individual sources
        """
        try:
            if not self.consumer_callbacks:
                return
                
            # Calculate chunk duration (100ms for 1600 samples at 16kHz)
            duration = len(audio_data) / 16000.0  # Convert samples to seconds
            
            # Create AudioChunk object for each consumer
            for consumer_type, callback in self.consumer_callbacks.items():
                try:
                    # Determine if this consumer should receive this chunk
                    should_process = self._should_process_for_consumer(consumer_type, vad_info)
                    
                    if should_process:
                        audio_chunk = AudioChunk(
                            data=audio_data.copy(),
                            timestamp=timestamp,
                            sample_rate=16000,
                            duration=duration,
                            consumer_type=consumer_type,
                            chunk_id=self.chunk_counter,
                            channels=1
                        )
                        
                        # Call the consumer callback
                        callback(audio_chunk)
                    else:
                        # Skip this consumer (e.g., transcription when no speech)
                        logger.debug(f"Skipping {consumer_type.value} - no speech detected")
                    
                except Exception as e:
                    logger.error(f"Error dispatching to consumer {consumer_type}: {e}")
            
            # Increment chunk counter
            self.chunk_counter += 1
                    
        except Exception as e:
            logger.error(f"Error in consumer dispatch: {e}")
    
    def _should_process_for_consumer(self, consumer_type: AudioConsumerType, vad_info: Dict[str, Any]) -> bool:
        """Determine if a consumer should process this chunk based on VAD results.
        
        Args:
            consumer_type: The type of consumer
            vad_info: VAD information from the chunk
            
        Returns:
            True if the consumer should process this chunk
        """
        # Extract VAD results
        mic_has_speech = vad_info.get("mic_has_speech", False)
        system_has_speech = vad_info.get("system_has_speech", False)
        vad_enabled = vad_info.get("vad_enabled", True)
        
        # Debug logging for the first few chunks
        if self.chunk_counter < 5:
            logger.info(f"Consumer {consumer_type.value}: VAD enabled={vad_enabled}, mic_speech={mic_has_speech}, system_speech={system_has_speech}")
        
        # If VAD is disabled, all consumers get all chunks
        if not vad_enabled:
            return True
        
        if consumer_type == AudioConsumerType.TRANSCRIPTION:
            # Only process transcription when speech is detected in either source
            result = mic_has_speech or system_has_speech
            if self.chunk_counter < 5:
                logger.info(f"Transcription decision: {result}")
            return result
        
        elif consumer_type == AudioConsumerType.UI_LEVELS:
            # Always show UI levels regardless of speech detection
            return True
        
        elif consumer_type == AudioConsumerType.RECORDING:
            # Always record everything (could be made configurable)
            return True
        
        else:
            # Unknown consumer type, default to processing
            return True
    
    def _add_to_buffer(self, audio_data: np.ndarray) -> None:
        """Add audio data to the circular buffer.
        
        Args:
            audio_data: Audio data to add to buffer
        """
        with self.lock:
            try:
                # Add new data to buffer
                self.audio_buffer.extend(audio_data.tolist())
                
                # Trim buffer if it exceeds maximum size
                if len(self.audio_buffer) > self.buffer_max_samples:
                    excess = len(self.audio_buffer) - self.buffer_max_samples
                    self.audio_buffer = self.audio_buffer[excess:]
                
            except Exception as e:
                logger.error(f"Failed to add to buffer: {e}")
    
    def cleanup(self) -> None:
        """Cleanup audio manager resources."""
        with self.lock:
            try:
                if self.is_capturing:
                    self.stop_capture()
                
                # Clear buffer
                self.audio_buffer.clear()
                
                # Clear callbacks
                self.consumer_callbacks.clear()
                
                logger.debug("AudioManager cleanup completed")
                
            except Exception as e:
                logger.error(f"Error during AudioManager cleanup: {e}") 