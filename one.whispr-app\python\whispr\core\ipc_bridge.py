"""
IPC Bridge for the One.Whispr application.

This module implements the IPC Bridge that facilitates communication
between the Electron frontend and Python backend.
"""

import asyncio
import json
import logging
import time
import uuid
from collections import deque
from typing import Dict, Any, Optional, List, Callable, Union, Set, Deque

from whispr.core.base import BaseService, Event

logger = logging.getLogger('whispr.ipc_bridge')

class IPCBridge(BaseService):
    """IPC Bridge for communication between Electron frontend and Python backend."""
    
    def __init__(self, service_container=None):
        """Initialize the IPC Bridge.
        
        Args:
            service_container: The service container for dependency resolution
        """
        super().__init__(service_container)
        self._clients = set()
        self._event_handlers = {}
        self._message_queue = []
        self._reconnect_timeout = 5.0  # Seconds to wait before attempting reconnect
        self._max_queue_size = 100  # Maximum number of messages to queue
        self._connected = False
        self._last_connect_attempt = 0
        self._registered_handlers = {}
        
        # Event subscription system (similar to old EventBroadcaster)
        self._event_subscriptions = {}  # client_id -> set of event types
        self._event_queue: Deque[Dict[str, Any]] = deque(maxlen=1000)
        self._pending_commands = {}  # message_id -> command details
        self._command_timeout = 30.0  # seconds
        self._processing_task = None
        self._processing_running = False
    
    async def initialize(self) -> bool:
        """Initialize the IPC Bridge.
        
        Returns:
            True if initialization was successful, False otherwise
        """
        try:
            # Get the WebSocketServer service
            self.websocket = self.get_service("websocket")
            if not self.websocket:
                self.logger.error("WebSocketServer service not found")
                return False
            
            # Register handlers for client connection/disconnection
            self.websocket.on_client_connect = self._on_client_connect
            self.websocket.on_client_disconnect = self._on_client_disconnect
            
            # Start the event processing loop
            self._processing_running = True
            self._processing_task = asyncio.create_task(self._event_processing_loop())
            
            self._is_initialized = True
            self.logger.info("IPC Bridge initialized")
            return True
        except Exception as e:
            self.logger.error(f"Error initializing IPC Bridge: {e}")
            return False
    
    async def _on_client_connect(self, client) -> None:
        """Handle client connection.
        
        Args:
            client: The connected client
        """
        self._clients.add(client)
        self._connected = True
        
        # Store client ID with the client object for subscription tracking
        client_id = f"{client.remote_address[0]}:{client.remote_address[1]}"
        client._client_id = client_id
        
        self.logger.info(f"Client connected: {client_id}")
        
        # Send any queued messages
        if self._message_queue:
            self.logger.info(f"Sending {len(self._message_queue)} queued messages")
            for message in self._message_queue:
                await self.send_message(message)
            self._message_queue.clear()
    
    async def _on_client_disconnect(self, client) -> None:
        """Handle client disconnection.
        
        Args:
            client: The disconnected client
        """
        if client in self._clients:
            self._clients.remove(client)
            
        # Remove client subscriptions
        if hasattr(client, '_client_id'):
            client_id = client._client_id
            if client_id in self._event_subscriptions:
                del self._event_subscriptions[client_id]
                self.logger.info(f"Removed subscriptions for client: {client_id}")
        
        if not self._clients:
            self._connected = False
            self.logger.info("All clients disconnected")
    
    async def _handle_get_services(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle getServices message.
        
        Args:
            args: Message arguments
            
        Returns:
            Dictionary with services information
        """
        if not self.service_container:
            return {"services": []}
        
        services = []
        for service_type in self.service_container.get_services():
            service = self.service_container.resolve(service_type)
            if service:
                services.append({
                    "name": service_type,
                    "status": service.get_status() if hasattr(service, "get_status") else {"initialized": True}
                })
        
        return {"services": services}
    
    async def _handle_get_status(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system.status message.
        
        Args:
            args: Message arguments
            
        Returns:
            Dictionary with status information
        """
        return self.get_status()
    
    async def _handle_ping(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system.ping message.
        
        Args:
            args: Message arguments
            
        Returns:
            Dictionary with pong response
        """
        return {"pong": True, "timestamp": time.time()}
    
    async def _handle_get_config(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle getConfig message.
        
        Args:
            args: Message arguments
            
        Returns:
            Dictionary with configuration
        """
        config = self.get_service("config")
        if not config:
            return {"error": "ConfigManager not available"}
        
        return config.get_config()
    
    async def _handle_update_config(self, args: Dict[str, Any]) -> Dict[str, Any]:
        """Handle updateConfig message.
        
        Args:
            args: Message arguments
            
        Returns:
            Dictionary with update result
        """
        config = self.get_service("config")
        if not config:
            return {"success": False, "error": "ConfigManager not available"}
        
        try:
            if "config" not in args:
                return {"success": False, "error": "No configuration provided"}
                
            result = config.update_config(args["config"])
            return {"success": result}
        except Exception as e:
            self.logger.error(f"Error updating configuration: {e}")
            return {"success": False, "error": str(e)}
    
    async def _handle_subscribe_events(self, args: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle event subscription requests.
        
        Args:
            args: The subscription parameters
            context: The context with websocket client
            
        Returns:
            Success response
        """
        try:
            client_id = args.get("client_id")
            event_types = args.get("event_types", [])
            
            websocket = context.get("websocket")
            if not websocket:
                return {"success": False, "error": "No WebSocket connection available"}
                
            if not client_id:
                if hasattr(websocket, '_client_id'):
                    client_id = websocket._client_id
                else:
                    return {"success": False, "error": "client_id is required"}
            
            # Set the client ID on the websocket for future reference
            websocket._client_id = client_id
            
            # Create or update subscription
            self._event_subscriptions[client_id] = set(event_types)
            
            self.logger.info(f"Client {client_id} subscribed to events: {event_types}")
            
            return {
                "success": True,
                "client_id": client_id,
                "subscribed_events": event_types
            }
        except Exception as e:
            self.logger.error(f"Error in subscribe_events handler: {e}")
            return {"success": False, "error": str(e)}
    
    async def send_message(self, message: Union[Dict[str, Any], str]) -> bool:
        """Send a message to all connected clients.
        
        Args:
            message: The message to send
            
        Returns:
            True if the message was sent, False otherwise
        """
        # If no clients or websocket server, queue the message
        if not self._clients or not self.websocket:
            self._queue_message(message)
            return False
        
        try:
            if isinstance(message, dict):
                message_str = json.dumps(message)
            else:
                message_str = message
                
            await self.websocket.broadcast(message_str)
            return True
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            self._queue_message(message)
            return False
    
    def send_message_sync(self, message: Union[Dict[str, Any], str]) -> bool:
        """Send a message to all connected clients synchronously.
        
        Args:
            message: The message to send
            
        Returns:
            True if the message was sent, False otherwise
        """
        # If no websocket server or no clients connected, queue the message
        if not self.websocket or not self._clients:
            self._queue_message(message)
            self.logger.debug(f"No active connections, message queued for later delivery")
            return False
        
        # Key events need special handling for real-time delivery
        if isinstance(message, dict) and message.get("type") == "key_event":
            # Add to event queue for immediate processing
            self._event_queue.append(message)
            # Also try direct delivery for better responsiveness with keyboard events
            try:
                if isinstance(message, dict):
                    message_str = json.dumps(message)
                else:
                    message_str = message
                    
                return self.websocket.broadcast_sync(message_str)
            except Exception as e:
                self.logger.debug(f"Direct key event delivery failed, using queue: {e}")
                # Will still be processed via event queue
                return True
        
        # Regular events get queued for subscription-based processing
        if isinstance(message, dict) and message.get("type") == "event":
            self._event_queue.append(message)
            return True
        
        # For non-event messages, try direct delivery
        try:
            if isinstance(message, dict):
                message_str = json.dumps(message)
            else:
                message_str = message
                
            success = self.websocket.broadcast_sync(message_str)
            if not success:
                self._queue_message(message)
            return success
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            self._queue_message(message)
            return False
    
    def _queue_message(self, message: Union[Dict[str, Any], str]) -> None:
        """Queue a message for later delivery.
        
        Args:
            message: The message to queue
        """
        # Limit queue size
        if len(self._message_queue) >= self._max_queue_size:
            self._message_queue.pop(0)  # Remove oldest message
            
        self._message_queue.append(message)
        self.logger.debug(f"Message queued (queue size: {len(self._message_queue)})")
    
    async def send_event(self, event: Union[Event, str, Dict[str, Any]]) -> bool:
        """Send an event to all connected clients.
        
        Args:
            event: The event to send
            
        Returns:
            True if the event was sent, False otherwise
        """
        try:
            if isinstance(event, Event):
                message = event.to_dict()
            elif isinstance(event, dict):
                message = {"type": "event", "event": event.get("event", "unknown"), "data": event}
            else:
                message = {"type": "event", "event": event, "data": {}}
                
            # Add to event queue for subscription-based processing
            self._event_queue.append(message)
            return True
        except Exception as e:
            self.logger.error(f"Error sending event: {e}")
            return False
    
    async def _event_processing_loop(self) -> None:
        """Background task to process queued events."""
        try:
            while self._processing_running:
                try:
                    # Process any events in the queue
                    events_to_process = []
                    while self._event_queue and len(events_to_process) < 20:
                        events_to_process.append(self._event_queue.popleft())
                    
                    if events_to_process:
                        await self._process_events(events_to_process)
                    
                    # Clean up any timed-out pending commands
                    now = time.time()
                    timed_out_commands = [
                        cmd_id for cmd_id, cmd in self._pending_commands.items()
                        if now - cmd["timestamp"] > self._command_timeout
                    ]
                    
                    for cmd_id in timed_out_commands:
                        self.logger.warning(f"Command {cmd_id} timed out")
                        self._pending_commands.pop(cmd_id, None)
                    
                    # Wait a short time
                    await asyncio.sleep(0.1)
                except Exception as e:
                    self.logger.error(f"Error in event processing loop: {e}")
                    await asyncio.sleep(1.0)
        except asyncio.CancelledError:
            self.logger.info("Event processing loop cancelled")
        except Exception as e:
            self.logger.error(f"Event processing loop exited with error: {e}")
    
    async def _process_events(self, events: List[Dict[str, Any]]) -> None:
        """Process a batch of events.
        
        Args:
            events: List of events to process
        """
        if not self._clients:
            # No clients connected, just queue messages for later
            for event in events:
                self._queue_message(event)
            return
            
        # Group events by client based on subscriptions
        client_events = {}
        
        for event in events:
            # Special handling for key_event type - high priority delivery to all clients
            if event.get("type") == "key_event":
                for client in self._clients:
                    if client not in client_events:
                        client_events[client] = []
                    client_events[client].append(event)
                continue
            
            # Standard event processing for regular events
            event_type = event.get("event")
            
            # Determine which clients should receive this event
            for client in self._clients:
                client_id = getattr(client, '_client_id', None)
                
                # Skip clients without an ID
                if not client_id:
                    continue
                    
                # Check if this client is subscribed to this event type
                subscribed_events = self._event_subscriptions.get(client_id, set())
                
                # If client has no specific subscriptions or is subscribed to this event type
                if not subscribed_events or event_type in subscribed_events:
                    if client not in client_events:
                        client_events[client] = []
                    client_events[client].append(event)
        
        # Send events to each client
        for client, client_event_list in client_events.items():
            try:
                # If multiple events of the same type, batch them
                event_batches = {}
                standalone_events = []
                
                for event in client_event_list:
                    # Key events are always delivered as standalone for lowest latency
                    if event.get("type") == "key_event":
                        standalone_events.append(event)
                        continue
                    
                    event_type = event.get("event")
                    
                    # Skip events without a type
                    if not event_type:
                        standalone_events.append(event)
                        continue
                    
                    # Batch transcription events for efficiency
                    if event_type.startswith("transcription."):
                        if event_type not in event_batches:
                            event_batches[event_type] = []
                        event_batches[event_type].append(event)
                    else:
                        standalone_events.append(event)
                
                # Send batched events
                for event_type, batch in event_batches.items():
                    if len(batch) > 1:
                        # Create a batch message
                        batch_message = {
                            "type": "event_batch",
                            "event": event_type,
                            "events": batch,
                            "count": len(batch)
                        }
                        
                        await client.send(json.dumps(batch_message))
                    else:
                        # Just send the single event
                        await client.send(json.dumps(batch[0]))
                
                # Send standalone events
                for event in standalone_events:
                    await client.send(json.dumps(event))
            except Exception as e:
                self.logger.error(f"Error sending events to client: {e}")
    
    def update_config(self, config: Dict[str, Any]) -> bool:
        """Update the service configuration.
        
        Args:
            config: The new configuration
            
        Returns:
            True if the update was successful, False otherwise
        """
        try:
            if "reconnect_timeout" in config:
                self._reconnect_timeout = float(config["reconnect_timeout"])
                
            if "max_queue_size" in config:
                self._max_queue_size = int(config["max_queue_size"])
                
            if "command_timeout" in config:
                self._command_timeout = float(config["command_timeout"])
                
            return True
        except Exception as e:
            self.logger.error(f"Error updating configuration: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current service status.
        
        Returns:
            A dictionary containing status information
        """
        status = super().get_status()
        status.update({
            "connected": self._connected,
            "client_count": len(self._clients) if self._clients else 0,
            "queued_messages": len(self._message_queue),
            "pending_commands": len(self._pending_commands),
            "event_queue_size": len(self._event_queue),
            "registered_handlers": list(self._registered_handlers.keys()),
            "event_subscriptions": {client_id: list(events) for client_id, events in self._event_subscriptions.items()}
        })
        return status
    
    async def cleanup(self) -> bool:
        """Clean up resources.
        
        Returns:
            True if cleanup was successful, False otherwise
        """
        try:
            # Stop the event processing loop
            self._processing_running = False
            if self._processing_task:
                self._processing_task.cancel()
                try:
                    await self._processing_task
                except asyncio.CancelledError:
                    pass
            
            # Clear registered handlers
            if self.websocket:
                for message_type in self._registered_handlers:
                    self.websocket.unregister_handler(message_type)
            
            self._clients.clear()
            self._message_queue.clear()
            self._registered_handlers.clear()
            self._event_subscriptions.clear()
            self._event_queue.clear()
            self._pending_commands.clear()
            self._connected = False
            
            self._is_initialized = False
            self.logger.info("IPC Bridge cleaned up")
            return True
        except Exception as e:
            self.logger.error(f"Error cleaning up IPC Bridge: {e}")
            return False 