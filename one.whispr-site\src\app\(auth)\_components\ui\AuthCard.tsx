'use client';

import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/card';

interface AuthCardProps {
  title: string;
  children: React.ReactNode;
  footerContent?: React.ReactNode;
  showTerms?: boolean;
}

/**
 * The main card container for all authentication flows
 */
export function AuthCard({ 
  title, 
  children, 
  footerContent,
  showTerms = true 
}: AuthCardProps) {
  return (
    <div className="min-h-screen flex items-center justify-center relative">
      <div className="w-full max-w-md">
        {/* Logo and title positioned above the card */}
        <div className="absolute left-1/2 -translate-x-1/2 -mt-29 flex flex-col items-center">
          <div className="mb-3">
            <Image 
              src="/one.whispr-white.png" 
              alt="Whispr" 
              width={48} 
              height={48} 
              className="rounded-sm hidden dark:block" 
            />
            <Image 
              src="/one.whispr-black.png" 
              alt="Whispr" 
              width={48} 
              height={48} 
              className="rounded-sm block dark:hidden" 
            />
          </div>
          <h1 className="text-2xl font-semibold">{title}</h1>
        </div>

        <Card className="w-full">
          <CardContent className="space-y-6 px-6">
            {children}
          </CardContent>
        </Card>
        
        {/* Footer content positioned below the card */}
        {footerContent && (
          <div className="absolute left-1/2 -translate-x-1/2 mt-6 w-full text-center">
            {footerContent}
          </div>
        )}
        
        {/* Terms of service - at bottom of page */}
        {showTerms && (
          <div className="absolute bottom-8 left-0 right-0 text-center">
            <p className="text-base text-muted-foreground">
              By continuing, you agree to our{' '}
              <a href="/terms" className="underline underline-offset-4 hover:text-foreground">
                Terms of Service
              </a>
              {' '}and{' '}
              <a href="/privacy" className="underline underline-offset-4 hover:text-foreground">
                Privacy Policy
              </a>
            </p>
          </div>
        )}
      </div>
    </div>
  );
} 