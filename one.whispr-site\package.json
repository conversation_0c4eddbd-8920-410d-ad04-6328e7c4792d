{"name": "one.whispr-site", "description": "One Whispr - Website", "version": "1.0.0", "author": "One Whispr Team", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:apply-migrations": "tsx scripts/apply-migrations.ts"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-hook-form": "^7.55.0", "clsx": "^2.1.1", "class-variance-authority": "^0.7.1", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "input-otp": "^1.4.2", "next": "15.3.0", "next-themes": "^0.4.6"}, "devDependencies": {"typescript": "^5.8.3", "@types/node": "^22.14.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.2", "@tailwindcss/postcss": "^4", "tailwindcss": "^4.1.3", "lightningcss": "^1.28.2", "tsx": "^4.19.3"}}