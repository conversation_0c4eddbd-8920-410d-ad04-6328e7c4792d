/**
 * Core Types and Enums
 * Shared types used across all domains
 */

// ============================================================================
// CORE ENUMS & TYPES
// ============================================================================

export type SetupStep = 'auth' | 'model' | 'audio' | 'shortcut' | 'tryit';
export type RecordingMode = 'toggle' | 'pushToTalk';
export type ModelType = 'whisper' | 'distil-whisper';
export type DeviceType = 'input' | 'output';
export type TranscriptionState = 'idle' | 'recording' | 'processing' | 'complete';

export type VoiceModelId = 
  | 'openai/whisper-tiny'
  | 'openai/whisper-base'
  | 'openai/whisper-base.en'
  | 'openai/whisper-small'
  | 'distil-whisper/distil-small.en'
  | 'distil-whisper/distil-medium.en'
  | 'distil-whisper/distil-large-v3';

// ============================================================================
// COMPONENT PROP TYPES
// ============================================================================

export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface StepComponentProps extends BaseComponentProps {
  onNext?: () => void;
  onPrevious?: () => void;
  canProceed?: boolean;
}

export interface LoadingSpinnerProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'accent';
}

export interface ErrorMessageProps extends BaseComponentProps {
  error: string | null;
  onDismiss?: () => void;
}

export interface ProgressBarProps extends BaseComponentProps {
  progress: number;
  total: number;
  showPercentage?: boolean;
}

// ============================================================================
// SYSTEM TYPES
// ============================================================================

export interface SystemInfo {
  ram: number; // GB
  cpuCores: number;
  platform: string;
  canUseGPU: boolean;
}

// ============================================================================
// IPC MESSAGE TYPES
// ============================================================================

export interface PythonMessage {
  type: 'event' | 'key_event' | 'response';
  event?: string;
  data?: any;
}

export interface KeyEvent {
  type: 'key_event';
  data: {
    currently_pressed: string[];
  };
}

export interface AudioLevelEvent {
  type: 'event';
  event: 'audio_levels';
  data: {
    levels: {
      microphone: number;
    };
  };
} 