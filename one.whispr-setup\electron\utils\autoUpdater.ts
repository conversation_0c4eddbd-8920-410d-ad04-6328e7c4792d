import { autoUpdater } from 'electron-updater';
import { SETUP_UPDATES } from '../constants';

/**
 * Auto-updater setup and configuration
 */
export function setupAutoUpdater(): void {
  // Configure autoUpdater
  autoUpdater.setFeedURL({
    provider: 'generic',
    url: SETUP_UPDATES.baseUrl
  });

  // Check for updates
  autoUpdater.checkForUpdatesAndNotify();
  
  // Setup event handlers
  autoUpdater.on('update-available', (info) => {
    console.log('[UPDATER] Update available!', info);
  });
  
  autoUpdater.on('update-not-available', (info) => {
    console.log('[UPDATER] No update available.', info);
  });
  
  autoUpdater.on('error', (err) => {
    console.error('[UPDATER] Error:', err);
  });
  
  autoUpdater.on('download-progress', (progress) => {
    console.log('[UPDATER] Download progress:', Math.round(progress.percent), '%');
  });
  
  autoUpdater.on('update-downloaded', (info) => {
    console.log('[UPDATER] Update downloaded. Will install on quit.', info);
  });
}
