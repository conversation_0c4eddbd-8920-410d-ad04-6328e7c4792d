"use client";

import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from "@/components/ui/button";
import { FaWindows } from 'react-icons/fa';
import { cn } from "@/lib/utils";

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);
  
  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);
  
  // Handle scroll event
  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 20;
      if (isScrolled !== scrolled) {
        setScrolled(isScrolled);
      }
    };
    
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [scrolled]);
  
  const logoSrc = '/one.whispr-white.png';

  // Function to check if a link is active
  const isActive = (path: string) => {
    if (!mounted) return false;
    if (path.startsWith('/#')) {
      return pathname === '/' && window.location.hash === path.substring(1);
    }
    if (path === '/') return pathname === '/';
    return pathname.startsWith(path);
  };

  // Function to handle smooth scrolling
  const handleScroll = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
    if (pathname !== '/') {
      // If not on homepage, navigate to homepage first with the hash
      return;
    }
    
    e.preventDefault();
    const element = document.getElementById(id);
    if (element) {
      // Close mobile menu if open
      if (mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
      
      // Smooth scroll to element
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      
      // Update URL hash without scrolling
      window.history.pushState(null, '', `/#${id}`);
    }
  };

  return (
    <header className={cn(
      "fixed top-4 left-1/2 -translate-x-1/2 w-[98%] max-w-[1550px] z-50 bg-background/90 backdrop-blur-sm rounded-xl shadow-lg overflow-hidden transition-all duration-300",
      scrolled ? "bg-background/95 shadow-xl box-shadow-glow" : ""
    )}>
      <div className="container mx-auto px-4 py-5">
        <div className="grid grid-cols-[1fr_auto_1fr] items-center w-full">
          {/* Logo - Left Section */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center">
              <div className="flex items-center gap-[3px]">
                <div className="w-13 h-13 relative flex-shrink-0">
                  <Image 
                    src={logoSrc} 
                    alt="whispr one logo" 
                    fill
                    sizes="(max-width: 768px) 64px, 64px"
                    className="object-contain object-left"
                    priority
                  />
                </div>
              </div>
            </Link>
          </div>

          {/* Navigation - Center Section */}
          <nav className="hidden md:flex items-center justify-center space-x-12">
            <Link 
              href="/use-cases" 
              className={cn(
                "text-[15px] font-medium tracking-widest font-display uppercase transition-colors relative",
                isActive('/use-cases') 
                  ? "text-foreground font-semibold after:absolute after:bottom-[-6px] after:left-1/2 after:-translate-x-1/2 after:w-1 after:h-1 after:bg-foreground after:rounded-full" 
                  : "text-muted-foreground hover:text-foreground/80"
              )}
            >
              Use Cases
            </Link>
            <Link 
              href="/features" 
              className={cn(
                "text-[15px] font-medium tracking-widest font-display uppercase transition-colors relative",
                isActive('/features') 
                  ? "text-foreground font-semibold after:absolute after:bottom-[-6px] after:left-1/2 after:-translate-x-1/2 after:w-1 after:h-1 after:bg-foreground after:rounded-full" 
                  : "text-muted-foreground hover:text-foreground/80"
              )}
            >
              Features
            </Link>
            <Link 
              href="/#pricing" 
              className={cn(
                "text-[15px] font-medium tracking-widest font-display uppercase transition-colors relative",
                isActive('/#pricing') 
                  ? "text-foreground font-semibold after:absolute after:bottom-[-6px] after:left-1/2 after:-translate-x-1/2 after:w-1 after:h-1 after:bg-foreground after:rounded-full" 
                  : "text-muted-foreground hover:text-foreground/80"
              )}
              onClick={(e) => handleScroll(e, 'pricing')}
            >
              Pricing
            </Link>
            <Link 
              href="/#faq" 
              className={cn(
                "text-[15px] font-medium tracking-widest font-display uppercase transition-colors relative",
                isActive('/#faq') 
                  ? "text-foreground font-semibold after:absolute after:bottom-[-6px] after:left-1/2 after:-translate-x-1/2 after:w-1 after:h-1 after:bg-foreground after:rounded-full" 
                  : "text-muted-foreground hover:text-foreground/80"
              )}
              onClick={(e) => handleScroll(e, 'faq')}
            >
              FAQ
            </Link>
          </nav>

          {/* Auth Buttons - Right Section */}
          <div className="hidden md:flex items-center justify-end space-x-4">
            <Button asChild variant="outline" size="lg" className="font-display font-medium uppercase tracking-widest text-[15px]">
              <Link href="/login">
                Sign In
              </Link>
            </Button>
            <Button asChild size="lg" className="font-display font-medium uppercase tracking-widest text-[15px]">
              <Link 
                href="/#download"
                className="flex items-center gap-2"
                onClick={(e) => handleScroll(e, 'download')}
              >
                <FaWindows size={16} />
                <span>Download</span>
              </Link>
            </Button>
          </div>

          {/* Mobile menu button */}
          <button 
            className="md:hidden justify-self-end"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            aria-label="Toggle mobile menu"
          >
            {mobileMenuOpen ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {mobileMenuOpen && (
        <div className={cn(
          "md:hidden p-6 pb-8 bg-background border-b space-y-6",
          scrolled ? "bg-background/95" : ""
        )}>
          <nav className="flex flex-col space-y-6">
            <Link 
              href="/use-cases" 
              className={cn(
                "text-[15px] font-medium tracking-widest font-display uppercase transition-colors relative",
                isActive('/use-cases') 
                  ? "text-foreground font-semibold" 
                  : "text-muted-foreground hover:text-foreground/80"
              )}
              onClick={() => setMobileMenuOpen(false)}
            >
              Use Cases
              {isActive('/use-cases') && <span className="absolute -left-4 top-1/2 -translate-y-1/2 w-2 h-2 bg-foreground rounded-full"></span>}
            </Link>
            <Link 
              href="/features" 
              className={cn(
                "text-[15px] font-medium tracking-widest font-display uppercase transition-colors relative",
                isActive('/features') 
                  ? "text-foreground font-semibold" 
                  : "text-muted-foreground hover:text-foreground/80"
              )}
              onClick={() => setMobileMenuOpen(false)}
            >
              Features
              {isActive('/features') && <span className="absolute -left-4 top-1/2 -translate-y-1/2 w-2 h-2 bg-foreground rounded-full"></span>}
            </Link>
            <Link 
              href="/#pricing" 
              className={cn(
                "text-[15px] font-medium tracking-widest font-display uppercase transition-colors relative",
                isActive('/#pricing') 
                  ? "text-foreground font-semibold" 
                  : "text-muted-foreground hover:text-foreground/80"
              )}
              onClick={(e) => handleScroll(e, 'pricing')}
            >
              Pricing
              {isActive('/#pricing') && <span className="absolute -left-4 top-1/2 -translate-y-1/2 w-2 h-2 bg-foreground rounded-full"></span>}
            </Link>
            <Link 
              href="/#faq" 
              className={cn(
                "text-[15px] font-medium tracking-widest font-display uppercase transition-colors relative",
                isActive('/#faq') 
                  ? "text-foreground font-semibold" 
                  : "text-muted-foreground hover:text-foreground/80"
              )}
              onClick={(e) => handleScroll(e, 'faq')}
            >
              FAQ
              {isActive('/#faq') && <span className="absolute -left-4 top-1/2 -translate-y-1/2 w-2 h-2 bg-foreground rounded-full"></span>}
            </Link>
          </nav>
          <div className="flex flex-col space-y-5 pt-5 border-t">
            <Button asChild variant="outline" size="lg" className="w-full font-display font-medium uppercase tracking-widest text-[15px]">
              <Link 
                href="/login"
                onClick={() => setMobileMenuOpen(false)}
              >
                Sign In
              </Link>
            </Button>
            <Button asChild size="lg" className="w-full font-display font-medium uppercase tracking-widest text-[15px]">
              <Link 
                href="/#download" 
                className="flex items-center justify-center gap-2"
                onClick={(e) => {
                  handleScroll(e, 'download');
                }}
              >
                <FaWindows size={16} />
                <span>Download</span>
              </Link>
            </Button>
          </div>
        </div>
      )}
    </header>
  );
} 