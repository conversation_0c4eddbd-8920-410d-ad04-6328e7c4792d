/**
 * Constants and Configuration
 * Shared constants, default values, and validation rules
 */

import type { SetupStep } from './core';
import type { StepConfig } from './setup-flow';
import type { Shortcuts } from './shortcut';

// ============================================================================
// CONSTANTS
// ============================================================================

export const SETUP_STEPS: SetupStep[] = ['auth', 'model', 'audio', 'shortcut', 'tryit'];

export const STEP_CONFIG: Record<SetupStep, Omit<StepConfig, 'isActive' | 'isCompleted'>> = {
  auth: {
    id: 'auth',
    title: "Let's get you started",
    description: 'Sign in to your account',
    canSkip: false,
    hasLifecycle: false,
  },
  model: {
    id: 'model',
    title: 'Choose Your Voice Model',
    description: 'Select a speech recognition model',
    canSkip: false,
    hasLifecycle: false,
  },
  audio: {
    id: 'audio',
    title: 'Audio Setup',
    description: 'Select your audio devices and monitor levels',
    canSkip: false,
    hasLifecycle: true, // Needs to start/stop audio monitoring
  },
  shortcut: {
    id: 'shortcut',
    title: 'Keyboard Shortcuts',
    description: 'Choose your recording mode and test shortcuts',
    canSkip: false,
    hasLifecycle: true, // Needs to enable/disable test mode
  },
  tryit: {
    id: 'tryit',
    title: 'Try It Out',
    description: 'Test your setup with a real transcription',
    canSkip: true,
    hasLifecycle: false,
  },
};

export const STEP_NAMES: Record<SetupStep, string> = {
  auth: 'Authentication',
  model: 'Voice Model',
  audio: 'Audio Setup',
  shortcut: 'Shortcuts',
  tryit: 'Try It Out'
};

export const DEFAULT_SHORTCUTS: Shortcuts = {
  pushToTalk: ['Control', 'Alt'],
  toggle: ['Control', 'Alt'],
  cancel: ['Escape'],
  modeSwitch: ['Alt', 'Space']
};

// Recommended models based on system specs
export const MODEL_RECOMMENDATIONS = {
  low: 'distil-whisper/distil-small.en',
  medium: 'openai/whisper-base',
  high: 'openai/whisper-small'
} as const;

// Validation rules
export const VALIDATION_RULES = {
  MIN_SHORTCUT_KEYS: 1,
  MAX_SHORTCUT_KEYS: 4,
  REQUIRED_STEPS: ['auth', 'model'] as SetupStep[],
  AUDIO_LEVEL_THRESHOLD: 0.1,
  TRANSCRIPTION_MIN_LENGTH: 5,
} as const; 