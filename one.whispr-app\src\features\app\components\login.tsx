import { Button } from '@src/components/ui/button';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@src/components/ui/card';
import { Separator } from '@src/components/ui/separator';
import { useAuth } from '../context';

export function Login() {
  const { login, register, loading, bypassLogin } = useAuth();
  
  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="text-2xl text-center">Welcome to One Whispr</CardTitle>
        <CardDescription className="text-center">
          AI-powered voice-to-text that works on any application
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <Button 
            className="w-full text-base" 
            onClick={login}
            disabled={loading}
          >
            {loading ? 'Processing...' : 'Sign In'}
          </Button>
          
          <div className="relative flex items-center justify-center">
            <Separator className="absolute" />
            <span className="relative bg-card px-2 text-sm text-muted-foreground">
              OR
            </span>
          </div>
          
          <Button 
            className="w-full text-base" 
            variant="outline" 
            onClick={register}
            disabled={loading}
          >
            Create Account
          </Button>
          
          {process.env.NODE_ENV === 'development' && (
            <>
              <div className="relative flex items-center justify-center mt-4">
                <Separator className="absolute" />
                <span className="relative bg-card px-2 text-sm text-muted-foreground">
                  Development Only
                </span>
              </div>
              
              <Button 
                className="w-full text-base" 
                variant="secondary" 
                onClick={bypassLogin}
              >
                Bypass Login (Dev)
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 