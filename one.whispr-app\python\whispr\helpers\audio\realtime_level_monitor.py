"""
Real-time audio level monitor for ultra-low latency level detection.

This module provides direct audio stream level monitoring that bypasses
buffer processing for instant visual feedback in audio setup interfaces.
"""

import numpy as np
import sounddevice as sd
import threading
import time
import logging
from typing import Optional, Callable, Dict, Any

logger = logging.getLogger(__name__)


class RealtimeLevelMonitor:
    """Ultra-low latency real-time audio level monitor for microphone only."""
    
    def __init__(self, sample_rate: int = 44100, channels: int = 1):
        """Initialize the real-time level monitor.
        
        Args:
            sample_rate: Audio sample rate for monitoring (44100 for max responsiveness)
            channels: Number of audio channels
        """
        self.sample_rate = sample_rate
        self.channels = channels
        
        # Direct level monitoring without buffering
        self.mic_level = 0.0
        self.level_lock = threading.Lock()
        
        # Stream objects for direct monitoring
        self.mic_stream = None
        
        # Device configuration
        self.input_device_id = None
        
        # State
        self.is_monitoring = False
        self.monitor_mic = True
        
        # Context for tracking additional information
        self.context = {}
        
        # Performance optimizations
        self.level_callback = None  # Optional callback for level updates
        self.last_update_time = 0
        self.min_update_interval = 0.05  # 50ms minimum between updates for smoother display
        
        logger.debug(f"RealtimeLevelMonitor initialized: {sample_rate}Hz, {channels}ch")
    
    def set_devices(self, input_device_id: Optional[int] = None):
        """Set the audio device for monitoring.
        
        Args:
            input_device_id: Input device ID (None for default)
        """
        self.input_device_id = input_device_id
        logger.debug(f"Set device: input={input_device_id}")

    def update_devices(self, input_device_id: Optional[int] = None):
        """Update monitoring device without stopping monitoring (hot-swap).
        
        Args:
            input_device_id: New input device ID (None for default)
        
        Returns:
            True if hot-swap successful, False otherwise
        """
        if not self.is_monitoring:
            # If not monitoring, just update device settings
            self.set_devices(input_device_id)
            return True
        
        logger.info(f"Hot-swapping monitoring device: input={input_device_id}")
        
        try:
            old_input = self.input_device_id
            self.input_device_id = input_device_id
            
            # Check if input device changed
            input_changed = old_input != input_device_id
            
            # Hot-swap microphone stream if input device changed
            if input_changed and self.mic_stream is not None:
                logger.debug(f"Hot-swapping microphone: {old_input} → {input_device_id}")
                try:
                    # Stop old stream
                    self.mic_stream.stop()
                    self.mic_stream.close()
                    self.mic_stream = None
                    
                    # Start new microphone stream
                    self._start_mic_monitoring()
                    logger.info(f"Microphone hot-swapped successfully: {old_input} → {input_device_id}")
                except Exception as e:
                    logger.error(f"Failed to hot-swap microphone: {e}")
                    return False
            
            logger.info("Device hot-swap completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error during device hot-swap: {e}")
            return False
    
    def set_level_callback(self, callback: Callable[[Dict[str, float]], None]):
        """Set a callback function for level updates.
        
        Args:
            callback: Function to call with level updates
        """
        self.level_callback = callback
    
    def _validate_input_device(self, device_id: Optional[int]) -> Optional[int]:
        """Validate and prepare input device for use.
        
        Args:
            device_id: Device ID to validate
            
        Returns:
            Valid device ID or None for default
        """
        if device_id is None:
            return None
            
        try:
            # Check if device exists and is valid
            device_info = sd.query_devices(device_id)
            if device_info['max_input_channels'] > 0:
                return device_id
            else:
                logger.warning(f"Device {device_id} has no input channels, using default")
                return None
        except Exception as e:
            logger.warning(f"Device {device_id} not valid: {e}, using default")
            return None
    
    def start_monitoring(self, monitor_mic: bool = True) -> bool:
        """Start real-time level monitoring.
        
        Args:
            monitor_mic: Whether to monitor microphone levels
            
        Returns:
            True if monitoring started successfully
        """
        if self.is_monitoring:
            return True
            
        self.monitor_mic = monitor_mic
        
        try:
            # Start microphone monitoring
            if monitor_mic:
                self._start_mic_monitoring()
            
            self.is_monitoring = True
            logger.info("Real-time level monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start level monitoring: {e}")
            self.stop_monitoring()
            return False
    
    def _start_mic_monitoring(self):
        """Start microphone level monitoring with minimal latency."""
        try:
            # Use small block size for low latency but not ultra-small for stability
            blocksize = 128  # Small block size for good response with less jitter
            
            def mic_callback(indata, frames, time_info, status):
                try:
                    if status:
                        logger.debug(f"Mic status: {status}")
                    
                    # Calculate RMS level directly without buffering
                    if indata.ndim > 1:
                        audio_data = indata[:, 0]  # Use first channel for mono
                    else:
                        audio_data = indata.flatten()
                    
                    rms_level = np.sqrt(np.mean(audio_data**2))
                    peak_level = np.max(np.abs(audio_data))
                    
                    # Use peak level for more responsive visual feedback
                    level = float(peak_level)
                    
                    # Apply smoothing to reduce jitter while maintaining responsiveness
                    with self.level_lock:
                        # Heavier exponential smoothing for stable readings
                        self.mic_level = self.mic_level * 0.85 + level * 0.15
                        
                    self._trigger_update()
                    
                except Exception as e:
                    logger.debug(f"Error in mic callback: {e}")
            
            # Validate and prepare device
            device_to_use = self._validate_input_device(self.input_device_id)
                
            self.mic_stream = sd.InputStream(
                device=device_to_use,
                channels=self.channels,
                samplerate=self.sample_rate,
                blocksize=blocksize,
                callback=mic_callback,
                dtype=np.float32
            )
            
            self.mic_stream.start()
            logger.debug(f"Microphone monitoring started on device {device_to_use}")
            
        except Exception as e:
            logger.error(f"Failed to start microphone monitoring: {e}")
            if self.mic_stream:
                try:
                    self.mic_stream.close()
                except:
                    pass
                self.mic_stream = None
            raise
    
    def _trigger_update(self):
        """Trigger level update callback if enough time has passed."""
        try:
            current_time = time.time()
            
            # Rate limit updates to avoid overwhelming the UI
            if current_time - self.last_update_time < self.min_update_interval:
                return
                
            self.last_update_time = current_time
            
            if self.level_callback:
                levels = self.get_levels()
                self.level_callback(levels)
                
        except Exception as e:
            logger.debug(f"Error in update trigger: {e}")
    
    def get_levels(self) -> Dict[str, float]:
        """Get current audio levels.
        
        Returns:
            Dictionary with current audio levels
        """
        with self.level_lock:
            return {
                'microphone': self.mic_level,
                'peak': self.mic_level  # For compatibility
            }
    
    def stop_monitoring(self):
        """Stop real-time level monitoring."""
        if not self.is_monitoring:
            return
            
        self.is_monitoring = False
        
        # Stop microphone stream
        if self.mic_stream:
            try:
                self.mic_stream.stop()
                self.mic_stream.close()
            except Exception as e:
                logger.debug(f"Error stopping mic stream: {e}")
            finally:
                self.mic_stream = None
        
        # Reset levels
        with self.level_lock:
            self.mic_level = 0.0
        
        logger.info("Real-time level monitoring stopped")
    
    def get_current_device(self) -> Optional[int]:
        """Get the currently configured input device ID.
        
        Returns:
            Current input device ID or None if using default
        """
        return self.input_device_id
        
    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the monitor.
        
        Returns:
            Dictionary with status information
        """
        return {
            "is_monitoring": self.is_monitoring,
            "input_device": self.input_device_id,
            "sample_rate": self.sample_rate,
            "channels": self.channels,
            "mic_level": self.get_levels().get("microphone", 0.0)
        }
    
    def __del__(self):
        """Cleanup when object is destroyed."""
        try:
            self.stop_monitoring()
        except:
            pass 