'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { FcGoogle } from "react-icons/fc";
import { FaXTwitter } from "react-icons/fa6";
import { FormSeparator } from './FormSeparator';
import { AuthMode } from '../utils/auth-utils';
import { createClient } from '@/utils/supabase/client';

interface SocialButtonsProps {
  mode?: AuthMode;
  showSeparator?: boolean;
  callbackUrl?: string | null;
  action?: string | null;
}

/**
 * Social login/registration buttons component
 */
export function SocialButtons({ 
  mode = 'login',
  showSeparator = true,
  callbackUrl,
  action
}: SocialButtonsProps) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle Google authentication
  const handleGoogleAuth = async () => {
    setLoading(true);
    setError(null);

    try {
      const supabase = createClient();
      
      // Store the callbackUrl in localStorage if it's a whispr:// URL
      if (callbackUrl && callbackUrl.startsWith('whispr://')) {
        try {
          localStorage.setItem('whisprCallbackUrl', callbackUrl);
        } catch (error) {
          // Silently handle localStorage errors
        }
      }
      
      // Get the current site URL to ensure correct redirection
      const siteUrl = window.location.origin;
      
      // Determine redirect URL for success page with appropriate params
      const redirectTo = `${siteUrl}/success`;
      
      // Store additional params to pass after authentication
      const additionalParams = new URLSearchParams();
      if (callbackUrl) {
        additionalParams.set('callbackUrl', callbackUrl);
      }
      additionalParams.set('action', mode); // login or register
      
      // Construct final redirect URL with params
      const finalRedirectUrl = `${redirectTo}?${additionalParams.toString()}`;
      
      // Sign in with Google OAuth
      const { data, error: signInError } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: finalRedirectUrl,
          queryParams: {
            access_type: 'offline',
            prompt: 'consent',
          },
        },
      });

      if (signInError) {
        console.error(`Failed to ${mode} with Google:`, signInError.message);
        setError('Google login failed. Please try again.');
      } else if (data.url) {
        // Redirect to the OAuth URL
        window.location.href = data.url;
      }
    } catch (error) {
      console.error(`Failed to ${mode} with Google`, error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle Twitter authentication
  const handleTwitterAuth = async () => {
    setLoading(true);
    setError(null);

    try {
      const supabase = createClient();
      
      // Store the callbackUrl in localStorage if it's a whispr:// URL
      if (callbackUrl && callbackUrl.startsWith('whispr://')) {
        try {
          localStorage.setItem('whisprCallbackUrl', callbackUrl);
        } catch (error) {
          // Silently handle localStorage errors
        }
      }
      
      // Get the current site URL to ensure correct redirection
      const siteUrl = window.location.origin;
      
      // Determine redirect URL for success page with appropriate params
      const redirectTo = `${siteUrl}/success`;
      
      // Store additional params to pass after authentication
      const additionalParams = new URLSearchParams();
      if (callbackUrl) {
        additionalParams.set('callbackUrl', callbackUrl);
      }
      additionalParams.set('action', mode); // login or register
      
      // Construct final redirect URL with params
      const finalRedirectUrl = `${redirectTo}?${additionalParams.toString()}`;
      
      // Sign in with Twitter OAuth
      const { data, error: signInError } = await supabase.auth.signInWithOAuth({
        provider: 'twitter',
        options: {
          redirectTo: finalRedirectUrl,
        },
      });

      if (signInError) {
        console.error(`Failed to ${mode} with Twitter:`, signInError.message);
        setError('Twitter login failed. Please try again.');
      } else if (data.url) {
        // Redirect to the OAuth URL
        window.location.href = data.url;
      }
    } catch (error) {
      console.error(`Failed to ${mode} with Twitter`, error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Auto-trigger social login based on action parameter
  useEffect(() => {
    if (action === 'google') {
      handleGoogleAuth();
    } else if (action === 'twitter') {
      handleTwitterAuth();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [action]); // Only depend on action to avoid infinite loops

  const buttonText = 'Continue with';

  // Show loading message when auto-triggering
  const isAutoTriggering = action === 'google' || action === 'twitter';

  return (
    <>
      {/* Show auto-trigger message */}
      {isAutoTriggering && (
        <div className="text-center text-sm text-primary mb-4">
          {action === 'google' ? 'Redirecting to Google...' : 'Redirecting to X...'}
        </div>
      )}

      {/* Separator */}
      {showSeparator && <FormSeparator />}
      
      {/* Social buttons */}
      <div className="space-y-3">
        <Button 
          type="button" 
          variant="outline" 
          className="w-full text-base" 
          onClick={handleGoogleAuth}
          disabled={loading || isAutoTriggering}
        >
          <FcGoogle className="mr-2 h-4 w-4" />
          {buttonText} Google
        </Button>
        
        <Button 
          type="button" 
          variant="outline" 
          className="w-full text-base" 
          onClick={handleTwitterAuth}
          disabled={loading || isAutoTriggering}
        >
          <FaXTwitter className="mr-2 h-4 w-4" />
          {buttonText} X
        </Button>
        
        {error && (
          <p className="text-sm text-destructive text-center">{error}</p>
        )}
      </div>
    </>
  );
} 