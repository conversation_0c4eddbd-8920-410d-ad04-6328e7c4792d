'use client';

import { useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { FiArrowRight, FiMail } from 'react-icons/fi';
import { joinWaitlist } from '../_lib/actions';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from '@/components/ui/form';
import { Alert, AlertDescription } from '@/components/ui/alert';

// Email validation schema
const waitlistSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

type WaitlistFormValues = z.infer<typeof waitlistSchema>;

export default function WaitlistForm() {
  const [success, setSuccess] = useState(false);
  
  // Initialize form with react-hook-form and zod validation
  const form = useForm<WaitlistFormValues>({
    resolver: zodResolver(waitlistSchema),
    defaultValues: {
      email: '',
    },
  });
  
  const { formState } = form;
  const isSubmitting = formState.isSubmitting;
  
  // Handle form submission
  const onSubmit = async (data: WaitlistFormValues) => {
    try {
      const result = await joinWaitlist(data.email);
      
      if (result.success) {
        setSuccess(true);
        form.reset();
      } else {
        form.setError('email', { 
          type: 'manual', 
          message: result.message 
        });
      }
    } catch (error) {
      console.error('Waitlist submission error:', error);
      form.setError('email', { 
        type: 'manual', 
        message: 'Something went wrong. Please try again.' 
      });
    }
  };

  if (success) {
    return (
      <Alert className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-green-200 dark:border-green-800">
        <AlertDescription className="text-center py-2">
          Thank you for joining our waitlist! We'll notify you when we launch.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="relative">
                  <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
                    <FiMail className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <Input 
                    placeholder="Enter your email" 
                    type="email"
                    autoComplete="email"
                    className="pl-10 h-10 text-base"
                    {...field} 
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        
        <Button 
          type="submit" 
          disabled={isSubmitting}
          className="w-full font-medium"
        >
          {isSubmitting ? (
            <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
          ) : null}
          Join Waitlist <FiArrowRight className="ml-2" />
        </Button>
      </form>
    </Form>
  );
} 