import { ipcMain } from 'electron';
import { TextReplacementRepository } from './repository';
import { TextReplacement } from './types';
import { backendEvents } from '../../../python/main';

const textReplacementRepository = new TextReplacementRepository();

/**
 * Sets up all text replacement-related IPC handlers
 */
export function setupTextReplacementIPCHandlers(): void {
  
  // Set up Python event handling for usage tracking
  backendEvents.on('rule_usage_update', (data) => {
    if (data.rule_type === 'text_replacement' && data.rule_id) {
      const success = textReplacementRepository.incrementUsage(data.rule_id);
      if (success) {
        console.log(`Updated usage for text replacement rule: ${data.rule_id}`);
      } else {
        console.warn(`Failed to update usage for text replacement rule: ${data.rule_id}`);
      }
    }
  });

  // Get all text replacements
  ipcMain.handle('text-replacements:get-all', async (): Promise<TextReplacement[]> => {
    try {
      return textReplacementRepository.findAll();
    } catch (error) {
      console.error('Error getting all text replacements:', error);
      return [];
    }
  });

  // Get text replacement by ID
  ipcMain.handle('text-replacements:get-by-id', async (_event, id: string): Promise<TextReplacement | null> => {
    try {
      return textReplacementRepository.findById(id);
    } catch (error) {
      console.error('Error getting text replacement by ID:', error);
      return null;
    }
  });

  // Get text replacements by mode
  ipcMain.handle('text-replacements:get-by-mode', async (_event, modeId: string): Promise<TextReplacement[]> => {
    try {
      return textReplacementRepository.findByMode(modeId);
    } catch (error) {
      console.error('Error getting text replacements by mode:', error);
      return [];
    }
  });

  // Get text replacements by order
  ipcMain.handle('text-replacements:get-by-order', async (_event, modeId?: string): Promise<TextReplacement[]> => {
    try {
      return textReplacementRepository.getActiveByOrder(modeId);
    } catch (error) {
      console.error('Error getting text replacements by order:', error);
      return [];
    }
  });

  // Search text replacements
  ipcMain.handle('text-replacements:search', async (_event, searchTerm: string): Promise<TextReplacement[]> => {
    try {
      return textReplacementRepository.searchByOriginal(searchTerm);
    } catch (error) {
      console.error('Error searching text replacements:', error);
      return [];
    }
  });

  // Get regex replacements
  ipcMain.handle('text-replacements:get-regex', async (_event, modeId?: string): Promise<TextReplacement[]> => {
    try {
      return textReplacementRepository.getRegexReplacements(modeId);
    } catch (error) {
      console.error('Error getting regex replacements:', error);
      return [];
    }
  });

  // Get literal replacements
  ipcMain.handle('text-replacements:get-literal', async (_event, modeId?: string): Promise<TextReplacement[]> => {
    try {
      return textReplacementRepository.getLiteralReplacements(modeId);
    } catch (error) {
      console.error('Error getting literal replacements:', error);
      return [];
    }
  });

  // Create new text replacement
  ipcMain.handle('text-replacements:create', async (_event, data: Omit<TextReplacement, 'id'>): Promise<TextReplacement | null> => {
    try {
      // Validate regex pattern if it's a regex replacement
      if (data.isRegex) {
        const validation = textReplacementRepository.validateRegexPattern(data.original);
        if (!validation.valid) {
          console.error('Invalid regex pattern:', validation.error);
          return null;
        }
      }
      
      return textReplacementRepository.create(data);
    } catch (error) {
      console.error('Error creating text replacement:', error);
      return null;
    }
  });

  // Update text replacement
  ipcMain.handle('text-replacements:update', async (_event, id: string, data: Partial<Omit<TextReplacement, 'id'>>): Promise<TextReplacement | null> => {
    try {
      // Validate regex pattern if updating to regex
      if (data.isRegex && data.original) {
        const validation = textReplacementRepository.validateRegexPattern(data.original);
        if (!validation.valid) {
          console.error('Invalid regex pattern:', validation.error);
          return null;
        }
      }
      
      return textReplacementRepository.update(id, data);
    } catch (error) {
      console.error('Error updating text replacement:', error);
      return null;
    }
  });

  // Delete text replacement
  ipcMain.handle('text-replacements:delete', async (_event, id: string): Promise<boolean> => {
    try {
      return textReplacementRepository.delete(id);
    } catch (error) {
      console.error('Error deleting text replacement:', error);
      return false;
    }
  });

  // Validate regex pattern
  ipcMain.handle('text-replacements:validate-regex', async (_event, pattern: string): Promise<{ valid: boolean; error?: string }> => {
    try {
      return textReplacementRepository.validateRegexPattern(pattern);
    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : 'Unknown validation error'
      };
    }
  });

  // Bulk import text replacements
  ipcMain.handle('text-replacements:bulk-import', async (_event, items: Omit<TextReplacement, 'id'>[]): Promise<boolean> => {
    try {
      return textReplacementRepository.bulkImport(items);
    } catch (error) {
      console.error('Error bulk importing text replacements:', error);
      return false;
    }
  });

  // Increment usage count (manual)
  ipcMain.handle('text-replacements:increment-usage', async (_event, id: string): Promise<boolean> => {
    try {
      return textReplacementRepository.incrementUsage(id);
    } catch (error) {
      console.error('Error incrementing text replacement usage:', error);
      return false;
    }
  });

  // Get usage analytics
  ipcMain.handle('text-replacements:get-usage-analytics', async () => {
    try {
      return textReplacementRepository.getUsageAnalytics();
    } catch (error) {
      console.error('Error getting text replacement usage analytics:', error);
      return {
        totalUsage: 0,
        averageFrequency: 0,
        mostFrequentRules: [],
        recentlyUsedRules: [],
        unusedRules: []
      };
    }
  });

  // Get text replacement statistics
  ipcMain.handle('text-replacements:get-statistics', async () => {
    try {
      return textReplacementRepository.getStatistics();
    } catch (error) {
      console.error('Error getting text replacement statistics:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        regex: 0,
        literal: 0,
        byMode: {},
        mostUsed: []
      };
    }
  });

  console.log('Text replacement IPC handlers registered');
} 