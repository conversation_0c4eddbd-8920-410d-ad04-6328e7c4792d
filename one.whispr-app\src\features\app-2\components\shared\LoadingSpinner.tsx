/**
 * Loading Spinner
 * Reusable loading indicator component matching app-latest design
 * Uses HiArrowPath icon with consistent sizing and animations
 */

import { HiArrowPath } from 'react-icons/hi2';
import { cn } from '@src/lib/utils';

// ============================================================================
// TYPES
// ============================================================================

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  color?: 'primary' | 'muted' | 'accent';
}

// ============================================================================
// COMPONENT
// ============================================================================

export function LoadingSpinner({ 
  size = 'md', 
  className,
  color = 'primary'
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const colorClasses = {
    primary: 'text-primary',
    muted: 'text-muted-foreground',
    accent: 'text-accent-foreground',
  };

  return (
    <HiArrowPath 
      className={cn(
        'animate-spin',
        sizeClasses[size],
        colorClasses[color],
        className
      )}
    />
  );
}

export default LoadingSpinner; 