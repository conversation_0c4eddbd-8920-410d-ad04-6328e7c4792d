/**
 * App-2 Main Application Component
 * Basic test implementation to run the AuthPage
 */

import React, { useState, useEffect } from 'react';
import { AuthProvider } from './contexts/AuthContext';
import { ModelProvider } from './contexts/ModelContext';
import { AudioProvider } from './contexts/AudioContext';
import { ShortcutProvider } from './contexts/ShortcutContext';
import { TryItProvider } from './contexts/TryItContext';
import { SetupFlowProvider } from './contexts/SetupFlowContext';
import { AuthPage } from './pages/AuthPage';
import { ModelPage } from './pages/ModelPage';
import { AudioPage } from './pages/AudioPage';
import { ShortcutPage } from './pages/ShortcutPage';
import { TryItPage } from './pages/TryItPage';
import { FloatingProgress } from './components/layout/FloatingProgress';
import { SetupLayout } from './components/layout/SetupLayout';
import { ContentContainer } from './components/layout/ContentContainer';
import type { SetupStep } from './types/core';

// ============================================================================
// CONSTANTS
// ============================================================================

const STEPS: SetupStep[] = ['auth', 'model', 'audio', 'shortcut', 'tryit'];

// ============================================================================
// MAIN APP COMPONENT
// ============================================================================

export function App() {
  const [currentStep, setCurrentStep] = useState<SetupStep>('auth');
  const [completedSteps, setCompletedSteps] = useState<Set<SetupStep>>(new Set());
  const [previousStep, setPreviousStep] = useState<SetupStep | null>(null);

  const currentStepIndex = STEPS.indexOf(currentStep);

  // Track step changes for debugging
  useEffect(() => {
    if (previousStep) {
      console.log(`📊 Step transition: ${previousStep} → ${currentStep}`);
    }
    setPreviousStep(currentStep);
  }, [currentStep, previousStep]);

  const goToStep = (step: SetupStep) => {
    console.log(`🔄 Navigating directly to step: ${step}`);
    setCurrentStep(step);
  };

  const handleNext = () => {
    console.log('Next step requested from:', currentStep);
    const nextIndex = currentStepIndex + 1;
    if (nextIndex < STEPS.length) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(STEPS[nextIndex]);
    } else {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      console.log('Setup flow completed!');
    }
  };

  const handlePrevious = () => {
    console.log('Previous step requested from:', currentStep);
    const prevIndex = currentStepIndex - 1;
    if (prevIndex >= 0) {
      setCurrentStep(STEPS[prevIndex]);
    }
  };

  return (
    <SetupFlowProvider>
      <AuthProvider>
        <ModelProvider>
          <AudioProvider>
            <ShortcutProvider>
              <TryItProvider>
                <div className="h-screen w-full flex flex-col bg-background relative">
                  {/* Floating Progress Bar - Fixed at top */}
                  <div className="absolute top-0 left-0 right-0 z-50 bg-card">
                    <div className="max-w-4xl mx-auto px-6 py-1">
                      <FloatingProgress
                        steps={STEPS}
                        currentStep={currentStep}
                        completedSteps={completedSteps}
                        onStepClick={goToStep}
                      />
                    </div>
                  </div>

                  {/* Step Content - Full height with top padding for progress bar */}
                  <div className="h-full bg-sidebar">
                    <div className="h-[calc(100%-40px)] bg-background rounded-2xl overflow-hidden relative">
                      {/* Setup Layout Wrapper - Common to all pages */}
                      <SetupLayout>
                        <ContentContainer maxWidth="xl" centered>
                          {/* Preloaded Pages - All rendered but only one visible */}
                          
                          {/* Auth Page */}
                          <div 
                            className={`absolute inset-0 transition-opacity duration-200 ${
                              currentStep === 'auth' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
                            }`}
                          >
                            <AuthPage 
                              onNext={handleNext}
                              onPrevious={currentStepIndex > 0 ? handlePrevious : undefined}
                            />
                          </div>
                          
                          {/* Model Page */}
                          <div 
                            className={`absolute inset-0 transition-opacity duration-200 ${
                              currentStep === 'model' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
                            }`}
                          >
                            <ModelPage 
                              onNext={handleNext}
                              onPrevious={currentStepIndex > 0 ? handlePrevious : undefined}
                            />
                          </div>
                          
                          {/* Audio Page */}
                          <div 
                            className={`absolute inset-0 transition-opacity duration-200 ${
                              currentStep === 'audio' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
                            }`}
                          >
                            <AudioPage 
                              onNext={handleNext}
                              onPrevious={currentStepIndex > 0 ? handlePrevious : undefined}
                              isActive={currentStep === 'audio'}
                            />
                          </div>
                          
                          {/* Shortcut Page */}
                          <div 
                            className={`absolute inset-0 transition-opacity duration-200 ${
                              currentStep === 'shortcut' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
                            }`}
                          >
                            <ShortcutPage 
                              onNext={handleNext}
                              onPrevious={currentStepIndex > 0 ? handlePrevious : undefined}
                              isActive={currentStep === 'shortcut'}
                            />
                          </div>
                          
                          {/* TryIt Page */}
                          <div 
                            className={`absolute inset-0 transition-opacity duration-200 ${
                              currentStep === 'tryit' ? 'opacity-100 z-10' : 'opacity-0 z-0 pointer-events-none'
                            }`}
                          >
                            <TryItPage 
                              onNext={handleNext}
                              onPrevious={currentStepIndex > 0 ? handlePrevious : undefined}
                              isActive={currentStep === 'tryit'}
                            />
                          </div>
                        </ContentContainer>
                      </SetupLayout>
                    </div>
                  </div>
                </div>
              </TryItProvider>
            </ShortcutProvider>
          </AudioProvider>
        </ModelProvider>
      </AuthProvider>
    </SetupFlowProvider>
  );
}

export default App; 