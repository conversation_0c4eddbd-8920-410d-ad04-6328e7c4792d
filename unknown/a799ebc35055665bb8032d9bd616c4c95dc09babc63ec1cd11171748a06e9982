/**
 * Shortcut Display
 * Component for displaying and configuring keyboard shortcuts
 * Uses existing UI components (KeyCombo, KeySymbol) to match app-latest patterns
 */

import React from 'react';
import { Button } from '@src/components/ui/button';
import { KeyCombo } from '@src/components/ui/key-combo';
import { KeySymbol } from '@src/components/ui/key-symbol';
import { HiMicrophone, HiArrowUturnLeft } from 'react-icons/hi2';
import type { RecordingMode } from '../../types/core';
import type { Shortcuts } from '../../types/shortcut';

// ============================================================================
// TYPES
// ============================================================================

interface ShortcutDisplayProps {
  shortcuts: Shortcuts;
  recordingMode: RecordingMode;
  recordingShortcut: string | null;
  tempKeys: string[];
  onEditShortcut: (shortcutType: keyof Shortcuts) => void;
  pressedKeys?: Set<string>;
  className?: string;
}

interface ShortcutConfigItemProps {
  label: string;
  keys: string[];
  onEdit: () => void;
  isRecording: boolean;
  isBeingRecorded: boolean;
  tempKeys: string[];
}

interface ShortcutComboProps {
  keys: string[];
  pressedKeys: Set<string>;
  label: string;
  isRecording?: boolean;
  tempKeys?: string[];
}

// ============================================================================
// COMPONENTS
// ============================================================================

// Shortcut key combo display with live press detection
function ShortcutCombo({ 
  keys, 
  pressedKeys, 
  label, 
  isRecording = false, 
  tempKeys = [] 
}: ShortcutComboProps) {
  // Use tempKeys when recording, otherwise use the configured keys
  const displayKeys = isRecording ? tempKeys : keys;
  const isEmpty = displayKeys.length === 0;
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <HiMicrophone className={`w-5 h-5 ${isRecording ? 'text-orange-500' : 'text-primary'}`} />
        <span className={`text-base font-medium ${isRecording ? 'text-orange-600' : ''}`}>
          {label}
          {isRecording && <span className="ml-2 text-sm">(Recording...)</span>}
        </span>
      </div>
      
      <div className={`
        p-6 rounded-lg border-2 min-h-[100px] flex items-center justify-center
        ${isRecording 
          ? 'bg-orange-50 dark:bg-orange-950/20 border-2 border-orange-200 dark:border-orange-800' 
          : 'bg-muted/20'
        }`}>
        {!isEmpty ? (
          displayKeys.map((keyName, index) => {
            // Direct key matching - Python sends us normalized key names
            const isPressed = pressedKeys.has(keyName);
            
            return (
              <React.Fragment key={keyName}>
                {index > 0 && (
                  <span className={`mx-2 text-2xl font-bold ${isRecording ? 'text-orange-600' : 'text-muted-foreground'}`}>+</span>
                )}
                <div className="transition-all duration-200">
                  <KeySymbol 
                    keyName={keyName}
                    size="setup"
                    className={`
                      transition-all duration-75 font-semibold
                      ${isPressed
                        ? 'border-4 border-green-400 bg-gradient-to-br from-green-400 to-green-500 text-white scale-110 shadow-xl shadow-green-400/50 ring-4 ring-green-300/50' 
                        : isRecording
                        ? 'border-2 border-orange-400 bg-orange-100 text-orange-700'
                        : 'border-2 border-primary-300 bg-primary-50 text-primary-700 hover:border-primary-400'
                      }
                    `}
                  />
                </div>
              </React.Fragment>
            );
          })
        ) : (
          <div className={`text-base ${isRecording ? 'text-orange-600 dark:text-orange-400' : 'text-muted-foreground'}`}>
            {isRecording ? 'Press keys to create your shortcut...' : 'No shortcut configured'}
          </div>
        )}
      </div>
    </div>
  );
}

// Individual shortcut configuration component
function ShortcutConfigItem({
  label,
  keys,
  onEdit,
  isRecording,
  isBeingRecorded,
  tempKeys
}: ShortcutConfigItemProps) {
  // Use temp keys when this specific shortcut is being recorded
  const displayKeys = isBeingRecorded ? tempKeys : keys;
  const hasKeys = displayKeys.length > 0;
  
  return (
    <div className="flex items-center justify-between p-4 border rounded-lg bg-muted/20">
      <span className={`text-sm font-medium ${isBeingRecorded ? 'text-orange-600' : ''}`}>
        {label}
        {isBeingRecorded && <span className="ml-1 text-xs">(Recording...)</span>}
      </span>
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          {hasKeys ? (
            <KeyCombo keyNames={displayKeys} size="md" className="gap-2" />
          ) : (
            <span className={`text-sm ${isBeingRecorded ? 'text-orange-500' : 'text-muted-foreground'}`}>
              {isBeingRecorded ? 'Press keys...' : 'Not set'}
            </span>
          )}
        </div>
        <Button 
          onClick={onEdit} 
          variant="ghost" 
          size="sm"
          disabled={isRecording}
          className="gap-1 hover:bg-muted/50"
        >
          <HiArrowUturnLeft className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}

// ============================================================================
// MAIN COMPONENT
// ============================================================================

export function ShortcutDisplay({
  shortcuts,
  recordingMode,
  recordingShortcut,
  tempKeys,
  onEditShortcut,
  pressedKeys = new Set(),
  className
}: ShortcutDisplayProps) {
  const isRecording = recordingShortcut !== null;

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Key Combinations Display */}
      {recordingMode === 'pushToTalk' ? (
        // Push-to-talk mode shortcuts
        <ShortcutCombo
          keys={shortcuts.pushToTalk}
          pressedKeys={pressedKeys}
          label="Push-to-Talk"
          isRecording={recordingShortcut === 'pushToTalk'}
          tempKeys={tempKeys}
        />
      ) : (
        // Toggle mode shortcuts
        <>
          <ShortcutCombo
            keys={shortcuts.toggle}
            pressedKeys={pressedKeys}
            label="Toggle Recording"
            isRecording={recordingShortcut === 'toggle'}
            tempKeys={tempKeys}
          />
          <ShortcutCombo
            keys={shortcuts.cancel}
            pressedKeys={pressedKeys}
            label="Cancel Recording"
            isRecording={false} // Cancel is never being recorded directly
            tempKeys={[]}
          />
        </>
      )}

      {/* Configuration Section */}
      <div className="space-y-4">
        <label className="text-base font-medium block">Configure Shortcuts</label>
        <div className="space-y-4">
          {recordingMode === 'pushToTalk' ? (
            // Push-to-talk mode: show only pushtotalk
            <ShortcutConfigItem
              label="Push-to-Talk"
              keys={shortcuts.pushToTalk}
              onEdit={() => onEditShortcut('pushToTalk')}
              isRecording={isRecording}
              isBeingRecorded={recordingShortcut === 'pushToTalk'}
              tempKeys={tempKeys}
            />
          ) : (
            // Toggle mode: show toggle and cancel
            <>
              <ShortcutConfigItem
                label="Toggle Recording"
                keys={shortcuts.toggle}
                onEdit={() => onEditShortcut('toggle')}
                isRecording={isRecording}
                isBeingRecorded={recordingShortcut === 'toggle'}
                tempKeys={tempKeys}
              />
              <ShortcutConfigItem
                label="Cancel Recording"
                keys={shortcuts.cancel}
                onEdit={() => onEditShortcut('cancel')}
                isRecording={isRecording}
                isBeingRecorded={recordingShortcut === 'cancel'}
                tempKeys={recordingShortcut === 'cancel' ? tempKeys : []}
              />
            </>
          )}
        </div>
      </div>

    </div>
  );
}

export default ShortcutDisplay; 