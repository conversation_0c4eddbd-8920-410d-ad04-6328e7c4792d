'use server';

import { createClient } from '@/utils/supabase/server';
import { z } from 'zod';

const waitlistSchema = z.object({
  email: z.string().email('Please enter a valid email address')
});

export async function joinWaitlist(email: string) {
  try {
    // Validate input
    const { email: validatedEmail } = waitlistSchema.parse({ email });
    
    // Get Supabase client
    const supabase = await createClient();
    
    // Insert into waitlist table
    const { error } = await supabase
      .from('waitlist')
      .insert([{ email: validatedEmail }]);
    
    if (error) {
      if (error.code === '23505') {
        return { success: false, message: 'This email is already on the waitlist!' };
      }
      console.error('Error adding to waitlist:', error);
      return { success: false, message: 'Something went wrong. Please try again.' };
    }
    
    return { success: true, message: 'Thank you for joining our waitlist!' };
  } catch (err) {
    console.error('Error in joinWaitlist:', err);
    if (err instanceof z.ZodError) {
      return { success: false, message: err.errors[0].message };
    }
    return { success: false, message: 'Something went wrong. Please try again.' };
  }
} 