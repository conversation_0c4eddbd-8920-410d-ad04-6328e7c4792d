"""
Transcription Service for One.Whispr

Orchestrates all transcription processing including vocabulary corrections,
text replacements, and dual processing modes (real-time vs batch).
"""

import asyncio
import logging
import time
import queue
import threading
from typing import Dict, List, Any, Optional, Callable

from ..core.base import ConfigurableService, Event
from ..config.manager import ConfigurationMixin
from ..helpers.transcription.transcription_engine import TranscriptionEngine, ProcessingMode
from ..helpers.audio.audio_manager import AudioConsumerType, AudioChunk

logger = logging.getLogger("whispr.transcription.service")


class TranscriptionService(ConfigurableService, ConfigurationMixin):
    """
    Primary orchestrator for all transcription processing.

    Responsibilities:
    - Dual processing modes: real-time (100ms chunks) vs batch (on stop)
    - Fresh configuration retrieval on each processing cycle
    - Comprehensive error handling with fallback to original text
    - WebSocket event emission for real-time updates
    """

    def __init__(self, service_container=None):
        """Initialize the transcription service."""
        super().__init__(service_container)
        
        # Core state
        self.is_processing = False
        self.current_session_id = None
        self.current_mode_id = None
        
        # Processing configuration (legacy - will be replaced by _service_config)
        self.processing_config = {}

        # Components (will be initialized in initialize())
        self.transcription_engine = None
        self.vocabulary_system = None
        self.text_processing_pipeline = None
        self.model_service = None
        self.audio_service = None
        
        # Processing state
        self.processing_lock = asyncio.Lock()
        self.processing_task = None
        self.processing_stop_event = asyncio.Event()
        
        # Session statistics
        self.session_stats = {
            'start_time': None,
            'chunks_processed': 0,
            'vocabulary_corrections': 0,
            'text_replacements': 0,
            'processing_time_ms': 0
        }
        
        # Service state
        self.is_initialized = False
        self.is_transcribing = False
        
        # Audio consumer callback
        self.audio_callback = None
        
        # Audio chunk processing queue
        self.audio_queue = queue.Queue(maxsize=100)  # Limit queue size to prevent memory issues
        self.processing_worker = None
        self.worker_stop_event = threading.Event()
        
        # Batch processing coordination
        self.batch_processing_requests = queue.Queue()  # For sending batch requests to worker
        self.batch_processing_results = {}  # session_id -> result future
        
        logger.info("TranscriptionService initialized")

    def _get_static_defaults(self) -> Dict[str, Any]:
        """Get static default configuration for TranscriptionService."""
        return {
            'default_model': 'openai/whisper-base'
        }

    async def initialize(self) -> bool:
        """Initialize the service."""
        try:
            logger.info("Initializing TranscriptionService...")

            # Load configuration first
            self._load_configuration()

            # Get required services
            self.model_service = self.service_container.resolve("models")
            if not self.model_service:
                logger.error("ModelService not available")
                return False

            self.audio_service = self.service_container.resolve("audio")
            if not self.audio_service:
                logger.error("AudioService not available")
                return False

            # Initialize transcription engine with dependencies
            self.transcription_engine = TranscriptionEngine(
                model_service=self.model_service,
                service_container=self.service_container
            )

            # Set up audio consumer callback
            self._setup_audio_consumer()

            # Start audio processing worker
            self._start_audio_processing_worker()

            self.is_initialized = True
            logger.info("TranscriptionService initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize TranscriptionService: {e}")
            return False
    
    async def start_processing(self, session_id: Optional[str] = None, 
                             processing_mode: Optional[ProcessingMode] = None) -> bool:
        """Start transcription processing.
        
        Args:
            session_id: Optional session identifier
            processing_mode: Optional processing mode (defaults to REAL_TIME)
            
        Returns:
            True if processing started successfully
        """
        try:
            if not self.is_initialized:
                logger.error("TranscriptionService not initialized")
                return False
            
            if self.is_transcribing:
                logger.warning("Transcription already active")
                return True
            
            # Ensure model is loaded
            if not await self._ensure_model_loaded():
                logger.error("Failed to load transcription model")
                return False
            
            # Set processing mode
            if processing_mode:
                self.transcription_engine.set_processing_mode(processing_mode)
            
            # Generate session ID and prepare configuration
            self.current_session_id = session_id or f"session_{int(time.time())}"
            
            # Get current configuration for session start event
            session_config = await self._get_session_configuration()
            
            # Start transcription engine
            engine_started = self.transcription_engine.start_processing(self.current_session_id)
            
            if not engine_started:
                logger.error("Failed to start transcription engine")
                return False
            
            # Start audio capture
            audio_started = self.audio_service.start_capture(session_id=self.current_session_id)
            if not audio_started:
                logger.error("Failed to start audio capture")
                await self.transcription_engine.stop_processing()
                return False
            
            self.is_transcribing = True
            
            # Session start event is automatically emitted by TranscriptionEngine.start_processing()
            logger.info(f"Transcription processing started (session: {self.current_session_id})")
            return True
            
        except Exception as e:
            logger.error(f"Error starting transcription processing: {e}")
            return False
    
    async def stop_processing(self, cancelled: bool = False) -> Optional[Dict[str, Any]]:
        """Stop transcription processing and return session statistics.
        
        Args:
            cancelled: Whether this is a cancellation (True) or normal stop (False)
        
        Returns:
            Dictionary with session statistics or None if not processing
        """
        try:
            if not self.is_transcribing:
                logger.warning("Transcription not currently active")
                return None
            
            # Stop transcription engine and get statistics
            # Note: Audio capture is now stopped by ShortcutService to prevent race condition
            engine_stats = await self.transcription_engine.stop_processing(cancelled)
            
            # Only set is_transcribing = False if batch processing is NOT scheduled
            # If batch processing is scheduled, the worker thread will set it to False when done
            if not (engine_stats and engine_stats.get('status') == 'batch_processing_scheduled'):
                self.is_transcribing = False
            
            if cancelled:
                # For cancellation, emit proper cancellation event
                self.transcription_engine.event_emitter.emit_error(
                    "session_cancelled", 
                    "User cancelled transcription"
                )
                
                session_id = self.current_session_id
                self.current_session_id = None
                
                logger.info(f"Transcription processing cancelled (session: {session_id})")
                return {"session_id": session_id, "cancelled": True, "cancelled_at": time.time()}
            else:
                # For normal stop, prepare session result with statistics
                session_result = {
                    "session_id": self.current_session_id,
                    "transcription_stats": engine_stats,
                    "stopped_at": time.time()
                }
                
                # Note: TranscriptionEngine._finish_session() already emits session_end event
                # No need to emit duplicate session_end event here
                
                session_id = self.current_session_id
                self.current_session_id = None
                
                logger.info(f"Transcription processing stopped. Session stats: {engine_stats}")
                return session_result
            
        except Exception as e:
            logger.error(f"Error stopping transcription processing: {e}")
            if self.transcription_engine and hasattr(self.transcription_engine, 'event_emitter'):
                self.transcription_engine.event_emitter.emit_error("STOP_ERROR", f"Error stopping transcription: {str(e)}")
            return None
    
    async def _ensure_model_loaded(self) -> bool:
        """Ensure a transcription model is loaded.
        
        Returns:
            True if model is loaded or was successfully loaded
        """
        try:
            # Check if model is already loaded
            if self.model_service._loaded_model:
                logger.debug("Transcription model already loaded")
                return True
            
            # Refresh configuration and get default model
            self._refresh_configuration()
            default_model = self.get_config('default_model', 'openai/whisper-base')
            
            logger.info(f"Loading transcription model: {default_model}")
            model_loaded = await self.model_service.load_model(default_model)
            
            if model_loaded:
                logger.info(f"Successfully loaded transcription model: {default_model}")
                return True
            else:
                logger.error(f"Failed to load transcription model: {default_model}")
                return False
                
        except Exception as e:
            logger.error(f"Error ensuring model is loaded: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get the current service status."""
        base_status = super().get_status()
        return {
            **base_status,
            "is_processing": self.is_processing,
            "current_session_id": self.current_session_id,
            "current_mode_id": self.current_mode_id,
            "is_initialized": self.is_initialized,
            "is_transcribing": self.is_transcribing,
            "model_loaded": (
                self.model_service._loaded_model is not None 
                if self.model_service else False
            ),
            "configuration": self.get_config()
        }
    
    async def cleanup(self) -> bool:
        """Clean up resources. To be implemented by subclasses."""
        try:
            logger.info("Cleaning up TranscriptionService...")
            
            if self.is_processing:
                await self.stop_processing()
            
            # Stop audio processing worker
            if self.processing_worker and self.processing_worker.is_alive():
                logger.info("Stopping audio processing worker...")
                self.worker_stop_event.set()
                
                # Wait for worker to finish, but don't wait forever
                self.processing_worker.join(timeout=2.0)
                if self.processing_worker.is_alive():
                    logger.warning("Audio processing worker did not stop gracefully")
                else:
                    logger.info("Audio processing worker stopped")
            
            # Clear the audio queue
            while not self.audio_queue.empty():
                try:
                    self.audio_queue.get_nowait()
                except queue.Empty:
                    break
            
            if self.transcription_engine:
                await self.transcription_engine.stop_processing(cancelled=True)
                self.transcription_engine = None
            
            if self.model_service:
                self.model_service = None
            
            if self.audio_service and self.audio_callback:
                # AudioService will handle consumer cleanup during its own cleanup
                pass
            
            self.is_initialized = False
            self.is_processing = False
            self.current_session_id = None
            self.current_mode_id = None
            
            logger.info("TranscriptionService cleanup completed")
            return True
            
        except Exception as e:
            logger.error(f"Failed to cleanup TranscriptionService: {e}")
            return False
    
    # Service registration methods
    def register_vocabulary_system(self, vocabulary_system):
        """Register the vocabulary system component."""
        self.vocabulary_system = vocabulary_system
        self.logger.debug("Registered VocabularySystem")
    
    def register_text_processing_pipeline(self, text_processing_pipeline):
        """Register the text processing pipeline component."""
        self.text_processing_pipeline = text_processing_pipeline
        self.logger.debug("Registered TextProcessingPipeline")
    
    # Private methods
    
    def _setup_audio_consumer(self) -> None:
        """Set up audio consumer for transcription."""
        try:
            # Create audio callback that processes AudioChunk objects
            self.audio_callback = self._on_audio_chunk
            
            # Register with AudioService
            if self.audio_service:
                self.audio_service.set_transcription_callback(self.audio_callback)
                logger.info("Audio consumer callback registered")
            else:
                logger.warning("AudioService not available for consumer setup")
                
        except Exception as e:
            logger.error(f"Error setting up audio consumer: {e}")
    
    def _start_audio_processing_worker(self) -> None:
        """Start the dedicated audio processing worker thread."""
        try:
            self.worker_stop_event.clear()
            self.processing_worker = threading.Thread(
                target=self._audio_processing_worker,
                name="TranscriptionAudioWorker",
                daemon=True
            )
            self.processing_worker.start()
            logger.info("Audio processing worker started")
        except Exception as e:
            logger.error(f"Error starting audio processing worker: {e}")
    
    def _audio_processing_worker(self) -> None:
        """Worker thread that processes audio chunks and batch requests."""
        # Create event loop for this worker thread
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        logger.info("Audio processing worker thread started")
        
        try:
            while not self.worker_stop_event.is_set():
                processed_something = False
                
                # Check for batch processing requests first (higher priority)
                try:
                    batch_request = self.batch_processing_requests.get_nowait()
                    logger.debug("Processing batch request in worker thread")
                    
                    # Process batch request asynchronously
                    result = loop.run_until_complete(self._process_batch_request(batch_request))
                    
                    # Store result for coordination with main thread
                    session_id = batch_request.get('session_id')
                    if session_id and session_id in self.batch_processing_results:
                        try:
                            self.batch_processing_results[session_id].set_result(result)
                        except Exception as e:
                            logger.error(f"Error setting batch result: {e}")
                    
                    # Set is_transcribing = False now that batch processing is complete
                    # This allows any remaining in-flight chunks to be processed
                    self.is_transcribing = False
                    logger.debug(f"Transcription marked as stopped after batch processing for session {session_id}")
                    
                    processed_something = True
                    
                except queue.Empty:
                    pass  # No batch requests
                except Exception as e:
                    logger.error(f"Error processing batch request: {e}")
                    # Set is_transcribing = False on error to prevent getting stuck
                    self.is_transcribing = False
                
                # Process regular audio chunks
                try:
                    audio_chunk = self.audio_queue.get(timeout=0.1 if not processed_something else 0.01)
                    
                    # Process the chunk asynchronously
                    loop.run_until_complete(self._process_audio_chunk(audio_chunk))
                    
                    # Mark task as done
                    self.audio_queue.task_done()
                    processed_something = True
                    
                except queue.Empty:
                    # No chunks to process
                    if not processed_something:
                        # Sleep briefly if nothing was processed
                        time.sleep(0.05)
                except Exception as e:
                    logger.error(f"Error in audio processing worker: {e}")
                    # Continue processing other chunks
                    continue
                    
        except Exception as e:
            logger.error(f"Fatal error in audio processing worker: {e}")
        finally:
            loop.close()
            logger.info("Audio processing worker stopped")
    
    def _on_audio_chunk(self, audio_chunk: AudioChunk) -> None:
        """Handle incoming audio chunks from AudioService.
        
        Args:
            audio_chunk: AudioChunk object from AudioManager
        """
        try:
            if not self.is_transcribing or not self.transcription_engine:
                return
            
            # Put chunk in queue for async processing
            # This is non-blocking and thread-safe
            try:
                self.audio_queue.put_nowait(audio_chunk)
            except queue.Full:
                # Queue is full, drop the oldest chunk and add new one
                try:
                    self.audio_queue.get_nowait()  # Remove oldest
                    self.audio_queue.put_nowait(audio_chunk)  # Add new
                    logger.debug("Audio queue full, dropped oldest chunk")
                except queue.Empty:
                    pass  # Queue was emptied by worker, try again
                except queue.Full:
                    logger.warning("Audio queue consistently full, dropping chunk")
            
        except Exception as e:
            logger.error(f"Error handling audio chunk: {e}")
    
    async def _process_audio_chunk(self, audio_chunk: AudioChunk) -> None:
        """Process audio chunk asynchronously.
        
        Args:
            audio_chunk: AudioChunk object to process
        """
        try:
            # Double-check transcription state in case it was cancelled while task was queued
            if not self.is_transcribing or not self.transcription_engine:
                logger.debug("Skipping audio chunk processing - transcription cancelled or engine unavailable")
                return
            
            # Extract audio data from chunk
            audio_data = audio_chunk.data
            
            # Prepare chunk metadata
            chunk_metadata = {
                "timestamp": audio_chunk.timestamp,
                "sample_rate": audio_chunk.sample_rate,
                "duration": audio_chunk.duration,
                "chunk_id": audio_chunk.chunk_id,
                "channels": audio_chunk.channels
            }
            
            # Process with transcription engine
            result = await self.transcription_engine.process_audio(
                audio_data=audio_data,
                session_id=self.current_session_id,
                chunk_metadata=chunk_metadata
            )
            
            if result and result.text:
                logger.debug(f"Transcription result: '{result.text}' "
                           f"(confidence: {result.confidence:.2f}, "
                           f"time: {result.processing_time_ms:.1f}ms)")
            elif result and result.error:
                # Log error for processing errors
                logger.error(f"Audio processing failed: {result.error}")
            
        except Exception as e:
            logger.error(f"Error processing audio chunk: {e}")
    
    async def _process_batch_request(self, batch_request: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process a batch processing request in the worker thread.
        
        Args:
            batch_request: Dictionary containing batch processing details
            
        Returns:
            Result dictionary or None if failed
        """
        try:
            session_id = batch_request.get('session_id')
            accumulated_audio = batch_request.get('accumulated_audio', [])
            
            if not accumulated_audio:
                logger.warning("No accumulated audio in batch request")
                return None
            
            logger.info(f"Processing batch request for session {session_id} with {len(accumulated_audio)} chunks")
            
            # Process the accumulated audio using the transcription engine
            result = await self.transcription_engine._process_accumulated_audio()
            
            # Finish the session after batch processing completes
            final_stats = self.transcription_engine._finish_session(cancelled=False)
            
            logger.info(f"Batch processing completed for session {session_id}")
            return {
                'session_id': session_id,
                'result': result,
                'final_stats': final_stats,
                'processed_chunks': len(accumulated_audio),
                'completed_at': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error processing batch request: {e}")
            return None
    
    async def _get_session_configuration(self) -> Dict[str, Any]:
        """Get current session configuration for event emission.
        
        Returns:
            Dictionary with session configuration details
        """
        try:
            # Get fresh configuration
            settings = self.get_settings()
            transcription_settings = settings.get('transcription', {})
            
            # Get active mode configuration (this is the PUSH-based data flow)
            active_mode = self.get_active_mode()
            mode_config = active_mode.get("configuration", {}) if active_mode else {}
            
            return {
                "default_model": self.get_config('default_model', 'openai/whisper-base'),
                "processing_mode": self.transcription_engine.processing_mode.value,
                "real_time_transcription": mode_config.get('realTimeTranscription', False),
                "listen_to_speaker": mode_config.get('listenToSpeaker', False),
                "mode_name": active_mode.get("name") if active_mode else None,
                "mode_id": active_mode.get("id") if active_mode else None,
                "vocabulary_enabled": bool(self.get_vocabulary_by_active_mode()),
                "text_replacements_enabled": bool(self.get_text_replacements_by_active_mode())
            }
            
        except Exception as e:
            logger.error(f"Error getting session configuration: {e}")
            return {
                "default_model": "openai/whisper-base",
                "processing_mode": "real_time",
                "error": str(e)
            }
    
 