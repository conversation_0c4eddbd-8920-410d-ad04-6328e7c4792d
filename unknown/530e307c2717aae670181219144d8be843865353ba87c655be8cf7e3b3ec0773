'use server';

import { z } from 'zod';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';

// Rate limiting constants
const REG_RATE_LIMIT_SECONDS = 60; // 1 minute between registration attempts
const REG_RATE_LIMIT_COOKIE = 'reg_last_attempt';
const REG_VERIFY_LIMIT_SECONDS = 30; // 30 seconds between verification attempts
const REG_VERIFY_LIMIT_COOKIE = 'reg_last_verify';
const REG_VERIFY_MAX_ATTEMPTS = 5; // Max verification attempts before longer cooldown
const REG_VERIFY_ATTEMPTS_COOKIE = 'reg_verify_attempts';

// Validation schemas
const userInfoSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
});

const passwordSchema = z.object({
  email: z.string().email({ message: 'Invalid email format' }),
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
  password: z.string()
    .min(6, { message: 'Password must be at least 6 characters' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one number' }),
  confirmPassword: z.string().min(1, { message: 'Please confirm your password' }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const otpSchema = z.object({
  email: z.string().email({ message: 'Invalid email format' }),
  otp: z.string().length(6, { message: 'OTP must be 6 digits' }),
});

/**
 * Process user registration information
 */
export async function processUserInfo(formData: FormData) {
  // Extract user information
  const email = formData.get('email') as string;
  const firstName = formData.get('firstName') as string;
  const lastName = formData.get('lastName') as string;
  
  const result = userInfoSchema.safeParse({ email, firstName, lastName });
  if (!result.success) {
    return { 
      success: false, 
      error: result.error.errors[0]?.message || 'Invalid user information'
    };
  }
  
  try {
    const supabase = await createClient();
    
    // Check if the email already exists using our auth_email_check view
    const { data, error } = await supabase
      .from('auth_email_check')
      .select('exists')
      .eq('email', email)
      .single();
      
    if (data?.exists) {
      return {
        success: false,
        error: 'Email address is already registered'
      };
    }
    
    // Email looks valid and not registered, proceed to password step
    return {
      success: true,
      email,
      firstName,
      lastName,
      nextStep: 'password'
    };
  } catch (error) {
    console.error('Registration info error:', error);
    return { 
      success: false, 
      error: 'An error occurred processing your information'
    };
  }
}

/**
 * Process user password creation
 */
export async function processPassword(formData: FormData) {
  // Extract password information
  const email = formData.get('email') as string;
  const firstName = formData.get('firstName') as string;
  const lastName = formData.get('lastName') as string;
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;
  
  const result = passwordSchema.safeParse({
    email, firstName, lastName, password, confirmPassword
  });
  
  if (!result.success) {
    return { 
      success: false, 
      error: result.error.errors[0]?.message || 'Invalid password information'
    };
  }
  
  try {
    // Check rate limiting for registration attempts
    const cookieStore = await cookies();
    const lastAttemptCookie = cookieStore.get(REG_RATE_LIMIT_COOKIE);
    
    if (lastAttemptCookie) {
      try {
        const lastAttemptData = JSON.parse(lastAttemptCookie.value);
        const lastAttemptTime = new Date(lastAttemptData.timestamp);
        const currentTime = new Date();
        const timeDiffSeconds = Math.floor((currentTime.getTime() - lastAttemptTime.getTime()) / 1000);
        
        // Global rate limiting regardless of email
        if (timeDiffSeconds < REG_RATE_LIMIT_SECONDS) {
          const timeLeftSeconds = REG_RATE_LIMIT_SECONDS - timeDiffSeconds;
          return {
            success: false,
            error: `Please wait ${timeLeftSeconds} seconds before trying again`
          };
        }
      } catch (e) {
        // Invalid cookie, ignore and proceed
        console.error('Invalid rate limit cookie:', e);
      }
    }
    
    const supabase = await createClient();
    
    // Set rate limiting cookie before the attempt
    const rateLimitData = {
      email,
      timestamp: new Date().toISOString()
    };
    
    cookieStore.set(REG_RATE_LIMIT_COOKIE, JSON.stringify(rateLimitData), {
      path: '/',
      maxAge: 60 * 60, // 1 hour (longer than the rate limit to ensure it's present)
      httpOnly: true,
      sameSite: 'lax',
    });
    
    // Try to sign up with the provided credentials - this will also check for duplicate emails
    // Omit emailRedirectTo to receive an OTP code instead of a magic link
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          firstName,
          lastName,
          full_name: `${firstName} ${lastName}`
        }
        // By removing emailRedirectTo, Supabase will send an OTP code
        // instead of a confirmation link
      }
    });
    
    if (error) {
      console.error('Supabase signup error:', { 
        message: error.message,
        status: error.status,
        name: error.name
      });
      
      // Check for duplicate email error messages
      if (error.message && (
          error.message.includes('already') || 
          error.message.includes('exist') ||
          error.message.includes('taken'))) {
        return {
          success: false,
          error: 'Email address is already registered'
        };
      }
      
      // Service unavailable errors
      if (error.status === 503 || error.status === 500 || 
          error.message.includes('unavailable') || 
          error.message.includes('rate limit')) {
        return {
          success: false,
          error: 'Registration service temporarily unavailable. Please try again in a few minutes.'
        };
      }
      
      return {
        success: false,
        error: error.message
      };
    }
    
    // Supabase returns identities.length = 0 for emails that are already registered
    // but may not return an explicit error in some cases
    if (data?.user?.identities?.length === 0) {
      return {
        success: false,
        error: 'Email address is already registered'
      };
    }
    
    // Reset verification attempts counter
    cookieStore.set(REG_VERIFY_ATTEMPTS_COOKIE, JSON.stringify({
      email,
      count: 0
    }), {
      path: '/',
      maxAge: 60 * 10, // 10 minutes
      httpOnly: true,
      sameSite: 'lax',
    });
    
    return {
      success: true,
      email,
      firstName,
      lastName,
      nextStep: 'otp',
      message: 'Please check your email for the verification code'
    };
  } catch (error) {
    console.error('Password processing error:', error);
    return { 
      success: false, 
      error: 'An error occurred during registration. Please try again later.'
    };
  }
}

/**
 * Verify OTP and complete registration
 */
export async function verifyRegistrationOTP(formData: FormData) {
  const email = formData.get('email') as string;
  const firstName = formData.get('firstName') as string;
  const lastName = formData.get('lastName') as string;
  const otp = formData.get('otp') as string;
  
  const result = otpSchema.safeParse({ email, otp });
  if (!result.success) {
    return { 
      success: false, 
      error: result.error.errors[0]?.message || 'Invalid OTP'
    };
  }
  
  try {
    // Check rate limiting for verification attempts
    const cookieStore = await cookies();
    const lastVerifyCookie = cookieStore.get(REG_VERIFY_LIMIT_COOKIE);
    const attemptsCookie = cookieStore.get(REG_VERIFY_ATTEMPTS_COOKIE);
    
    let attempts = 0;
    if (attemptsCookie) {
      try {
        const attemptsData = JSON.parse(attemptsCookie.value);
        if (attemptsData.email === email) {
          attempts = attemptsData.count || 0;
        }
      } catch (e) {
        console.error('Invalid attempts cookie:', e);
      }
    }

    if (lastVerifyCookie && attempts >= REG_VERIFY_MAX_ATTEMPTS) {
      try {
        const lastVerifyData = JSON.parse(lastVerifyCookie.value);
        const lastVerifyTime = new Date(lastVerifyData.timestamp);
        const currentTime = new Date();
        const timeDiffSeconds = Math.floor((currentTime.getTime() - lastVerifyTime.getTime()) / 1000);
        
        // Longer cooldown after max attempts
        if (lastVerifyData.email === email && timeDiffSeconds < REG_VERIFY_LIMIT_SECONDS * 2) {
          const timeLeftSeconds = (REG_VERIFY_LIMIT_SECONDS * 2) - timeDiffSeconds;
          return {
            success: false,
            error: `Too many attempts. Please wait ${timeLeftSeconds} seconds before trying again.`
          };
        }
      } catch (e) {
        console.error('Invalid rate limit cookie:', e);
      }
    } else if (lastVerifyCookie) {
      try {
        const lastVerifyData = JSON.parse(lastVerifyCookie.value);
        const lastVerifyTime = new Date(lastVerifyData.timestamp);
        const currentTime = new Date();
        const timeDiffSeconds = Math.floor((currentTime.getTime() - lastVerifyTime.getTime()) / 1000);
        
        // Standard cooldown
        if (lastVerifyData.email === email && timeDiffSeconds < REG_VERIFY_LIMIT_SECONDS) {
          const timeLeftSeconds = REG_VERIFY_LIMIT_SECONDS - timeDiffSeconds;
          return {
            success: false,
            error: `Please wait ${timeLeftSeconds} seconds between verification attempts`
          };
        }
      } catch (e) {
        console.error('Invalid rate limit cookie:', e);
      }
    }
    
    // Update the verification attempt timestamp
    cookieStore.set(REG_VERIFY_LIMIT_COOKIE, JSON.stringify({
      email,
      timestamp: new Date().toISOString()
    }), {
      path: '/',
      maxAge: 60 * 60, // 1 hour
      httpOnly: true,
      sameSite: 'lax',
    });
    
    // Update attempts count
    cookieStore.set(REG_VERIFY_ATTEMPTS_COOKIE, JSON.stringify({
      email,
      count: attempts + 1
    }), {
      path: '/',
      maxAge: 60 * 10, // 10 minutes
      httpOnly: true,
      sameSite: 'lax',
    });
    
    // Verify OTP with Supabase
    const supabase = await createClient();
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token: otp,
      type: 'signup'
    });
    
    if (error) {
      console.error('Registration OTP verification error:', error.message);
      
      // Check for specific error types with more precise handling
      if (error.message.toLowerCase().includes('token is invalid')) {
        return {
          success: false,
          error: 'Verification code is invalid. Please check and try again.',
        };
      }
      
      if (error.message.toLowerCase().includes('token has expired')) {
        return {
          success: false,
          error: 'Your verification code has expired. Please restart registration.',
        };
      }
      
      // General case for invalid tokens
      if (error.message.toLowerCase().includes('invalid') || error.message.toLowerCase().includes('expired')) {
        return {
          success: false,
          error: 'Verification code is no longer valid. Please try again.',
        };
      }
      
      return { 
        success: false, 
        error: error.message
      };
    }
    
    // Clear verification attempts on success
    cookieStore.set(REG_VERIFY_ATTEMPTS_COOKIE, JSON.stringify({
      email,
      count: 0
    }), {
      path: '/',
      maxAge: 60 * 10,
      httpOnly: true,
      sameSite: 'lax',
    });
    
    return { 
      success: true,
      email,
      firstName,
      lastName,
      action: 'register'
    };
  } catch (error) {
    console.error('OTP verification error:', error);
    return { 
      success: false, 
      error: 'Failed to verify registration code' 
    };
  }
}

/**
 * Handle successful registration
 */
export async function handleSuccessfulRegistration(callbackUrl?: string): Promise<void> {
  // Redirect to the callback URL or the default URL
  if (callbackUrl) {
    redirect(callbackUrl);
  } else {
    redirect('/');
  }
} 