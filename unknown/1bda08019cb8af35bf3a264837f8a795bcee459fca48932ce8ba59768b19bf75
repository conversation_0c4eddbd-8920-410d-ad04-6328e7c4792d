'use client';

import { useRouter } from 'next/navigation';
import { buildAuthParams, AuthStep } from '../utils/auth-utils';

interface NavigationOptions {
  preserveParams?: boolean;
  additionalParams?: Record<string, string>;
}

/**
 * Custom hook for handling authentication flow navigation
 */
export function useAuthNavigation() {
  const router = useRouter();
  
  /**
   * Navigate to the next step in an auth flow
   */
  const navigateToStep = (
    flow: 'login' | 'register' | 'forgot-password',
    step: AuthStep,
    params: Record<string, string | null | undefined> = {},
    options: NavigationOptions = {}
  ) => {
    // Build URL parameters
    const searchParams = buildAuthParams({
      step,
      ...params,
    });
    
    // Navigate to the next step
    router.push(`/${flow}?${searchParams.toString()}`);
  };
  
  /**
   * Navigate to the login flow
   */
  const navigateToLogin = (
    step: AuthStep = 'email',
    params: Record<string, string | null | undefined> = {}
  ) => {
    navigateToStep('login', step, params);
  };
  
  /**
   * Navigate to the registration flow
   */
  const navigateToRegister = (
    step: AuthStep = 'info',
    params: Record<string, string | null | undefined> = {}
  ) => {
    navigateToStep('register', step, params);
  };
  
  /**
   * Navigate to the password reset flow
   */
  const navigateToPasswordReset = (
    step: AuthStep = 'email',
    params: Record<string, string | null | undefined> = {}
  ) => {
    navigateToStep('forgot-password', step, params);
  };
  
  /**
   * Handle successful authentication
   * Always redirects to the success page to ensure all scenarios are properly handled
   */
  const handleAuthSuccess = (
    sessionStatus: string,
    email: string,
    action: 'login' | 'register',
    callbackUrl?: string | null
  ) => {
    // Build redirect parameters for success
    const redirectParams = new URLSearchParams();
    redirectParams.set('email', email);
    redirectParams.set('action', action);
    
    // Only include callbackUrl if it exists
    if (callbackUrl) {
      redirectParams.set('callbackUrl', callbackUrl);
    }
    
    // Always redirect to success to handle all authentication scenarios
    router.push(`/success?${redirectParams.toString()}`);
  };
  
  return {
    navigateToStep,
    navigateToLogin,
    navigateToRegister,
    navigateToPasswordReset,
    handleAuthSuccess,
  };
} 