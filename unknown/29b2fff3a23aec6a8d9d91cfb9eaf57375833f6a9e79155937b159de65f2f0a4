/**
 * Model Card
 * Complete model display component matching app-latest ModelStep design
 * Shows model info, status, progress tracking, and handles selection
 */

import React from 'react';
import { Badge } from '@src/components/ui/badge';
import { Progress } from '@src/components/ui/progress';
import { 
  Hi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  Hi<PERSON>puChip
} from 'react-icons/hi2';
import { StatusBadge } from '../shared/StatusBadge';
import type { VoiceModel, DownloadProgress, LoadingProgress } from '../../types/model';

// ============================================================================
// TYPES
// ============================================================================

interface ModelCardProps {
  model: VoiceModel;
  isSelected: boolean;
  onSelect: (model: VoiceModel) => void;
  downloadProgress?: DownloadProgress | null;
  loadingProgress?: LoadingProgress | null;
  disabled?: boolean;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function ModelCard({
  model,
  isSelected,
  onSelect,
  downloadProgress,
  loadingProgress,
  disabled = false,
  className
}: ModelCardProps) {
  const isCurrentlyDownloading = downloadProgress?.modelId === model.id;
  const isCurrentlyLoading = loadingProgress?.modelId === model.id;
  const isDisabled = disabled || (downloadProgress && downloadProgress.modelId !== model.id) || 
                    (loadingProgress && loadingProgress.modelId !== model.id);

  const getStatusBadgeType = () => {
    if (isCurrentlyLoading) return 'loading';
    if (isCurrentlyDownloading) return 'downloading';
    if (model.isLoaded) return 'ready';
    if (model.isDownloaded) return 'downloaded';
    return 'not-downloaded';
  };

  const getProgressLabel = () => {
    if (loadingProgress?.stage) {
      switch (loadingProgress.stage) {
        case 'Initializing libraries': return 'Preparing ML libraries...';
        case 'Loading processor': return 'Loading tokenizer...';
        case 'Loading model weights': return 'Loading neural network...';
        case 'Moving to device': return 'Optimizing for hardware...';
        default: return loadingProgress.stage;
      }
    }
    return 'Loading model...';
  };

  return (
    <div 
      className={`border rounded-xl p-4 transition-all duration-300 cursor-pointer relative overflow-hidden ${
        isDisabled 
          ? 'opacity-50 cursor-not-allowed border-muted bg-muted/20' 
          : model.isLoaded
            ? 'border-green-300 bg-green-50/50 dark:border-green-700 dark:bg-green-950/30 shadow-md ring-1 ring-green-200 dark:ring-green-800'
            : isSelected 
              ? 'border-primary bg-primary/5 shadow-md ring-1 ring-primary/20' 
              : 'hover:border-primary/50 hover:bg-muted/30 hover:shadow-sm'
      } ${className || ''}`}
      onClick={() => !isDisabled && onSelect(model)}
    >
      <div className="flex items-center gap-4">
        {/* Left: Selection indicator */}
        <div className="flex-shrink-0">
          {isSelected ? (
            <div className="w-6 h-6 rounded-full bg-primary flex items-center justify-center transition-all duration-300">
              <HiCheck className="w-4 h-4 text-primary-foreground" />
            </div>
          ) : (
            <div className="w-6 h-6 rounded-full border-2 border-muted-foreground/30 transition-all duration-300" />
          )}
        </div>

        {/* Center: Model info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-semibold text-base truncate">{model.name}</h3>
            {model.isLoaded && (
              <Badge className="text-xs font-medium bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 shadow-sm">
                <HiCheckCircle className="w-3 h-3 mr-1" />
                Active
              </Badge>
            )}
            {model.isRecommended && !model.isLoaded && (
              <Badge variant="default" className="text-xs font-medium">
                <HiCpuChip className="w-3 h-3 mr-1" />
                Recommended
              </Badge>
            )}
          </div>

          <p className="text-sm text-muted-foreground mb-3 line-clamp-2 leading-relaxed">
            {model.description}
          </p>

          {/* Status badge using shared StatusBadge component */}
          <div className="transition-all duration-300">
            <StatusBadge status={getStatusBadgeType()} size="sm" />
          </div>

          {/* Download progress */}
          {isCurrentlyDownloading && downloadProgress && (
            <div className="mt-3 space-y-2 duration-300">
              <div className="flex items-center justify-between text-xs">
                <span>Downloading...</span>
                <span className="font-medium">{downloadProgress.progress}%</span>
              </div>
              <Progress value={downloadProgress.progress || 0} className="h-2" />
              {downloadProgress.speed && (
                <div className="text-xs text-muted-foreground">
                  {downloadProgress.speed}
                </div>
              )}
            </div>
          )}

          {/* Loading progress */}
          {isCurrentlyLoading && loadingProgress && (
            <div className="mt-3 space-y-2 duration-300">
              <div className="flex items-center justify-between text-xs">
                <span className="text-muted-foreground">
                  {getProgressLabel()}
                </span>
                {loadingProgress.progress && (
                  <span className="font-medium text-foreground">{loadingProgress.progress}%</span>
                )}
              </div>
              {loadingProgress.progress ? (
                <Progress value={loadingProgress.progress} className="h-2" />
              ) : (
                <div className="h-2 bg-muted rounded-full overflow-hidden">
                  <div className="h-full bg-primary/60 rounded-full animate-pulse" />
                </div>
              )}
              {/* Show additional info for slow stages */}
              {loadingProgress.stage === 'Initializing libraries' && (
                <div className="text-xs text-muted-foreground">
                  This may take a moment on first run...
                </div>
              )}
            </div>
          )}
        </div>

        {/* Right: File size and status icon */}
        <div className="flex-shrink-0 flex flex-col items-center justify-center min-w-[80px]">
          <div className="text-base font-semibold text-foreground mb-1">
            {model.size}
          </div>
          <div className="flex items-center justify-center transition-all duration-300">
            {isCurrentlyLoading ? (
              <div className="w-5 h-5 text-purple-500 animate-pulse" />
            ) : isCurrentlyDownloading ? (
              <div className="w-5 h-5 text-blue-500 animate-spin" />
            ) : model.isLoaded ? (
              <HiCheckCircle className="w-5 h-5 text-green-500" />
            ) : model.isDownloaded ? (
              <HiCheck className="w-5 h-5 text-emerald-500" />
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}

export default ModelCard; 