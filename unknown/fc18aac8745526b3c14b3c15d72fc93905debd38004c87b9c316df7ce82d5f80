/**
 * Request/response handling for backend communication.
 */

import { IPCMessage, PendingRequest } from './types';
import { sendWebSocketMessage, isWebSocketConnected, backendEvents } from './websocket';

// Request state
const pendingRequests = new Map<string, PendingRequest>();

/**
 * Initialize request handling by setting up message listener
 */
export function initializeRequestHandling(): void {
  // Listen for WebSocket messages that are responses to requests
  backendEvents.on('message', (message: IPCMessage) => {
    if (message.id && pendingRequests.has(message.id)) {
      // This is a response to a request
      const { resolve, reject, timeout } = pendingRequests.get(message.id)!;
      clearTimeout(timeout);
      pendingRequests.delete(message.id);
      
      if (message.error) {
        reject(new Error(message.error));
      } else {
        resolve(message.result || message.data);
      }
    }
  });
}

/**
 * Send a request to the backend
 */
export async function sendRequest(
  command: string, 
  params: any = {}, 
  timeout: number = 30000
): Promise<any> {
  if (!isWebSocketConnected()) {
    throw new Error('Not connected to backend');
  }
  
  return new Promise((resolve, reject) => {
    const id = `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    const request: IPCMessage = {
      id,
      type: 'request',
      command,
      params,
    };
    
    const timeoutId = setTimeout(() => {
      if (pendingRequests.has(id)) {
        pendingRequests.delete(id);
        reject(new Error(`Request timed out: ${command}`));
      }
    }, timeout);
    
    pendingRequests.set(id, { resolve, reject, timeout: timeoutId });
    
    sendWebSocketMessage(request).catch((error) => {
      clearTimeout(timeoutId);
      pendingRequests.delete(id);
      reject(error);
    });
  });
}

/**
 * Send an event to the backend
 */
export function sendEvent(event: string, data: any = {}): void {
  if (!isWebSocketConnected()) {
    console.error('Cannot send event: not connected to backend');
    return;
  }
  
  const message: IPCMessage = {
    type: 'event',
    event,
    data,
  };
  
  sendWebSocketMessage(message).catch((error) => {
    console.error('Error sending event:', error);
  });
}

/**
 * Clear all pending requests
 */
export function clearPendingRequests(): void {
  for (const { reject, timeout } of pendingRequests.values()) {
    clearTimeout(timeout);
    reject(new Error('Backend terminated'));
  }
  pendingRequests.clear();
} 