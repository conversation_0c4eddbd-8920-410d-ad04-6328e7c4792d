/**
 * TypeScript declarations for Electron IPC API
 */
interface ElectronAPI {
  ipcRenderer: {
    /**
     * Send a message to the main process
     */
    send: (channel: string, ...args: any[]) => void;
    
    /**
     * Invoke a method in the main process and return result
     */
    invoke: <T = any>(channel: string, ...args: any[]) => Promise<T>;
    
    /**
     * Listen for events from main process
     * @returns Function to remove the listener
     */
    on: (channel: string, listener: (...args: any[]) => void) => () => void;
    
    /**
     * Remove all listeners for a specific channel
     */
    removeAllListeners: (channel: string) => void;
    
    /**
     * Listen for one message and then remove listener
     */
    once: (channel: string, listener: (...args: any[]) => void) => void;
  };
}

declare global {
  interface Window {
    electron?: ElectronAPI;
  }
}

export {}; 