"""Real-time segment management with sliding window for transcription."""

import logging
import time
from typing import Dict, Any, Optional, List, Tuple
import numpy as np
from collections import deque

logger = logging.getLogger(__name__)


class SegmentResult:
    """Result from processing a segment chunk."""
    
    def __init__(self, text: str, confidence: float, metadata: Dict[str, Any]):
        self.text = text
        self.confidence = confidence
        self.metadata = metadata


class RealtimeSegmentManager:
    """Manages sliding window segments for real-time transcription."""
    
    def __init__(self, 
                 sliding_window_duration: float = 10.0, 
                 stable_threshold: float = 3.0,
                 target_sample_rate: int = 16000):
        """Initialize segment manager with sliding window approach.
        
        Args:
            sliding_window_duration: Duration in seconds for sliding window (default: 10s)
            stable_threshold: Seconds after which text is considered stable (default: 3s)
            target_sample_rate: Audio sample rate for calculations
        """
        self.sliding_window_duration = sliding_window_duration
        self.stable_threshold = stable_threshold
        self.target_sample_rate = target_sample_rate
        
        # Calculate maximum samples in window
        self.max_samples_in_window = int(sliding_window_duration * target_sample_rate)
        
        # Sliding window state
        self.audio_window: deque = deque()  # Store (audio_chunk, timestamp) tuples
        self.window_start_time: Optional[float] = None
        
        # Current segment state
        self.current_segment_id: Optional[str] = None
        self.segment_counter = 0
        
        # Note: Text tracking moved to TextAccumulator class
        # RealtimeSegmentManager now focuses only on audio window management
        
        # Chunk counting
        self.total_chunks_processed = 0
        
        logger.info(f"RealtimeSegmentManager initialized: {sliding_window_duration}s sliding window, "
                   f"{stable_threshold}s stable threshold, max {self.max_samples_in_window} samples")
    
    def start_new_session(self, session_id: str) -> None:
        """Start a new transcription session.
        
        Args:
            session_id: Session identifier
        """
        self.reset()
        current_time = time.time()
        self.window_start_time = current_time
        self.segment_counter = 1
        self.current_segment_id = f"{session_id}_segment_{self.segment_counter}"
        
        logger.info(f"Started new sliding window session: {self.current_segment_id}")
    
    def add_audio_chunk(self, audio_data: np.ndarray, session_id: str) -> Dict[str, Any]:
        """Add new audio chunk to sliding window.
        
        Args:
            audio_data: New audio chunk to add
            session_id: Session identifier
            
        Returns:
            Dictionary with window status and accumulated audio for processing
        """
        if self.window_start_time is None:
            self.start_new_session(session_id)
        
        current_time = time.time()
        
        # Add new chunk to window
        self.audio_window.append((audio_data, current_time))
        self.total_chunks_processed += 1
        
        # Remove old chunks that fall outside the sliding window
        window_cutoff_time = current_time - self.sliding_window_duration
        while self.audio_window and self.audio_window[0][1] < window_cutoff_time:
            self.audio_window.popleft()
        
        # Update window start time to first chunk in current window
        if self.audio_window:
            self.window_start_time = self.audio_window[0][1]
        
        # Create accumulated audio from current window
        current_window_audio = [chunk for chunk, _ in self.audio_window]
        accumulated_audio = np.concatenate(current_window_audio) if current_window_audio else None
        
        # Calculate window statistics
        window_duration = current_time - self.window_start_time if self.window_start_time else 0.0
        window_sample_count = len(accumulated_audio) if accumulated_audio is not None else 0
        
        return {
            'action': 'process_window',
            'segment_id': self.current_segment_id,
            'window_duration': window_duration,
            'window_chunks': len(self.audio_window),
            'window_samples': window_sample_count,
            'accumulated_audio': accumulated_audio,
            'total_chunks_processed': self.total_chunks_processed
        }
    
    def get_window_metadata(self) -> Dict[str, Any]:
        """Get metadata about the current audio window.
        
        Returns:
            Metadata for the current window state
        """
        current_time = time.time()
        window_duration = current_time - self.window_start_time if self.window_start_time else 0.0
        
        return {
            'segment_id': self.current_segment_id,
            'window_duration': window_duration,
            'chunks_count': len(self.audio_window),
            'total_chunks_processed': self.total_chunks_processed,
            'window_type': 'sliding',
            'sliding_window_duration': self.sliding_window_duration,
            'stable_threshold': self.stable_threshold
        }
    
    def finalize_current_session(self) -> Optional[Dict[str, Any]]:
        """Finalize the current session (called on session end).
        
        Returns:
            Finalization metadata or None if no active session
        """
        if not self.current_segment_id:
            return None
        
        current_time = time.time()
        window_duration = current_time - self.window_start_time if self.window_start_time else 0.0
        
        return {
            'segment_id': self.current_segment_id,
            'window_duration': window_duration,
            'is_segment_final': True,
            'changes': {'type': 'finalized'},
            'chunks_count': len(self.audio_window),
            'total_chunks_processed': self.total_chunks_processed,
            'window_type': 'sliding'
        }
    
    def reset(self) -> None:
        """Reset all sliding window state."""
        self.audio_window.clear()
        self.window_start_time = None
        self.current_segment_id = None
        self.segment_counter = 0
        # Text tracking removed - handled by TextAccumulator
        self.total_chunks_processed = 0
        
        logger.debug("Sliding window segment manager reset")
    
    # Text analysis methods removed - now handled by TextAccumulator class
    
    def get_status(self) -> Dict[str, Any]:
        """Get current status of the sliding window manager.
        
        Returns:
            Dictionary with current status information
        """
        current_time = time.time()
        window_duration = current_time - self.window_start_time if self.window_start_time else 0.0
        
        return {
            'segment_id': self.current_segment_id,
            'window_duration': window_duration,
            'sliding_window_duration': self.sliding_window_duration,
            'chunks_in_window': len(self.audio_window),
            'total_chunks_processed': self.total_chunks_processed,
            'max_samples_in_window': self.max_samples_in_window,
            'stable_threshold': self.stable_threshold,
            'window_type': 'sliding'
        } 