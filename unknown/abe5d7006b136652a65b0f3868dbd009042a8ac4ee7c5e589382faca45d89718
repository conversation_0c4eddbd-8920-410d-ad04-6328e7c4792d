/**
 * Setup Flow Types
 * Types related to step management, flow control, and navigation
 */

import type { SetupStep } from './core';

// ============================================================================
// SETUP FLOW TYPES
// ============================================================================

export interface StepConfig {
  id: SetupStep;
  title: string;
  description: string;
  canSkip: boolean;
  isActive: boolean;
  isCompleted: boolean;
  hasLifecycle: boolean; // Whether step needs enter/exit handling
}

export interface SetupFlowState {
  currentStep: SetupStep;
  completedSteps: Set<SetupStep>;
  steps: StepConfig[];
  canProceed: boolean;
  loading: boolean;
  error: string | null;
}

export interface SetupFlowActions {
  goToStep: (step: SetupStep) => void;
  goNext: () => void;
  goPrevious: () => void;
  markStepCompleted: (step: SetupStep) => void;
  setCanProceed: (canProceed: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export interface SetupFlowContextValue {
  state: SetupFlowState;
  actions: SetupFlowActions;
  
  // Computed values
  currentStepIndex: number;
  totalSteps: number;
  progress: number;
  canGoNext: boolean;
  canGoPrevious: boolean;
} 