'use client';

import { useState } from 'react';
import { 
  FieldValues, 
  UseFormReturn, 
  UseFormProps, 
  useForm as useReactHookForm
} from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

interface UseAuthFormOptions<T extends FieldValues> {
  schema: z.ZodType<T>;
  defaultValues: UseFormProps<T>['defaultValues'];
  onSubmit: (data: T) => Promise<void> | void;
}

/**
 * Custom hook for auth forms that combines form handling with loading state
 */
export function useAuthForm<T extends FieldValues>({
  schema,
  defaultValues,
  onSubmit
}: UseAuthFormOptions<T>) {
  const [loading, setLoading] = useState(false);
  
  // Create the form with the provided schema and default values
  const form = useReactHookForm<T>({
    resolver: zodResolver(schema as any),
    defaultValues,
  });
  
  // Handle form submission with loading state
  const handleSubmit = async (data: T) => {
    setLoading(true);
    
    try {
      await onSubmit(data);
    } catch (error) {
      console.error('Form submission error:', error);
      // You could add form error handling here
    } finally {
      setLoading(false);
    }
  };
  
  // Submit handler that wraps the form's handleSubmit
  const submitHandler = form.handleSubmit(handleSubmit);
  
  return {
    form,
    loading,
    setLoading,
    submitHandler,
  };
} 