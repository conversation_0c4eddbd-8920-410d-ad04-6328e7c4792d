"""
Transcription Types and Data Classes

Shared types, enums, and data structures used across transcription modules.
"""

from enum import Enum
from typing import Optional
from dataclasses import dataclass


class ProcessingMode(Enum):
    """Processing modes for transcription engine."""
    REAL_TIME = "real_time"  # Process audio chunks as they arrive (100ms chunks)
    BATCH = "batch"          # Process larger audio segments


@dataclass
class TranscriptionResult:
    """Result from transcription processing."""
    text: str
    confidence: float
    processing_time_ms: float
    language: Optional[str] = None
    is_final: bool = True
    raw_text: Optional[str] = None
    error: Optional[str] = None 