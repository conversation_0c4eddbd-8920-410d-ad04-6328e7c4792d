"""
ShortcutKeyboardManager class for managing keyboard shortcuts detection.

This module contains the ShortcutKeyboardManager class which is responsible for
setting up keyboard hooks and detecting when shortcuts are pressed.
"""

import keyboard
import logging
import threading
import time
from typing import Dict, Any, Set, List, Optional, Callable, Tuple

from .shortcut_utils import ShortcutUtils

logger = logging.getLogger('whispr.shortcut')

class ShortcutKeyboardManager:
    """Class for managing keyboard shortcuts detection."""
    
    def __init__(self, service_container=None):
        """Initialize the keyboard manager.
        
        Args:
            service_container: Service container for callbacks
        """
        self.service_container = service_container
        self.shortcuts = {}
        self.push_to_talk_mode = True
        self.is_recording = False
        self.listen_for_window_cancel = False
        self.listen_for_mode_switch_cancel = False  # Flag for mode switch window cancel
        
        # Callback functions
        self.on_start_recording = None
        self.on_stop_recording = None
        self.on_cancel_recording = None
        self.on_mode_switch = None
        self.on_window_cancelled = None
        
        # Single set to track ALL pressed keys
        self.currently_pressed_keys = set()
        
        # Single global hook reference
        self._global_hook = None
        self.hooks_setup = False
        
        # Cooldown for toggle
        self.last_toggle_time = 0.0
        self.toggle_cooldown = 0.25
        
        # Lock for thread safety
        self._lock = threading.Lock()
    
    def set_service_container(self, service_container):
        """Set the service container reference.
        
        Args:
            service_container: The service container instance
        """
        self.service_container = service_container
    
    def is_test_mode_active(self) -> bool:
        """Check if test mode is active via the shortcut service.
        
        Returns:
            True if test mode is active, False otherwise
        """
        if self.service_container:
            shortcut_service = self.service_container.resolve("shortcut")
            if shortcut_service:
                return shortcut_service.is_test_mode_active
        return False
    
    def set_shortcuts(self, shortcuts):
        """Set the shortcuts dictionary.
        
        Args:
            shortcuts: Dictionary of shortcuts
        """
        self.shortcuts = shortcuts
    
    def setup_hooks(self):
        """Setup hooks for keyboard events."""
        # Clean up any existing hooks
        self.unhook_all_keys()
        
        # Get shortcut keys to hook
        shortcuts_to_hook = set()
        
        # Add all shortcut keys to the set of keys to hook
        for shortcut_type in ["pushToTalk", "toggle", "cancel", "modeSwitch"]:
            if shortcut_type in self.shortcuts and self.shortcuts[shortcut_type]:
                keys = ShortcutUtils.translate_set(set(self.shortcuts[shortcut_type]))
                shortcuts_to_hook.update(keys)
        
        # Set up a global hook first (without suppression)
        # This ensures all key events are tracked properly
        self._global_hook = keyboard.hook(self._on_keyboard_event)
        
        # Set up suppression hooks for shortcut keys
        # These will conditionally suppress ONLY when a complete shortcut is pressed
        for key in shortcuts_to_hook:
            try:
                keyboard.hook_key(key, self._on_shortcut_key_event, suppress=True)
            except Exception as e:
                logger.error(f"Error setting up hook for key {key}: {e}")
                
        # Mark hooks as set up
        self.hooks_setup = True
        logger.debug(f"Keyboard hooks have been set up. Push-to-talk mode: {self.push_to_talk_mode}")
    
    def _on_keyboard_event(self, event):
        """Handle all keyboard events for tracking purposes.
        This hook doesn't suppress anything - just tracks key state.
        """
        try:
            # Update our internal key state based on event type
            if event.event_type == keyboard.KEY_DOWN:
                if event.name not in self.currently_pressed_keys:
                    self.currently_pressed_keys.add(event.name)
            elif event.event_type == keyboard.KEY_UP:
                if event.name in self.currently_pressed_keys:
                    self.currently_pressed_keys.remove(event.name)
            
            # Broadcast key events if test mode is active for real-time key visualization
            if self.is_test_mode_active():
                self._broadcast_key_event(event)
                
        except Exception as e:
            logger.error(f"Error in keyboard event handler: {e}")
    
    def _broadcast_key_event(self, event):
        """Broadcast key events for real-time visualization during test mode.
        
        Args:
            event: The keyboard event to broadcast
        """
        try:
            if self.service_container:
                # Get IPC bridge from service container for event broadcasting
                ipc_bridge = self.service_container.resolve("ipc")
                if ipc_bridge:
                    # Convert key name to user-friendly string representation
                    key_string = ShortcutUtils.key_to_string(event.name)
                    
                    # Convert all currently pressed keys to user-friendly strings
                    currently_pressed_strings = [
                        ShortcutUtils.key_to_string(key) for key in self.currently_pressed_keys
                    ]
                    
                    # Prepare event data
                    event_data = {
                        "type": "key_event",
                        "data": {
                            "key": key_string,
                            "event_type": "down" if event.event_type == keyboard.KEY_DOWN else "up",
                            "timestamp": time.time(),
                            "currently_pressed": currently_pressed_strings
                        }
                    }
                    
                    # Send the event through IPCBridge
                    if hasattr(ipc_bridge, 'send_message_sync'):
                        ipc_bridge.send_message_sync(event_data)
                else:
                    logger.warning("No IPC bridge available for key event broadcasting")
            else:
                logger.warning("No service container available for key event broadcasting")
                    
        except Exception as e:
            logger.error(f"Error broadcasting key event: {e}")
    
    def _on_shortcut_key_event(self, event):
        """Handle shortcut key events with suppression.
        Return False to suppress the key.
        """
        with self._lock:
            key_str = event.name
            
            # Get all keys that are currently pressed according to our tracking
            # We need to get the actual state before making any decisions
            current_pressed = ShortcutUtils.translate_set(self.currently_pressed_keys)
            
            # For key down events, ensure we have this key in our set
            if event.event_type == keyboard.KEY_DOWN:
                current_pressed.add(key_str)
            
            # Get all shortcut combinations
            ptt_combo = ShortcutUtils.translate_set(set(self.shortcuts.get("pushToTalk", [])))
            toggle_combo = ShortcutUtils.translate_set(set(self.shortcuts.get("toggle", [])))
            cancel_combo = ShortcutUtils.translate_set(set(self.shortcuts.get("cancel", [])))
            mode_switch_combo = ShortcutUtils.translate_set(set(self.shortcuts.get("modeSwitch", [])))
            
            # Check for complete shortcuts and trigger appropriate actions
            is_ptt_active = ptt_combo and len(ptt_combo) > 0 and all(k in current_pressed for k in ptt_combo)
            is_toggle_active = toggle_combo and len(toggle_combo) > 0 and all(k in current_pressed for k in toggle_combo)
            is_cancel_active = cancel_combo and len(cancel_combo) > 0 and all(k in current_pressed for k in cancel_combo)
            is_mode_switch_active = mode_switch_combo and len(mode_switch_combo) > 0 and all(k in current_pressed for k in mode_switch_combo)
            
            # Check if we're in test mode - handle differently
            test_mode = self.is_test_mode_active()
            
            # Directly call handlers for detected shortcuts
            if event.event_type == keyboard.KEY_DOWN:
                # Check for window cancel after recording has stopped
                if not self.is_recording and (
                    (self.listen_for_window_cancel or self.listen_for_mode_switch_cancel) 
                    and is_cancel_active
                ):
                    if self.on_window_cancelled:
                        self.on_window_cancelled()
                    self.listen_for_window_cancel = False  # Only listen once
                    self.listen_for_mode_switch_cancel = False  # Reset mode switch cancel flag too
                    return False  # Suppress the key
                
                # Mode switch
                if is_mode_switch_active:
                    if time.time() - self.last_toggle_time < self.toggle_cooldown:
                        pass  # Respect cooldown
                    else:
                        self.last_toggle_time = time.time()
                        if self.on_mode_switch:
                            self.on_mode_switch()
                        if not test_mode:
                            # Only set this flag if not in test mode
                            self.listen_for_mode_switch_cancel = True  # Enable cancel after mode switch
                # Push-to-talk mode
                elif self.push_to_talk_mode and is_ptt_active and not self.is_recording:
                    # Start recording in push-to-talk mode
                    if self.on_start_recording:
                        self.on_start_recording('pushToTalk')
                # Toggle mode
                elif not self.push_to_talk_mode:
                    if is_toggle_active:
                        if time.time() - self.last_toggle_time < self.toggle_cooldown:
                            pass  # Respect cooldown
                        else:
                            self.last_toggle_time = time.time()
                            # Toggle recording state
                            if not self.is_recording:
                                if self.on_start_recording:
                                    self.on_start_recording('toggle')
                            else:
                                if self.on_stop_recording:
                                    self.on_stop_recording('toggle')
                    
                    elif is_cancel_active and self.is_recording:
                        if time.time() - self.last_toggle_time < self.toggle_cooldown:
                            pass  # Respect cooldown
                        else:
                            self.last_toggle_time = time.time()
                            if self.on_cancel_recording:
                                self.on_cancel_recording()
            
            # For key release, handle PTT deactivation
            elif event.event_type == keyboard.KEY_UP and self.push_to_talk_mode and self.is_recording:
                # Check if PTT shortcut is still active after this key release
                ptt_still_active = ptt_combo and all(k in current_pressed - {key_str} for k in ptt_combo)
                if not ptt_still_active:
                    if self.on_stop_recording:
                        self.on_stop_recording('pushToTalk')
            
            # In test mode, don't suppress keys - let them pass through to the application
            if test_mode:
                return True  # Let keys pass through in test mode
            
            # Suppress the key if it's part of an active shortcut
            if is_ptt_active or is_toggle_active or is_cancel_active or is_mode_switch_active:
                return False  # Suppress the key
            
            # If it's just an individual key, let it pass through
            return True  # Let it pass through
    
    def unhook_all_keys(self):
        """Remove all keyboard hooks."""
        if self._global_hook:
            try:
                keyboard.unhook(self._global_hook)
            except Exception as e:
                logger.debug(f"Error unhooking global keyboard hook: {e}")
            self._global_hook = None
        
        # Unhook all hooked keys
        keyboard.unhook_all()
        
        # Release potentially stuck modifier keys
        for key in ["ctrl", "shift", "alt", "windows"]:
            try:
                keyboard.release(key)
            except:
                pass
        
        # Clear pressed keys state
        self.currently_pressed_keys.clear()
        
        # Mark hooks as not set up
        self.hooks_setup = False
        logger.debug("Keyboard hooks have been removed") 