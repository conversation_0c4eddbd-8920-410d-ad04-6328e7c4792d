-- Enable Row Level Security on waitlist table
ALTER TABLE waitlist ENABLE ROW LEVEL SECURITY;

-- Create policy for authenticated users to view waitlist
CREATE POLICY "Allow authenticated users to view waitlist entries"
  ON waitlist
  FOR SELECT
  USING (auth.role() IN ('authenticated', 'service_role'));

-- Create policy for service role to manage waitlist
CREATE POLICY "Service role can manage all waitlist entries"
  ON waitlist
  USING (auth.role() = 'service_role');
  
-- Create policy for anon submissions
CREATE POLICY "Anonymous users can submit waitlist entries"
  ON waitlist
  FOR INSERT
  WITH CHECK (true);

-- Add comment to explain the policies
COMMENT ON TABLE waitlist IS 'Stores email addresses of users who want to join the waitlist with RLS policies'; 