/**
 * Auth Status
 * Simple component for displaying authentication state and user info
 * Can be used to show login status or user details
 */

import { StatusBadge } from '../shared/StatusBadge';

// ============================================================================
// TYPES
// ============================================================================

interface AuthStatusProps {
  isAuthenticated: boolean;
  user?: {
    email?: string;
    name?: string;
  };
  loading?: boolean;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function AuthStatus({
  isAuthenticated,
  user,
  loading = false,
  className
}: AuthStatusProps) {
  if (loading) {
    return (
      <div className={`flex items-center gap-2 ${className || ''}`}>
        <StatusBadge status="loading" text="Checking authentication..." />
      </div>
    );
  }

  if (isAuthenticated && user) {
    return (
      <div className={`flex items-center gap-2 ${className || ''}`}>
        <StatusBadge status="success" text="Authenticated" />
        {user.name && (
          <span className="text-sm text-muted-foreground">
            Welcome, {user.name}
          </span>
        )}
        {!user.name && user.email && (
          <span className="text-sm text-muted-foreground">
            {user.email}
          </span>
        )}
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className || ''}`}>
      <StatusBadge status="error" text="Not authenticated" />
    </div>
  );
}

export default AuthStatus; 