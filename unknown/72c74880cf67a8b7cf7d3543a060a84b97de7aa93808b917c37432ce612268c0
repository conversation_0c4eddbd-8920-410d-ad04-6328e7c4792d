-- Drop trigger and function
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP FUNCTION IF EXISTS public.handle_new_user();

-- Drop timestamp trigger
DROP TRIGGER IF EXISTS set_timestamp ON profiles;

-- Drop policies
DROP POLICY IF EXISTS "Users can view their own profile" ON profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON profiles;
DROP POLICY IF EXISTS "Service role can manage all profiles" ON profiles;

-- Drop table with cascade to ensure all dependencies are removed
DROP TABLE IF EXISTS profiles CASCADE; 