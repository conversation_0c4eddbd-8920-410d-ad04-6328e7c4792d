"""
ShortcutUtils class for utility functions related to keyboard shortcuts.

This module contains the ShortcutUtils class which provides utility methods
for working with keyboard shortcuts, including key translations and sorting.
"""

from typing import Dict, Any, Set, List, Tuple

class ShortcutUtils:
    """Utility class for keyboard shortcut operations."""
    
    # Recognized modifier keys
    MODIFIERS = {
        "ctrl", "shift", "alt", "option", "windows", "win", "super", "command", "meta"
    }

    # Mapping for user-friendly names -> keyboard library
    KEY_MAPPING = {
        "Control": "ctrl",
        "Alt": "alt",
        "Shift": "shift",
        "Command": "windows",
        "Space": "space",
        "Enter": "enter",
        "Escape": "esc",
        "Tab": "tab",
        "Backspace": "backspace",
        "Delete": "delete",
        "Home": "home",
        "End": "end",
        "PageUp": "page up",
        "PageDown": "page down",
        "Insert": "insert",
        "ArrowUp": "up",
        "ArrowDown": "down",
        "ArrowLeft": "left",
        "ArrowRight": "right",
        "F1": "f1","F2": "f2","F3": "f3","F4": "f4","F5": "f5",
        "F6": "f6","F7": "f7","F8": "f8","F9": "f9","F10": "f10",
        "F11": "f11","F12": "f12",
        "NumLock": "num lock",
        "Numpad0": "numpad 0","Numpad1": "numpad 1","Numpad2": "numpad 2",
        "Numpad3": "numpad 3","Numpad4": "numpad 4","Numpad5": "numpad 5",
        "Numpad6": "numpad 6","Numpad7": "numpad 7","Numpad8": "numpad 8",
        "Numpad9": "numpad 9","NumpadDivide": "numpad divide",
        "NumpadMultiply": "numpad multiply","NumpadSubtract": "numpad subtract",
        "NumpadAdd": "numpad add","NumpadDecimal": "numpad decimal",
        "NumpadEnter": "numpad enter",
        "Backquote": "`","Minus": "-","Equal": "=",
        "BracketLeft": "[","BracketRight": "]","Backslash": "\\",
        "Semicolon": ";","Quote": "'","Comma": ",","Period": ".","Slash": "/",
    }
    
    # Order for sorting modifier keys
    MODIFIER_ORDER = {
        'Control': 1,
        'Alt': 2,
        'Shift': 3,
        'Command': 4
    }
    
    @staticmethod
    def translate_key(key: str) -> str:
        """Convert user-friendly key name to keyboard library name.
        
        Args:
            key: The key name
            
        Returns:
            The translated key name
        """
        if len(key) == 1:
            return key.lower()
        return ShortcutUtils.KEY_MAPPING.get(key, key.lower())
    
    @staticmethod
    def translate_set(keys: Set[str]) -> Set[str]:
        """Translate a set of key names.
        
        Args:
            keys: The set of key names
            
        Returns:
            The translated set of key names
        """
        return {ShortcutUtils.translate_key(x) for x in keys}
    
    @staticmethod
    def key_to_string(key_name: str) -> str:
        """Convert keyboard key name to string representation.
        
        Args:
            key_name: The key name from keyboard library
            
        Returns:
            User-friendly key name
        """
        if not key_name:
            return ''
        # If single character, uppercase it
        if len(key_name) == 1:
            return key_name.upper()
        # Otherwise, use reverse mapping
        for k, v in ShortcutUtils.KEY_MAPPING.items():
            if v == key_name:
                return k
        return key_name
    
    @staticmethod
    def sort_keys(keys: list) -> list:
        """Sort keys with modifiers first.
        
        Args:
            keys: List of keys to sort
            
        Returns:
            Sorted list of keys
        """
        def get_key_order(k_str):
            if k_str in ShortcutUtils.MODIFIER_ORDER:
                return (0, ShortcutUtils.MODIFIER_ORDER[k_str])
            return (1, k_str.lower())
        
        return sorted(keys, key=get_key_order)
    
    @staticmethod
    def split_chord(chord: Set[str]) -> Tuple[Set[str], Set[str]]:
        """Split a chord into modifier and non-modifier keys.
        
        Args:
            chord: Set of keys in the chord
            
        Returns:
            Tuple of (modifier_keys, non_modifier_keys)
        """
        normalized = ShortcutUtils.translate_set(chord)
        modifiers = {k for k in normalized if k in ShortcutUtils.MODIFIERS}
        non_modifiers = {k for k in normalized if k not in ShortcutUtils.MODIFIERS}
        return modifiers, non_modifiers 