/**
 * Status Badge
 * Reusable status indicator component matching app-latest design
 * Shows various states with appropriate icons and colors
 */

import { Badge } from '@src/components/ui/badge';
import { 
  HiC<PERSON><PERSON>Circle, 
  <PERSON><PERSON><PERSON><PERSON>, 
  HiArrowDownTray, 
  <PERSON><PERSON>rrowPath, 
  HiPlay,
  HiExclamationCircle 
} from 'react-icons/hi2';
import { cn } from '@src/lib/utils';

// ============================================================================
// TYPES
// ============================================================================

type StatusType = 
  | 'ready' 
  | 'downloaded' 
  | 'downloading' 
  | 'loading' 
  | 'not-downloaded' 
  | 'error'
  | 'success'
  | 'warning';

interface StatusBadgeProps {
  status: StatusType;
  text?: string;
  className?: string;
  size?: 'sm' | 'md';
}

// ============================================================================
// STATUS CONFIG
// ============================================================================

const statusConfig = {
  ready: {
    icon: HiCheckCircle,
    text: 'Ready to Use',
    className: 'bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800',
    animate: false,
  },
  downloaded: {
    icon: HiCheck,
    text: 'Downloaded',
    className: 'bg-emerald-100 text-emerald-700 dark:bg-emerald-900/40 dark:text-emerald-400',
    animate: false,
  },
  downloading: {
    icon: HiArrowPath,
    text: 'Downloading',
    className: 'bg-blue-100 text-blue-700 dark:bg-blue-900/40 dark:text-blue-400',
    animate: true,
  },
  loading: {
    icon: HiPlay,
    text: 'Loading',
    className: 'bg-purple-100 text-purple-700 dark:bg-purple-900/40 dark:text-purple-400',
    animate: true,
  },
  'not-downloaded': {
    icon: HiArrowDownTray,
    text: 'Not Downloaded',
    className: 'border-dashed',
    animate: false,
  },
  error: {
    icon: HiExclamationCircle,
    text: 'Error',
    className: 'bg-red-50 text-red-600 dark:bg-red-900/20 dark:text-red-400',
    animate: false,
  },
  success: {
    icon: HiCheckCircle,
    text: 'Success',
    className: 'bg-green-50 text-green-600 dark:bg-green-900/20 dark:text-green-400',
    animate: false,
  },
  warning: {
    icon: HiExclamationCircle,
    text: 'Warning',
    className: 'bg-yellow-50 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400',
    animate: false,
  },
};

// ============================================================================
// COMPONENT
// ============================================================================

export function StatusBadge({
  status,
  text,
  className,
  size = 'sm'
}: StatusBadgeProps) {
  const config = statusConfig[status];
  const Icon = config.icon;
  
  const sizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
  };

  const iconSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
  };

  return (
    <Badge 
      variant={status === 'not-downloaded' ? 'outline' : 'default'}
      className={cn(
        'font-medium gap-1',
        config.className,
        sizeClasses[size],
        className
      )}
    >
      <Icon 
        className={cn(
          iconSizeClasses[size],
          config.animate && 'animate-spin'
        )} 
      />
      {text || config.text}
    </Badge>
  );
}

export default StatusBadge; 