'use client';

import { Suspense, ReactNode } from 'react';
import { FiLoader } from 'react-icons/fi';

interface SearchParamsWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
}

/**
 * A wrapper component that provides a Suspense boundary for components using useSearchParams()
 * This is necessary for Next.js to properly handle client-side rendering with search params.
 */
export function SearchParamsWrapper({ 
  children, 
  fallback 
}: SearchParamsWrapperProps) {
  return (
    <Suspense fallback={fallback || <DefaultFallback />}>
      {children}
    </Suspense>
  );
}

/**
 * Default loading state that maintains the same visual style as the rest of the app
 */
function DefaultFallback() {
  return (
    <div className="flex justify-center items-center min-h-[200px]">
      <FiLoader className="animate-spin text-primary w-6 h-6" />
    </div>
  );
} 