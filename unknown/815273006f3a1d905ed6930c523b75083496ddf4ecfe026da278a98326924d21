/**
 * Step Lifecycle Hook
 * Extracted from SetupFlowContext for reusability
 * Allows contexts to register lifecycle handlers for setup steps
 */

import { useEffect } from 'react';
import type { SetupStep } from '../types/core';

// ============================================================================
// TYPES
// ============================================================================

interface StepLifecycleHandlers {
  onEnter?: (step: SetupStep) => Promise<void>;
  onExit?: (step: SetupStep) => Promise<void>;
  onCleanup?: (step: SetupStep) => Promise<void>;
}

// ============================================================================
// GLOBAL REGISTRY (matches SetupFlowContext implementation)
// ============================================================================

const stepHandlers = new Map<SetupStep, StepLifecycleHandlers>();

export function registerStepHandlers(step: SetupStep, handlers: StepLifecycleHandlers) {
  stepHandlers.set(step, handlers);
}

export function unregisterStepHandlers(step: SetupStep) {
  stepHandlers.delete(step);
}

export function getStepHandlers(step: SetupStep) {
  return stepHandlers.get(step);
}

// ============================================================================
// HOOK
// ============================================================================

/**
 * Hook for registering step lifecycle handlers
 * This is the same hook that was implemented in SetupFlowContext
 */
export function useStepLifecycle(
  step: SetupStep, 
  handlers: StepLifecycleHandlers,
  deps: React.DependencyList = []
) {
  useEffect(() => {
    console.log(`📝 Registering lifecycle handlers for step: ${step}`);
    registerStepHandlers(step, handlers);
    
    return () => {
      console.log(`📝 Unregistering lifecycle handlers for step: ${step}`);
      unregisterStepHandlers(step);
    };
  }, [step, ...deps]);
}

export default useStepLifecycle; 