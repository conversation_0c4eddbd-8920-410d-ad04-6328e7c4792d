"""
Model management helpers for One.Whispr.

This module provides model management capabilities including
registry, storage, downloading, and validation.
"""

from .model_registry import ModelRegistry, VOICE_MODELS
from .storage_manager import StorageManager
from .model_metadata import ModelMetadata, ModelStatus
from .model_downloader import ModelDownloader, DownloadProgress, DownloadStatus
from .model_validator import ModelValidator, ValidationReport, ValidationResult, ValidationError
from .model_loader import ModelLoader, LoadedModel, LoadingState, LoadingProgress
from .memory_manager import MemoryManager, MemoryPressure, MemoryThresholds, MemoryEvent, MemoryStats
from .hardware_detector import HardwareDetector, HardwareConfig, MemoryInfo

__all__ = [
    'ModelRegistry',
    'VOICE_MODELS',
    'StorageManager', 
    'ModelMetadata',
    'ModelStatus',
    'ModelDownloader',
    'DownloadProgress',
    'DownloadStatus',
    'ModelValidator',
    'ValidationReport',
    'ValidationResult',
    'ValidationError',
    'ModelLoader',
    'LoadedModel',
    'LoadingState',
    'LoadingProgress',
    'MemoryManager',
    'MemoryPressure',
    'MemoryThresholds',
    'MemoryEvent',
    'MemoryStats',
    'HardwareDetector',
    'HardwareConfig',
    'MemoryInfo'
] 