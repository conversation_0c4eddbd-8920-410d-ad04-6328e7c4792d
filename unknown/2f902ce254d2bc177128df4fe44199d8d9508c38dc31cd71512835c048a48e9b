/**
 * Content Container
 * Wrapper for step content with consistent layout and spacing
 * Provides centered content area with proper padding for app-latest design
 */

import React from 'react';

// ============================================================================
// TYPES
// ============================================================================

interface ContentContainerProps {
  children: React.ReactNode;
  centered?: boolean;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function ContentContainer({
  children,
  centered = true,
  maxWidth = 'md',
  className
}: ContentContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    full: 'max-w-full'
  };

  return (
    <div className={`h-full ${centered ? 'flex items-center justify-center' : ''} px-6 ${className || ''}`}>
      <div className={`w-full ${maxWidthClasses[maxWidth]} ${centered ? 'mx-auto' : ''}`}>
        {children}
      </div>
    </div>
  );
}

export default ContentContainer; 