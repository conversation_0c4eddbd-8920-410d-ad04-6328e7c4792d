/**
 * Step Navigation
 * Navigation controls for moving between setup steps
 * Provides previous/next buttons with proper state management
 */

import { Button } from '@src/components/ui/button';
import { LoadingSpinner } from '../shared/LoadingSpinner';

// ============================================================================
// TYPES
// ============================================================================

interface StepNavigationProps {
  canGoPrevious: boolean;
  canGoNext: boolean;
  canSkip?: boolean;
  isLastStep?: boolean;
  loading?: boolean;
  nextLabel?: string;
  previousLabel?: string;
  skipLabel?: string;
  onPrevious: () => void;
  onNext: () => void;
  onSkip?: () => void;
  className?: string;
}

// ============================================================================
// COMPONENT
// ============================================================================

export function StepNavigation({
  canGoPrevious,
  canGoNext,
  canSkip = false,
  isLastStep = false,
  loading = false,
  nextLabel,
  previousLabel = 'Previous',
  skipLabel = 'Skip for Now',
  onPrevious,
  onNext,
  onSkip,
  className
}: StepNavigationProps) {
  const defaultNextLabel = isLastStep ? 'Complete Setup' : 'Next';
  const finalNextLabel = nextLabel || defaultNextLabel;

  return (
    <div className={`flex justify-between items-center pt-6 ${className || ''}`}>
      {/* Previous Button */}
      <Button
        onClick={onPrevious}
        disabled={!canGoPrevious || loading}
        variant="outline"
        className="gap-2"
      >
        ← {previousLabel}
      </Button>

      {/* Skip Button (centered) */}
      {canSkip && onSkip && (
        <Button
          onClick={onSkip}
          disabled={loading}
          variant="ghost"
          size="sm"
          className="text-muted-foreground hover:text-foreground"
        >
          {skipLabel}
        </Button>
      )}

      {/* Next Button */}
      <Button
        onClick={onNext}
        disabled={(!canGoNext && !canSkip) || loading}
        className="gap-2"
      >
        {loading && <LoadingSpinner size="sm" />}
        {finalNextLabel} →
      </Button>
    </div>
  );
}

export default StepNavigation; 