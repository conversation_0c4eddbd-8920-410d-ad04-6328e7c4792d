/**
 * Social Login Button
 * OAuth provider button component matching app-latest design
 * Supports Google and X (Twitter) authentication
 */

import { Button } from '@src/components/ui/button';
import { FcGoogle } from 'react-icons/fc';
import { FaXTwitter } from 'react-icons/fa6';

// ============================================================================
// TYPES
// ============================================================================

type SocialProvider = 'google' | 'twitter';

interface SocialLoginButtonProps {
  provider: SocialProvider;
  onLogin: () => void;
  loading?: boolean;
  disabled?: boolean;
  className?: string;
}

// ============================================================================
// PROVIDER CONFIG
// ============================================================================

const providerConfig = {
  google: {
    icon: FcGoogle,
    label: 'Continue with Google',
  },
  twitter: {
    icon: FaXTwitter,
    label: 'Continue with X',
  },
};

// ============================================================================
// COMPONENT
// ============================================================================

export function SocialLoginButton({
  provider,
  onLogin,
  loading = false,
  disabled = false,
  className
}: SocialLoginButtonProps) {
  const config = providerConfig[provider];
  const Icon = config.icon;

  return (
    <Button 
      className={`w-full justify-center gap-2 ${className || ''}`}
      variant="outline" 
      onClick={onLogin}
      disabled={loading || disabled}
    >
      <Icon className="w-4 h-4" />
      {config.label}
    </Button>
  );
}

export default SocialLoginButton; 