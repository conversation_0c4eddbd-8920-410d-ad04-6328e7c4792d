import React from 'react';

interface ProgressBarProps {
  progress: number;
  label?: string;
  showPercentage?: boolean;
  className?: string;
  height?: number;
  status?: 'idle' | 'progress' | 'success' | 'error';
}

/**
 * A simple progress bar component
 */
export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  label,
  showPercentage = true,
  className = '',
  height = 8,
  status = 'progress'
}) => {
  // Ensure progress is between 0 and 100
  const safeProgress = Math.min(100, Math.max(0, progress));
  
  // Determine color based on status
  let barColor = 'bg-blue-500';
  if (status === 'success') barColor = 'bg-green-500';
  if (status === 'error') barColor = 'bg-red-500';
  if (status === 'idle') barColor = 'bg-gray-300';
  
  // Create height and width classes
  const heightClass = `h-${height}`;
  const widthClass = `w-[${safeProgress}%]`;
  
  return (
    <div className={`w-full ${className}`}>
      {label && (
        <div className="flex justify-between items-center mb-1">
          <span className="text-sm font-medium text-gray-700">{label}</span>
          {showPercentage && (
            <span className="text-sm font-medium text-gray-700">{Math.round(safeProgress)}%</span>
          )}
        </div>
      )}
      <div 
        className={`w-full bg-gray-200 rounded-full overflow-hidden ${heightClass}`}
      >
        <div 
          className={`${barColor} transition-all duration-300 ease-in-out h-full ${widthClass}`}
        />
      </div>
    </div>
  );
};

export default ProgressBar;
