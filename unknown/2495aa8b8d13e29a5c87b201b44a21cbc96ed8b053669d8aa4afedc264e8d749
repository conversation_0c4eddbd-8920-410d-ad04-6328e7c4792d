'use client';

import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { SocialButtons } from '../ui/SocialButtons';
import { useAuthForm } from '../hooks/useAuthForm';
import { useAuthNavigation } from '../hooks/useAuthNavigation';
import { processUserInfo } from '../../register/actions';

// Registration info validation schema
const registerInfoSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  firstName: z.string().min(1, { message: 'First name is required' }),
  lastName: z.string().min(1, { message: 'Last name is required' }),
});

type RegisterInfoFormValues = z.infer<typeof registerInfoSchema>;

interface RegisterInfoFormProps {
  initialEmail?: string;
  callbackUrl?: string | null;
  showSocial?: boolean;
  action?: string | null;
}

/**
 * Registration form for collecting user information
 */
export function RegisterInfoForm({ 
  initialEmail = '', 
  callbackUrl = null,
  showSocial = true,
  action
}: RegisterInfoFormProps) {
  const navigation = useAuthNavigation();
  
  // Handle form submission
  const handleSubmit = async (data: RegisterInfoFormValues) => {
    try {
      // Create FormData for server action
      const formData = new FormData();
      formData.append('email', data.email);
      formData.append('firstName', data.firstName);
      formData.append('lastName', data.lastName);
      
      // Call the server action
      const result = await processUserInfo(formData);
      
      if (result.success) {
        // Navigate to password step with the data
        navigation.navigateToRegister('password', {
          email: data.email,
          firstName: data.firstName,
          lastName: data.lastName,
          callbackUrl
        });
      } else {
        // Handle error
        form.setError('email', {
          type: 'manual',
          message: result.error || 'Error processing registration'
        });
      }
    } catch (error) {
      console.error('Registration error:', error);
      form.setError('email', {
        type: 'manual',
        message: 'An unexpected error occurred'
      });
    }
  };
  
  // Setup form with our hook
  const { form, loading, submitHandler } = useAuthForm<RegisterInfoFormValues>({
    schema: registerInfoSchema,
    defaultValues: {
      email: initialEmail,
      firstName: '',
      lastName: '',
    },
    onSubmit: handleSubmit,
  });

  return (
    <>
      <Form {...form}>
        <form onSubmit={submitHandler} className="space-y-4">
          <div className="flex gap-2">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Your first name" 
                      autoComplete="given-name"
                      className="text-base"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Your last name" 
                      autoComplete="family-name"
                      className="text-base"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Your email address" 
                    type="email" 
                    autoComplete="email"
                    className="text-base"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <Button type="submit" className="w-full text-base" disabled={loading}>
            {loading ? 'Processing...' : 'Continue'}
          </Button>
        </form>
      </Form>
      
      {showSocial && <SocialButtons mode="register" callbackUrl={callbackUrl} action={action} />}
    </>
  );
} 