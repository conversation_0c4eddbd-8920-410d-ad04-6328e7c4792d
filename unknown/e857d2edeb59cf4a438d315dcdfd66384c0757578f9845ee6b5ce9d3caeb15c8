import { ipcMain } from 'electron';
import { VocabularyRepository } from './repository';
import { VocabularyItem } from './types';
import { backendEvents } from '../../../python/main';

const vocabularyRepository = new VocabularyRepository();

/**
 * Sets up all vocabulary-related IPC handlers
 */
export function setupVocabularyIPCHandlers(): void {
  
  // Set up backend event handling for usage tracking
  backendEvents.on('vocabulary_usage_update', (data) => {
    if (data.word_id || data.word) {
      // Try by ID first, then by word
      const identifier = data.word_id || data.word;
      const result = vocabularyRepository.updateUsage(identifier);
      if (result) {
        console.log(`Updated usage for vocabulary: ${identifier}`);
      } else {
        console.warn(`Failed to update usage for vocabulary: ${identifier}`);
      }
    }
  });

  // Get all vocabulary items
  ipcMain.handle('vocabulary:get-all', async (): Promise<VocabularyItem[]> => {
    try {
      return vocabularyRepository.findAll();
    } catch (error) {
      console.error('Error getting all vocabulary:', error);
      return [];
    }
  });

  // Get vocabulary by ID
  ipcMain.handle('vocabulary:get-by-id', async (_event, id: string): Promise<VocabularyItem | null> => {
    try {
      return vocabularyRepository.findById(id);
    } catch (error) {
      console.error('Error getting vocabulary by ID:', error);
      return null;
    }
  });

  // Get vocabulary items by mode
  ipcMain.handle('vocabulary:get-by-mode', async (_event, modeId: string): Promise<VocabularyItem[]> => {
    try {
      return vocabularyRepository.findByMode(modeId);
    } catch (error) {
      console.error('Error getting vocabulary by mode:', error);
      return [];
    }
  });

  // Search vocabulary by word
  ipcMain.handle('vocabulary:search', async (_event, word: string): Promise<VocabularyItem[]> => {
    try {
      return vocabularyRepository.searchByWord(word);
    } catch (error) {
      console.error('Error searching vocabulary:', error);
      return [];
    }
  });

  // Get vocabulary by frequency
  ipcMain.handle('vocabulary:get-by-frequency', async (_event, limit?: number): Promise<VocabularyItem[]> => {
    try {
      const items = vocabularyRepository.getActiveByFrequency();
      return limit ? items.slice(0, limit) : items;
    } catch (error) {
      console.error('Error getting vocabulary by frequency:', error);
      return [];
    }
  });

  // Create new vocabulary item
  ipcMain.handle('vocabulary:create', async (_event, data: Omit<VocabularyItem, 'id'>): Promise<VocabularyItem | null> => {
    try {
      return vocabularyRepository.create(data);
    } catch (error) {
      console.error('Error creating vocabulary item:', error);
      return null;
    }
  });

  // Update vocabulary item
  ipcMain.handle('vocabulary:update', async (_event, id: string, data: Partial<Omit<VocabularyItem, 'id'>>): Promise<VocabularyItem | null> => {
    try {
      return vocabularyRepository.update(id, data);
    } catch (error) {
      console.error('Error updating vocabulary item:', error);
      return null;
    }
  });

  // Delete vocabulary item
  ipcMain.handle('vocabulary:delete', async (_event, id: string): Promise<boolean> => {
    try {
      return vocabularyRepository.delete(id);
    } catch (error) {
      console.error('Error deleting vocabulary item:', error);
      return false;
    }
  });

  // Update word usage
  ipcMain.handle('vocabulary:update-usage', async (_event, id: string): Promise<VocabularyItem | null> => {
    try {
      return vocabularyRepository.updateUsage(id);
    } catch (error) {
      console.error('Error updating vocabulary usage:', error);
      return null;
    }
  });

  // Get vocabulary statistics
  ipcMain.handle('vocabulary:get-statistics', async () => {
    try {
      return vocabularyRepository.getStatistics();
    } catch (error) {
      console.error('Error getting vocabulary statistics:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        byMode: {},
        totalFrequency: 0
      };
    }
  });

  console.log('Vocabulary IPC handlers registered');
} 