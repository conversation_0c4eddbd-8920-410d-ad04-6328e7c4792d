"""
Utility modules for One.Whispr.

This module provides utility functions and classes that are used
across the application.
"""

# Note: Utility classes will be implemented as needed
# This serves as a placeholder for future utility functionality

# Performance monitoring and optimization utilities
from .performance_profiler import (
    PerformanceProfiler, 
    PerformanceMetrics, 
    SystemMetrics,
    get_profiler,
    profile_operation,
    profile_function,
    profile_async_function
)

from .performance_optimizer import (
    PerformanceOptimizer,
    OptimizationConfig,
    SystemConstraints,
    MemoryManager,
    AdaptiveProcessor,
    SmartCache,
    get_optimizer,
    optimize_function,
    optimize_batch_processing,
    optimize_memory,
    get_performance_recommendations,
    cached,
    adaptive_batch,
    memory_managed
)

__all__ = [
    # Performance Profiler
    'PerformanceProfiler',
    'PerformanceMetrics',
    'SystemMetrics',
    'get_profiler',
    'profile_operation',
    'profile_function',
    'profile_async_function',
    
    # Performance Optimizer
    'PerformanceOptimizer',
    'OptimizationConfig',
    'SystemConstraints',
    'MemoryManager',
    'AdaptiveProcessor',
    'SmartCache',
    'get_optimizer',
    'optimize_function',
    'optimize_batch_processing',
    'optimize_memory',
    'get_performance_recommendations',
    'cached',
    'adaptive_batch',
    'memory_managed'
]
