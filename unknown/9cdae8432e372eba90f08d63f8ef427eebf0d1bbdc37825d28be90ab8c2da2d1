import { BaseRepository } from '../../database/repositories/base';
import { SchemaWithMigrations } from '../../database/schemas/manager';
import { AppEntity, DEFAULT_APP_ENTITY, SetupStep, User, UserSession, AuthSettings } from './types';
import { generateId } from '../../database/core/id';

/**
 * Simple JWT decoder to extract payload without verification
 */
function decodeJWT(token: string): any {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = parts[1];
    const decoded = Buffer.from(payload, 'base64url').toString('utf8');
    return JSON.parse(decoded);
  } catch {
    return null;
  }
}

/**
 * Repository for app configuration and state
 */
export class AppRepository extends BaseRepository<AppEntity> {
  constructor() {
    super('app');
  }

  /**
   * Ensure the table exists with proper schema
   */
  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS app (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Get the app configuration (singleton pattern)
   */
  getAppConfig(): AppEntity {
    const configs = this.findAll();
    if (configs.length === 0) {
      // Create default config
      return this.create(DEFAULT_APP_ENTITY);
    }
    return configs[0];
  }

  /**
   * Update app configuration
   */
  updateAppConfig(updates: Partial<Omit<AppEntity, 'id'>>): AppEntity {
    const config = this.getAppConfig();
    const updated = this.update(config.id, updates);
    if (!updated) {
      throw new Error('Failed to update app configuration');
    }
    return updated;
  }

  /**
   * Mark setup as completed
   */
  markSetupCompleted(): AppEntity {
    return this.updateAppConfig({ 
      setupCompleted: true,
      firstRun: false,
      lastUsed: new Date().toISOString(),
      currentSetupStep: undefined // Clear current step when completed
    });
  }

  /**
   * Update last used timestamp
   */
  updateLastUsed(): AppEntity {
    return this.updateAppConfig({ 
      lastUsed: new Date().toISOString()
    });
  }

  /**
   * Update current setup step
   */
  updateCurrentSetupStep(step: SetupStep): AppEntity {
    return this.updateAppConfig({
      currentSetupStep: step
    });
  }

  /**
   * Check if setup is needed
   */
  isSetupNeeded(): boolean {
    const config = this.getAppConfig();
    return !config.setupCompleted;
  }

  /**
   * Get current setup step
   */
  getCurrentSetupStep(): SetupStep | null {
    const config = this.getAppConfig();
    return (config.currentSetupStep as SetupStep) || null;
  }

  /**
   * Reset setup (for testing or re-setup)
   */
  resetSetup(): AppEntity {
    return this.updateAppConfig({
      setupCompleted: false,
      currentSetupStep: 'auth'
    });
  }

  /**
   * Get schema definition for this repository
   */
  static getSchema(): SchemaWithMigrations {
    return {
      name: 'app',
      createStatement: `
        CREATE TABLE IF NOT EXISTS app (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            -- Add index for faster lookups (though app table will only have one record)
            CREATE INDEX IF NOT EXISTS idx_app_id ON app(id);
          `,
          down: `
            DROP INDEX IF EXISTS idx_app_id;
          `
        }
      ]
    };
  }
}

// === AUTHENTICATION REPOSITORIES ===

/**
 * Repository for managing users
 */
export class UserRepository extends BaseRepository<User> {
  constructor() {
    super('users');
  }

  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Find user by email
   */
  findByEmail(email: string): User | null {
    try {
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE json_extract(data, '$.email') = ?
      `;
      const result = this.db.prepare(query).get(email);
      return result ? this.deserialize(result) : null;
    } catch (error) {
      console.error('Error finding user by email:', error);
      return null;
    }
  }



  /**
   * Get the currently active user
   */
  getActiveUser(): User | null {
    try {
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE json_extract(data, '$.isActive') = 1
        ORDER BY json_extract(data, '$.lastLoginAt') DESC
        LIMIT 1
      `;
      const result = this.db.prepare(query).get();
      return result ? this.deserialize(result) : null;
    } catch (error) {
      console.error('Error getting active user:', error);
      return null;
    }
  }

  /**
   * Create or update user from OAuth data
   */
  upsertFromOAuth(oauthData: {
    email: string;
    firstName?: string;
    lastName?: string;
    fullName?: string;
    avatarUrl?: string;
    providerId: string;
  }): User {
    // Check if user already exists by email
    let existingUser = this.findByEmail(oauthData.email);

    const now = new Date().toISOString();

    if (existingUser) {
      // Update existing user
      const updatedUser: User = {
        ...existingUser,
        firstName: oauthData.firstName || existingUser.firstName,
        lastName: oauthData.lastName || existingUser.lastName,
        fullName: oauthData.fullName || existingUser.fullName,
        avatarUrl: oauthData.avatarUrl || existingUser.avatarUrl,
        providerId: oauthData.providerId,
        isActive: true,
        lastLoginAt: now,
        updatedAt: now,
      };
      this.save(updatedUser);
      return updatedUser;
    } else {
      // Create new user
      const newUser: User = {
        id: generateId(),
        email: oauthData.email,
        firstName: oauthData.firstName,
        lastName: oauthData.lastName,
        fullName: oauthData.fullName,
        avatarUrl: oauthData.avatarUrl,
        providerId: oauthData.providerId,
        isActive: true,
        lastLoginAt: now,
        createdAt: now,
        updatedAt: now,
      };
      this.save(newUser);
      return newUser;
    }
  }

  /**
   * Deactivate all users (for logout)
   */
  deactivateAllUsers(): void {
    const users = this.findAll();
    users.forEach(user => {
      if (user.isActive) {
        user.isActive = false;
        user.updatedAt = new Date().toISOString();
        this.save(user);
      }
    });
  }

  static getSchema(): SchemaWithMigrations {
    return {
      name: 'users',
      createStatement: `
        CREATE TABLE IF NOT EXISTS users (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            CREATE INDEX IF NOT EXISTS idx_users_email ON users(json_extract(data, '$.email'));
            CREATE INDEX IF NOT EXISTS idx_users_active ON users(json_extract(data, '$.isActive'));
          `,
          down: `
            DROP INDEX IF EXISTS idx_users_email;
            DROP INDEX IF EXISTS idx_users_active;
          `
        }
      ]
    };
  }
}

/**
 * Repository for managing user sessions
 */
export class UserSessionRepository extends BaseRepository<UserSession> {
  constructor() {
    super('user_sessions');
  }

  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Find sessions by user ID
   */
  findByUserId(userId: string): UserSession[] {
    try {
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE json_extract(data, '$.userId') = ?
        ORDER BY json_extract(data, '$.createdAt') DESC
      `;
      const results = this.db.prepare(query).all(userId);
      return results.map(row => this.deserialize(row));
    } catch (error) {
      console.error('Error finding sessions by user ID:', error);
      return [];
    }
  }

  /**
   * Get the active session for a user
   */
  getActiveSession(userId: string): UserSession | null {
    try {
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE json_extract(data, '$.userId') = ? 
        AND json_extract(data, '$.isActive') = 1
        AND datetime(json_extract(data, '$.expiresAt')) > datetime('now')
        ORDER BY json_extract(data, '$.lastAccessedAt') DESC
        LIMIT 1
      `;
      const result = this.db.prepare(query).get(userId);
      return result ? this.deserialize(result) : null;
    } catch (error) {
      console.error('Error getting active session:', error);
      return null;
    }
  }

  /**
   * Create session from OAuth token
   */
  createFromToken(userId: string, accessToken: string, additionalData?: {
    refreshToken?: string;
    expiresIn?: number;
    deviceInfo?: UserSession['deviceInfo'];
  }): UserSession {
    const now = new Date().toISOString();
    
    // Parse JWT to get expiry (fallback to 1 hour)
    let expiresAt: string;
    try {
      const decoded = decodeJWT(accessToken) as any;
      expiresAt = decoded?.exp 
        ? new Date(decoded.exp * 1000).toISOString()
        : new Date(Date.now() + (additionalData?.expiresIn || 3600) * 1000).toISOString();
    } catch {
      expiresAt = new Date(Date.now() + (additionalData?.expiresIn || 3600) * 1000).toISOString();
    }

    const session: UserSession = {
      id: generateId(),
      userId,
      accessToken,
      refreshToken: additionalData?.refreshToken,
      tokenType: 'Bearer',
      expiresAt,
      deviceInfo: additionalData?.deviceInfo || {
        platform: process.platform,
        deviceId: generateId(),
      },
      isActive: true,
      lastAccessedAt: now,
      createdAt: now,
    };

    this.save(session);
    return session;
  }

  /**
   * Update last accessed time
   */
  updateLastAccessed(sessionId: string): boolean {
    const session = this.findById(sessionId);
    if (!session) return false;

    session.lastAccessedAt = new Date().toISOString();
    return this.save(session);
  }

  /**
   * Deactivate all sessions for a user
   */
  deactivateUserSessions(userId: string): void {
    const sessions = this.findByUserId(userId);
    sessions.forEach(session => {
      if (session.isActive) {
        session.isActive = false;
        this.save(session);
      }
    });
  }

  /**
   * Clean up expired sessions
   */
  cleanupExpiredSessions(): number {
    try {
      const expiredQuery = `
        SELECT * FROM ${this.tableName} 
        WHERE datetime(json_extract(data, '$.expiresAt')) <= datetime('now')
      `;
      const expiredSessions = this.db.prepare(expiredQuery).all();
      
      let deletedCount = 0;
      expiredSessions.forEach(row => {
        const session = this.deserialize(row);
        if (this.delete(session.id)) {
          deletedCount++;
        }
      });

      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up expired sessions:', error);
      return 0;
    }
  }

  static getSchema(): SchemaWithMigrations {
    return {
      name: 'user_sessions',
      createStatement: `
        CREATE TABLE IF NOT EXISTS user_sessions (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(json_extract(data, '$.userId'));
            CREATE INDEX IF NOT EXISTS idx_sessions_active ON user_sessions(json_extract(data, '$.isActive'));
            CREATE INDEX IF NOT EXISTS idx_sessions_expires ON user_sessions(json_extract(data, '$.expiresAt'));
          `,
          down: `
            DROP INDEX IF EXISTS idx_sessions_user_id;
            DROP INDEX IF EXISTS idx_sessions_active;
            DROP INDEX IF EXISTS idx_sessions_expires;
          `
        }
      ]
    };
  }
}

/**
 * Repository for managing auth settings
 */
export class AuthSettingsRepository extends BaseRepository<AuthSettings> {
  constructor() {
    super('auth_settings');
  }

  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS auth_settings (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Get auth settings for a user
   */
  getByUserId(userId: string): AuthSettings | null {
    try {
      const query = `
        SELECT * FROM ${this.tableName} 
        WHERE json_extract(data, '$.userId') = ?
      `;
      const result = this.db.prepare(query).get(userId);
      return result ? this.deserialize(result) : null;
    } catch (error) {
      console.error('Error getting auth settings by user ID:', error);
      return null;
    }
  }

  /**
   * Create default auth settings for a user
   */
  createDefault(userId: string): AuthSettings {
    const now = new Date().toISOString();
    const settings: AuthSettings = {
      id: generateId(),
      userId,
      autoLogin: true,
      rememberDevice: true,
      sessionTimeout: 60 * 24 * 7, // 7 days in minutes
      biometricEnabled: false,
      createdAt: now,
      updatedAt: now,
    };

    this.save(settings);
    return settings;
  }

  /**
   * Get or create auth settings for a user
   */
  getOrCreateForUser(userId: string): AuthSettings {
    let settings = this.getByUserId(userId);
    if (!settings) {
      settings = this.createDefault(userId);
    }
    return settings;
  }

  static getSchema(): SchemaWithMigrations {
    return {
      name: 'auth_settings',
      createStatement: `
        CREATE TABLE IF NOT EXISTS auth_settings (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            CREATE INDEX IF NOT EXISTS idx_auth_settings_user_id ON auth_settings(json_extract(data, '$.userId'));
            CREATE UNIQUE INDEX IF NOT EXISTS idx_auth_settings_user_unique ON auth_settings(json_extract(data, '$.userId'));
          `,
          down: `
            DROP INDEX IF EXISTS idx_auth_settings_user_id;
            DROP INDEX IF EXISTS idx_auth_settings_user_unique;
          `
        }
      ]
    };
  }
}