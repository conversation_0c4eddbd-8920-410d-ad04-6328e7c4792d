"""
Audio processing helpers.

Components:
- AudioManager: Coordinates capture and consumer dispatch
- AudioCapture: Dual microphone + system audio capture with 100ms chunking
- AudioDeviceManager: Audio device enumeration and testing
- RealtimeLevelMonitor: Real-time audio level monitoring
- WavRecorder: Session-based WAV file recording
- SileroVAD: Voice activity detection using Silero VAD
"""

from .audio_manager import AudioManager, AudioConsumerType, AudioChunk
from .audio_capture import AudioCapture
from .device_manager import AudioDeviceManager
from .realtime_level_monitor import RealtimeLevelMonitor  
from .wav_recorder import WavRecorder
from .silero_vad import SileroVAD

__all__ = [
    "AudioManager",
    "AudioConsumerType", 
    "AudioChunk",
    "AudioCapture",
    "AudioDeviceManager",
    "RealtimeLevelMonitor",
    "WavRecorder",
    "SileroVAD"
] 