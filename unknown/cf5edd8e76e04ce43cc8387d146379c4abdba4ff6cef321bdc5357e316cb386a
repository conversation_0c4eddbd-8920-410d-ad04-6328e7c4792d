import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';

// Load environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing required environment variables: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with admin privileges
const supabase = createClient(supabaseUrl, supabaseKey);

// Path to migrations directory
const migrationsDir = path.join(__dirname, '..', 'src', 'migrations');

async function applyMigrations() {
  try {
    console.log('Checking for migrations in:', migrationsDir);
    
    // Read all migration files
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .filter(file => !file.includes('_rollback.sql'))
      .sort();
    
    if (migrationFiles.length === 0) {
      console.log('No migration files found');
      return;
    }
    
    console.log(`Found ${migrationFiles.length} migration files`);
    
    // Make sure _migrations table exists
    try {
      await supabase.rpc('execute_sql', { 
        sql_query: `
          CREATE TABLE IF NOT EXISTS public._migrations (
            id SERIAL PRIMARY KEY,
            name TEXT UNIQUE NOT NULL,
            applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      });
    } catch (error) {
      console.error('Could not create migrations table. Make sure you have run the setup SQL from README.');
      console.error(error);
      process.exit(1);
    }
    
    // Get already applied migrations
    const { data: appliedMigrations, error: fetchError } = await supabase
      .from('_migrations')
      .select('name');
    
    if (fetchError) {
      console.error('Error fetching applied migrations:', fetchError);
      process.exit(1);
    }
    
    const appliedMigrationNames = (appliedMigrations || []).map(m => m.name);
    console.log(`Found ${appliedMigrationNames.length} already applied migrations`);
    
    // Apply each pending migration
    for (const file of migrationFiles) {
      if (appliedMigrationNames.includes(file)) {
        console.log(`✓ Already applied: ${file}`);
        continue;
      }
      
      console.log(`Applying: ${file}`);
      
      // Read migration SQL
      const sql = fs.readFileSync(path.join(migrationsDir, file), 'utf8');
      
      // Execute SQL using RPC function
      const { error } = await supabase.rpc('execute_sql', { sql_query: sql });
      
      if (error) {
        console.error(`Failed to apply migration ${file}:`, error);
        process.exit(1);
      }
      
      // Record migration in _migrations table
      const { error: recordError } = await supabase
        .from('_migrations')
        .insert([{ name: file }]);
      
      if (recordError) {
        console.error(`Failed to record migration ${file}:`, recordError);
        process.exit(1);
      }
      
      console.log(`✓ Successfully applied: ${file}`);
    }
    
    console.log('All migrations applied successfully');
  } catch (error) {
    console.error('Migration error:', error);
    process.exit(1);
  }
}

// Run migrations
applyMigrations(); 