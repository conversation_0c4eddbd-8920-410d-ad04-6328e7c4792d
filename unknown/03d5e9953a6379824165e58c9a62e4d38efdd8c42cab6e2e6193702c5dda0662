import '@src/main.css'
import React from 'react';
import ReactDOM from 'react-dom/client';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@src/components/layout/theme-provider';
import { Titlebar } from '@src/components/layout/titlebar';

import App from '@src/features/app-2/App';
import { AppSidebar, PreloadedPages } from '@src/components/layout/app-sidebar';
import { SidebarProvider } from '@src/components/ui/sidebar';
import { ScrollArea } from '@radix-ui/react-scroll-area';

const GlobalProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
      {children}
    </ThemeProvider>
  );
};

// Use named export for consistent components exports
export function AuthLayout() {
  return <App />;
}

export function MainLayout() {
  return (
    <SidebarProvider defaultOpen={window.innerWidth >= 800}>
      <AppSidebar />
      <div className="flex-1 overflow-hidden bg-sidebar">
        <div className="h-[calc(100%-2rem)] bg-background border-7 border-sidebar rounded-3xl overflow-hidden border-l-0 border-t-0">
          <ScrollArea className="h-full">
            <div className="p-6">
              <div className="container mx-auto ">
                <PreloadedPages />
              </div>
            </div>
          </ScrollArea>
        </div>
      </div>
    </SidebarProvider>
  );
}

// Initialize the application
const rootElement = document.getElementById("root");
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <Titlebar>
        <GlobalProviders>
          <HashRouter>
            <Routes>
              <Route path="/" element={<Navigate to="/auth" replace />} />
              <Route path="/auth" element={<AuthLayout />} />
              <Route element={<MainLayout />}>
                <Route path="/home" element={null} />
                <Route path="/modes" element={null} />
                <Route path="/text-replacements" element={null} />
                <Route path="/vocabulary" element={null} />
                <Route path="/ai-models" element={null} />
                <Route path="/settings" element={null} />
                <Route path="/history" element={null} />
              </Route>
              {/* Redirect to login by default */}
              <Route path="*" element={<Navigate to="/auth" replace />} />
            </Routes>
          </HashRouter>
        </GlobalProviders>
      </Titlebar>
    </React.StrictMode>
  );
}