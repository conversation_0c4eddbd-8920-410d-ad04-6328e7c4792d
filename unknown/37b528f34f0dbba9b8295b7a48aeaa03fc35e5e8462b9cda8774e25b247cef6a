declare module 'electron-store' {
  interface Options<T> {
    defaults?: T;
    name?: string;
    clearInvalidConfig?: boolean;
  }

  class ElectronStore<T> {
    constructor(options?: Options<T>);
    
    set<K extends keyof T>(key: K, value: T[K]): void;
    set(object: Partial<T>): void;
    get<K extends keyof T>(key: K): T[K];
    get(): T;
    has<K extends keyof T>(key: K): boolean;
    clear(): void;

    store: T;
  }

  export default ElectronStore;
} 