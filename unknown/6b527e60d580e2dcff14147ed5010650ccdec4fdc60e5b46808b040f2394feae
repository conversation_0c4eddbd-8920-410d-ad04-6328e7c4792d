'use server';

import { z } from 'zod';
import { redirect } from 'next/navigation';
import { cookies } from 'next/headers';
import { createClient } from '@/utils/supabase/server';

// Rate limiting constants
const RESET_RATE_LIMIT_SECONDS = 60; // 1 minute between reset requests
const RESET_RATE_LIMIT_COOKIE = 'reset_last_sent';
const RESET_VERIFY_LIMIT_SECONDS = 30; // 30 seconds between verification attempts
const RESET_VERIFY_LIMIT_COOKIE = 'reset_last_verify';

// Validation schemas
const emailSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

const otpSchema = z.object({
  email: z.string().email({ message: 'Invalid email format' }),
  otp: z.string().length(6, { message: 'OTP must be 6 digits' }),
});

const passwordResetSchema = z.object({
  email: z.string().email({ message: 'Invalid email format' }),
  token: z.string().min(1, { message: 'Verification token is missing' }),
  password: z.string().min(8, { message: 'Password must be at least 8 characters' }),
  confirmPassword: z.string().min(8, { message: 'Please confirm your password' }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

/**
 * Request password reset by sending OTP
 */
export async function requestPasswordReset(formDataOrEmail: FormData | string) {
  // Extract email from input
  let email: string;
  
  if (typeof formDataOrEmail === 'string') {
    email = formDataOrEmail;
  } else {
    email = formDataOrEmail.get('email') as string;
  }
  
  const result = emailSchema.safeParse({ email });
  if (!result.success) {
    return { 
      success: false, 
      error: result.error.errors[0]?.message || 'Invalid email'
    };
  }
  
  try {
    // Check rate limiting using cookies
    const cookieStore = await cookies();
    const lastSentCookie = cookieStore.get(RESET_RATE_LIMIT_COOKIE);
    
    if (lastSentCookie) {
      try {
        const lastSentData = JSON.parse(lastSentCookie.value);
        const lastSentTime = new Date(lastSentData.timestamp);
        const currentTime = new Date();
        const timeDiffSeconds = Math.floor((currentTime.getTime() - lastSentTime.getTime()) / 1000);
        
        // If the email matches and not enough time has passed
        if (lastSentData.email === email && timeDiffSeconds < RESET_RATE_LIMIT_SECONDS) {
          const timeLeftSeconds = RESET_RATE_LIMIT_SECONDS - timeDiffSeconds;
          return {
            success: false,
            error: `Please wait ${timeLeftSeconds} seconds before requesting another reset code`
          };
        }
      } catch (e) {
        // Invalid cookie, ignore and proceed
        console.error('Invalid rate limit cookie:', e);
      }
    }
    
    const supabase = await createClient();
    
    // Check if the user exists without sending an email
    const { data, error: checkError } = await supabase
      .from('auth_email_check')
      .select('exists')
      .eq('email', email)
      .single();
    
    // For security reasons, don't reveal if email exists or not
    // Just proceed as if the email was sent
    if (checkError || !data || !data.exists) {
      // Set rate limit cookie even for non-existent emails to prevent email enumeration
      const rateLimitData = {
        email,
        timestamp: new Date().toISOString()
      };
      
      cookieStore.set(RESET_RATE_LIMIT_COOKIE, JSON.stringify(rateLimitData), {
        path: '/',
        maxAge: 60 * 60, // 1 hour (longer than the rate limit to ensure it's present)
        httpOnly: true,
        sameSite: 'lax',
      });
      
      return { 
        success: true, 
        email,
        message: 'If the email exists, a reset code will be sent',
        nextStep: 'otp' // Show OTP screen for consistent UX
      };
    }
    
    // User exists, send the actual reset email with OTP
    // Use resetPasswordForEmail which sends an email with an OTP code
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      // For password reset, redirect to the reset step with verified token
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/forgot-password?step=reset&email=${encodeURIComponent(email)}&token=verified`,
    });
    
    if (error) {
      console.error('Password reset request error:', error);
      return { 
        success: false, 
        error: 'Failed to send reset email'
      };
    }
    
    // Set rate limiting cookie for successful requests
    const rateLimitData = {
      email,
      timestamp: new Date().toISOString()
    };
    
    cookieStore.set(RESET_RATE_LIMIT_COOKIE, JSON.stringify(rateLimitData), {
      path: '/',
      maxAge: 60 * 60, // 1 hour (longer than the rate limit to ensure it's present)
      httpOnly: true,
      sameSite: 'lax',
    });
    
    return { 
      success: true, 
      email,
      message: 'Check your email for a password reset code',
      nextStep: 'otp'
    };
  } catch (error) {
    console.error('Password reset request error:', error);
    return { 
      success: false, 
      error: 'An error occurred processing your request' 
    };
  }
}

/**
 * Verify OTP for password reset
 */
export async function verifyResetOTP(formData: FormData) {
  const email = formData.get('email') as string;
  const otp = formData.get('otp') as string;
  
  const result = otpSchema.safeParse({ email, otp });
  if (!result.success) {
    return { 
      success: false, 
      error: result.error.errors[0]?.message || 'Invalid OTP'
    };
  }
  
  try {
    // Check rate limiting for verification attempts
    const cookieStore = await cookies();
    const lastVerifyCookie = cookieStore.get(RESET_VERIFY_LIMIT_COOKIE);
    
    if (lastVerifyCookie) {
      try {
        const lastVerifyData = JSON.parse(lastVerifyCookie.value);
        const lastVerifyTime = new Date(lastVerifyData.timestamp);
        const currentTime = new Date();
        const timeDiffSeconds = Math.floor((currentTime.getTime() - lastVerifyTime.getTime()) / 1000);
        
        // If the email matches and not enough time has passed
        if (lastVerifyData.email === email && timeDiffSeconds < RESET_VERIFY_LIMIT_SECONDS) {
          const timeLeftSeconds = RESET_VERIFY_LIMIT_SECONDS - timeDiffSeconds;
          return {
            success: false,
            error: `Please wait ${timeLeftSeconds} seconds between verification attempts`
          };
        }
      } catch (e) {
        console.error('Invalid rate limit cookie:', e);
      }
    }
    
    // Update the verification attempt timestamp
    cookieStore.set(RESET_VERIFY_LIMIT_COOKIE, JSON.stringify({
      email,
      timestamp: new Date().toISOString()
    }), {
      path: '/',
      maxAge: 60 * 60, // 1 hour
      httpOnly: true,
      sameSite: 'lax',
    });
    
    const supabase = await createClient();
    
    // Verify OTP with Supabase using the recovery type for password reset
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token: otp,
      type: 'recovery'
    });
    
    if (error) {
      console.error('Reset OTP verification error:', error.message);
      
      // Check for specific error types with more precise handling
      if (error.message.toLowerCase().includes('token is invalid')) {
        return {
          success: false,
          error: 'Verification code is invalid. Please check and try again.',
        };
      }
      
      if (error.message.toLowerCase().includes('token has expired')) {
        return {
          success: false,
          error: 'Your verification code has expired. Please request a new one.',
        };
      }
      
      // General case for invalid tokens
      if (error.message.toLowerCase().includes('invalid') || error.message.toLowerCase().includes('expired')) {
        return {
          success: false,
          error: 'Verification code is no longer valid. Please request a new one.',
        };
      }
      
      return { 
        success: false, 
        error: error.message || 'Invalid or expired verification code'
      };
    }
    
    return { 
      success: true, 
      email,
      token: 'verified',
      nextStep: 'reset'
    };
  } catch (error) {
    console.error('OTP verification error:', error);
    return { 
      success: false, 
      error: 'Failed to verify reset code' 
    };
  }
}

/**
 * Complete the password reset
 */
export async function resetPassword(formData: FormData) {
  const email = formData.get('email') as string;
  const token = formData.get('token') as string;
  const password = formData.get('password') as string;
  const confirmPassword = formData.get('confirmPassword') as string;
  
  const result = passwordResetSchema.safeParse({ 
    email, 
    token, 
    password, 
    confirmPassword 
  });
  
  if (!result.success) {
    return { 
      success: false, 
      error: result.error.errors[0]?.message || 'Invalid password information'
    };
  }
  
  try {
    // Only attempt password update if token was verified
    if (token !== 'verified') {
      return {
        success: false,
        error: 'Invalid or expired reset token'
      };
    }
    
    const supabase = await createClient();
    
    // Update the user's password using the Supabase auth API
    const { data, error } = await supabase.auth.updateUser({ 
      password: password 
    });
    
    if (error) {
      console.error('Password update error:', error);
      return { 
        success: false, 
        error: error.message || 'Failed to update password'
      };
    }
    
    return { 
      success: true, 
      message: 'Your password has been reset successfully'
    };
  } catch (error) {
    console.error('Password reset error:', error);
    return { 
      success: false, 
      error: 'Failed to reset password' 
    };
  }
} 