'use client';

import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { SocialButtons } from '../ui/SocialButtons';
import { useAuthForm } from '../hooks/useAuthForm';
import { useAuthNavigation } from '../hooks/useAuthNavigation';
import { AuthMode } from '../utils/auth-utils';
import { verifyEmail } from '../../login/actions';

// Email validation schema
const emailSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
});

type EmailFormValues = z.infer<typeof emailSchema>;

interface EmailFormProps {
  mode?: AuthMode;
  callbackUrl?: string | null;
  initialEmail?: string;
  showSocial?: boolean;
  buttonText?: string;
  action?: string | null;
}

/**
 * Email form for login, registration, and password reset
 */
export function EmailForm({ 
  mode = 'login',
  callbackUrl = null,
  initialEmail = '',
  showSocial = true,
  buttonText,
  action
}: EmailFormProps) {
  const navigation = useAuthNavigation();
  
  // Get appropriate button text based on mode
  const getButtonText = (mode: AuthMode) => {
    switch (mode) {
      case 'reset': return 'Send Reset Code';
      case 'register': return 'Continue';
      case 'login': 
      default: return 'Continue';
    }
  };
  
  // Handle form submission
  const handleSubmit = async (data: EmailFormValues) => {
    // Create FormData for server action
    const formData = new FormData();
    formData.append('email', data.email);
    
    if (mode === 'login') {
      try {
        // Call the server action
        const result = await verifyEmail(formData);
        
        if (result.success) {
          navigation.navigateToLogin('password', { 
            email: data.email,
            callbackUrl
          });
        } else {
          // Handle error from server action
          form.setError('email', { 
            type: 'manual', 
            message: result.error || 'Email verification failed' 
          });
        }
      } catch (error) {
        console.error('Email verification error:', error);
        form.setError('email', { 
          type: 'manual', 
          message: 'An unexpected error occurred' 
        });
      }
    } else if (mode === 'reset') {
      navigation.navigateToPasswordReset('otp', { 
        email: data.email,
        callbackUrl
      });
    } else if (mode === 'register') {
      navigation.navigateToRegister('info', { 
        email: data.email,
        callbackUrl
      });
    }
  };
  
  // Setup the form with our hooks
  const { form, loading, submitHandler } = useAuthForm<EmailFormValues>({
    schema: emailSchema,
    defaultValues: {
      email: initialEmail,
    },
    onSubmit: handleSubmit,
  });

  // Determine if we should show social login buttons
  const shouldShowSocial = showSocial && (mode === 'login' || mode === 'register');
  
  // Get the appropriate button text
  const displayButtonText = buttonText || getButtonText(mode);

  return (
    <>
      <Form {...form}>
        <form onSubmit={submitHandler} className="space-y-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email Address</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="Your email address" 
                    type="email" 
                    autoComplete="email"
                    className="text-base"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <Button type="submit" className="w-full text-base" disabled={loading}>
            {loading ? 'Processing...' : displayButtonText}
          </Button>
        </form>
      </Form>
      
      {shouldShowSocial && <SocialButtons mode={mode} callbackUrl={callbackUrl} action={action} />}
    </>
  );
} 