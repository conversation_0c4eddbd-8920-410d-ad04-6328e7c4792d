import { ipc<PERSON><PERSON><PERSON> } from 'electron';

export interface BackendStatusData {
  pythonRunning: boolean;
  pythonConnected: boolean;
  port: number | null;
}

// Standardized setup function for auto-discovery
export function setupAPI() {
  return {
    // === BACKEND COMMUNICATION ===

    // Backend communication
    sendCommand: (type: string, args?: any) => ipcRenderer.invoke('send-command', type, args),
    getBackendStatus: () => ipcRenderer.invoke('python-get-status'),
    reconnectBackend: () => ipcRenderer.invoke('python-reconnect'),

    // Backend WebSocket message listener
    onBackendMessage: (callback: (message: any) => void) => {
      const subscription = (_event: any, message: any) => callback(message);
      ipcRenderer.on('python-ws-message', subscription);

      // Return cleanup function
      return () => {
        ipcRenderer.removeListener('python-ws-message', subscription);
      };
    },

    // Backend connection status listeners
    onBackendConnected: (callback: () => void) => {
      const subscription = () => callback();
      ipcRenderer.on('python-ws-connected', subscription);

      // Return cleanup function
      return () => {
        ipcRenderer.removeListener('python-ws-connected', subscription);
      };
    },

    onBackendConnectionFailed: (callback: () => void) => {
      const subscription = () => callback();
      ipcRenderer.on('python-ws-connection-failed', subscription);

      // Return cleanup function
      return () => {
        ipcRenderer.removeListener('python-ws-connection-failed', subscription);
      };
    },

    // Backend server welcome listener
    onBackendServerWelcome: (callback: (data: any) => void) => {
      const subscription = (_event: any, data: any) => callback(data);
      ipcRenderer.on('python-server-welcome', subscription);

      // Return cleanup function
      return () => {
        ipcRenderer.removeListener('python-server-welcome', subscription);
      };
    }
  };
}
