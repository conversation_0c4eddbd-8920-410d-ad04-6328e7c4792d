"""Transcription event emission and IPC bridge handling."""

import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

from .transcription_types import TranscriptionResult

logger = logging.getLogger(__name__)


@dataclass
class TranscriptionEvent:
    """Base transcription event structure."""
    event_type: str
    timestamp: float
    data: Dict[str, Any]


class TranscriptionEventEmitter:
    """Handles emission of transcription events via IPC bridge."""
    
    def __init__(self, ipc_bridge=None):
        """Initialize event emitter.
        
        Args:
            ipc_bridge: IPC bridge for sending events to main process
        """
        self.ipc_bridge = ipc_bridge
        self.session_id = None
        
        logger.debug("TranscriptionEventEmitter initialized")
    
    def set_session_id(self, session_id: str) -> None:
        """Set current session ID for event context.
        
        Args:
            session_id: Current transcription session ID
        """
        self.session_id = session_id
        logger.debug(f"Event emitter session ID set: {session_id}")
    
    def emit_session_started(self, processing_mode: str, model_size: str) -> None:
        """Emit session started event - shows recording indicator.
        
        Args:
            processing_mode: Processing mode (real_time/batch)
            model_size: Whisper model size
        """
        self._emit_event("session_start", {
            "session_id": self.session_id,
            "processing_mode": processing_mode,
            "model_size": model_size
        })
    
    def emit_session_ended(self, session_stats: Dict[str, Any]) -> None:
        """Emit session ended event - hides recording indicator.
        
        Args:
            session_stats: Final session statistics
        """
        self._emit_event("session_end", {
            "session_id": self.session_id,
            "stats": session_stats
        })
    
    def emit_transcription_result(self, result: TranscriptionResult) -> None:
        """Emit final transcription result - the actual transcribed text.
        
        Args:
            result: Transcription result
        """
        self._emit_event("result", {
            "session_id": self.session_id,
            "result": asdict(result)
        })
    
    def emit_partial_result(self, partial_text: str, confidence: float = 0.0, 
                           metadata: Optional[Dict[str, Any]] = None) -> None:
        """Emit partial transcription result - real-time growing context updates.
        
        Args:
            partial_text: Partial transcription text (growing context)
            confidence: Confidence score
            metadata: Rich metadata including segment info, changes, stability, etc.
        """
        self._emit_event("partial", { 
            "session_id": self.session_id,
            "text": partial_text,
            "confidence": confidence,
            "is_partial": True,
            **(metadata or {})  # Spread all metadata into the event
        })
    
    def emit_final_result(self, final_text: str, confidence: float = 1.0, 
                         metadata: Optional[Dict[str, Any]] = None) -> None:
        """Emit final transcription result - completed transcription.
        
        Args:
            final_text: Final transcription text
            confidence: Confidence score
            metadata: Optional metadata
        """
        self._emit_event("result", { 
            "session_id": self.session_id,
            "text": final_text,
            "confidence": confidence,
            "is_partial": False,
            **(metadata or {})  # Spread all metadata into the event
        })
    
    def emit_error(self, error_type: str, error_message: str) -> None:
        """Emit error event - shows error messages to user.
        
        Args:
            error_type: Type of error
            error_message: Error description
        """
        self._emit_event("error", {
            "session_id": self.session_id,
            "error_type": error_type,
            "error_message": error_message
        })
    

    def _emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Internal method to emit events via IPC bridge.
        
        Args:
            event_type: Type of event
            data: Event data
        """
        try:
            if self.ipc_bridge:
                # Use the correct IPC bridge format for synchronous sending
                event_message = {
                    "type": "event",
                    "event": f"transcription.{event_type}",  # Use dot notation like frontend expects
                    "data": {
                        **data,  # Include all the data
                        "timestamp": __import__('time').time()  # Add timestamp to data
                    }
                }
                
                # Use send_message_sync for synchronous sending from worker threads
                if hasattr(self.ipc_bridge, 'send_message_sync'):
                    success = self.ipc_bridge.send_message_sync(event_message)
                    if success:
                        logger.debug(f"Emitted event: transcription.{event_type}")
                    else:
                        logger.warning(f"Failed to send event transcription.{event_type} (queued for later)")
                else:
                    logger.warning("send_message_sync method not available on IPC bridge")
            else:
                logger.debug(f"No IPC bridge - would emit transcription.{event_type}: {data}")
        
        except Exception as e:
            logger.error(f"Failed to emit event transcription.{event_type}: {e}")