'use client';

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { AuthCard } from '../_components/ui/AuthCard';
import { AuthFlowController, AuthFlowFooterContent } from '../_lib/auth-flow';
import { loginSteps } from '../_lib/auth-steps';
import { SearchParamsWrapper } from '@/components/suspense/SearchParamsWrapper';

// Content component that uses useSearchParams
function LoginContent() {
  const searchParams = useSearchParams();
  const step = searchParams.get('step') || 'email';
  const email = searchParams.get('email') || '';
  const message = searchParams.get('message');
  const callbackUrl = searchParams.get('callbackUrl');
  const action = searchParams.get('action');
  
  // Extract all search params
  const params: Record<string, string | null> = {};
  searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  // Generate footer content based on step or message
  const footerContent: AuthFlowFooterContent = {
    email: (
      <p className="text-base text-muted-foreground">
        Don't have an account?{' '}
        <Link 
          href={callbackUrl 
            ? `/register?callbackUrl=${encodeURIComponent(callbackUrl)}`
            : '/register'
          }
          className="font-medium text-primary hover:underline underline-offset-4"
        >
          Sign Up
        </Link>
      </p>
    ),
    otp: (
      <p className="text-base text-muted-foreground">
        Can't find your email? Check your spam folder.
      </p>
    )
  };
  
  // Show a success message when password is reset
  if (message === 'password-reset-success') {
    footerContent.email = (
      <p className="text-base text-green-600 dark:text-green-400">
        Your password has been reset successfully.
      </p>
    );
  }

  return (
    <AuthFlowController
      steps={loginSteps}
      currentStep={step}
      params={{ email, callbackUrl, action }}
      footerContent={footerContent}
      renderCard={({ title, children, footerContent }) => (
        <AuthCard title={title} footerContent={footerContent}>
          {children}
        </AuthCard>
      )}
    />
  );
}

// Main page component with the SearchParamsWrapper
export default function LoginPage() {
  return (
    <SearchParamsWrapper>
      <LoginContent />
    </SearchParamsWrapper>
  );
} 