"""
Hardware detection and optimization for One.Whispr voice models.

This module detects system capabilities and determines optimal
configuration for model loading and memory management.
"""

import gc
import logging
import platform
from dataclasses import dataclass
from typing import Dict, Any, Optional, Tuple
import psutil

logger = logging.getLogger('whispr.helpers.models.hardware_detector')

# Lazy loading flag - import torch only when needed
_TORCH_AVAILABLE = None
_ACCELERATE_AVAILABLE = None

def _check_torch_availability():
    """Check if Py<PERSON>orch is available (lazy import)."""
    global _TORCH_AVAILABLE
    if _TORCH_AVAILABLE is None:
        try:
            import torch
            _TORCH_AVAILABLE = True
            logger.info("PyTorch is available")
        except ImportError:
            _TORCH_AVAILABLE = False
            logger.warning("PyTorch not available - falling back to CPU-only mode")
    return _TORCH_AVAILABLE

def _check_accelerate_availability():
    """Check if Accelerate is available (lazy import)."""
    global _ACCELERATE_AVAILABLE
    if _ACCELERATE_AVAILABLE is None:
        try:
            import accelerate
            _ACCELERATE_AVAILABLE = True
            logger.info("Accelerate is available")
        except ImportError:
            _ACCELERATE_AVAILABLE = False
            logger.debug("Accelerate not available - some optimizations will be skipped")
    return _ACCELERATE_AVAILABLE


@dataclass
class HardwareConfig:
    """Hardware configuration for optimal model loading."""
    device: str
    torch_dtype: str
    total_memory: int
    available_memory: int
    max_model_size: int
    recommended_batch_size: int
    supports_half_precision: bool
    cpu_cores: int
    cpu_architecture: str
    gpu_name: Optional[str] = None
    gpu_memory: Optional[int] = None
    gpu_compute_capability: Optional[Tuple[int, int]] = None
    supports_device_mapping: bool = False
    attn_implementation: str = "eager"


@dataclass
class MemoryInfo:
    """Current memory usage information."""
    total_system_memory: int
    available_system_memory: int
    used_system_memory: int
    memory_percent: float
    gpu_memory_total: Optional[int] = None
    gpu_memory_used: Optional[int] = None
    gpu_memory_free: Optional[int] = None
    gpu_memory_percent: Optional[float] = None


class HardwareDetector:
    """Detects and manages hardware capabilities for model loading."""
    
    def __init__(self):
        """Initialize the hardware detector."""
        self._config: Optional[HardwareConfig] = None
        self._torch_available = None  # Will be checked lazily
        
    def detect_optimal_config(self) -> HardwareConfig:
        """Detect optimal hardware configuration for model loading.
        
        Returns:
            HardwareConfig with optimal settings
        """
        if self._config is None:
            self._config = self._detect_config()
        
        return self._config
    
    def _detect_config(self) -> HardwareConfig:
        """Internal method to detect hardware configuration."""
        logger.info("Detecting hardware configuration...")
        
        # Get system memory info
        memory = psutil.virtual_memory()
        total_memory = memory.total
        available_memory = memory.available
        
        # Get CPU info
        cpu_cores = psutil.cpu_count(logical=True)
        cpu_architecture = platform.machine()
        
        # Default to CPU configuration
        device = "cpu"
        torch_dtype = "float32"
        supports_half_precision = False
        gpu_name = None
        gpu_memory = None
        gpu_compute_capability = None
        attn_implementation = "sdpa"  # Default to SDPA which works on CPU and GPU
        
        # Try to detect CUDA if torch is available
        if _check_torch_availability():
            import torch
            if torch.cuda.is_available():
                try:
                    device = "cuda"
                    torch_dtype = "float16"  # Use half precision on GPU
                    supports_half_precision = True
                    
                    # Get GPU info
                    gpu_properties = torch.cuda.get_device_properties(0)
                    gpu_name = gpu_properties.name
                    gpu_memory = gpu_properties.total_memory
                    gpu_compute_capability = (gpu_properties.major, gpu_properties.minor)
                    
                    # Use GPU memory for calculations if available
                    available_memory = min(available_memory, int(gpu_memory * 0.8))  # Reserve 20% GPU memory
                    
                    # Determine if Flash Attention is likely to be supported
                    # Flash Attention generally requires NVIDIA GPU with compute capability 7.5+
                    # (RTX 20xx and later, or A100/H100)
                    if gpu_compute_capability and gpu_compute_capability[0] >= 7 and (
                        gpu_compute_capability[0] > 7 or gpu_compute_capability[1] >= 5
                    ):
                        attn_implementation = "flash_attention_2"
                        logger.info(f"GPU supports Flash Attention: {gpu_name} with compute capability {gpu_compute_capability}")
                    else:
                        attn_implementation = "sdpa"
                        logger.info(f"Using SDPA attention for GPU: {gpu_name} with compute capability {gpu_compute_capability}")
                    
                    logger.info(f"CUDA detected: {gpu_name} with {gpu_memory / (1024**3):.1f}GB memory")
                    
                except Exception as e:
                    logger.warning(f"CUDA detection failed, falling back to CPU: {e}")
                    device = "cpu"
                    torch_dtype = "float32"
                    supports_half_precision = False
                    attn_implementation = "sdpa"
        
        # Check for accelerate availability
        supports_device_mapping = _check_accelerate_availability()
        
        if supports_device_mapping:
            logger.info("Accelerate device mapping optimization available")
        
        # Calculate memory limits (reserve 30% for system)
        usable_memory = int(available_memory * 0.7)
        max_model_size = int(usable_memory * 0.8)  # Reserve some memory for loading operations
        
        # Determine recommended batch size based on available memory
        if usable_memory > 8 * (1024**3):  # > 8GB
            recommended_batch_size = 16
        elif usable_memory > 4 * (1024**3):  # > 4GB
            recommended_batch_size = 8
        elif usable_memory > 2 * (1024**3):  # > 2GB
            recommended_batch_size = 4
        else:
            recommended_batch_size = 1
        
        config = HardwareConfig(
            device=device,
            torch_dtype=torch_dtype,
            total_memory=total_memory,
            available_memory=available_memory,
            max_model_size=max_model_size,
            recommended_batch_size=recommended_batch_size,
            supports_half_precision=supports_half_precision,
            cpu_cores=cpu_cores,
            cpu_architecture=cpu_architecture,
            gpu_name=gpu_name,
            gpu_memory=gpu_memory,
            gpu_compute_capability=gpu_compute_capability,
            supports_device_mapping=supports_device_mapping,
            attn_implementation=attn_implementation
        )
        
        logger.info(f"Hardware config: {device} device, {usable_memory / (1024**3):.1f}GB usable memory, using {attn_implementation} attention")
        return config
    
    def get_memory_info(self, include_gpu: bool = True) -> MemoryInfo:
        """Get current memory usage information.
        
        Args:
            include_gpu: Whether to include GPU memory info (requires PyTorch import)
        
        Returns:
            MemoryInfo with current memory statistics
        """
        # System memory
        memory = psutil.virtual_memory()
        
        memory_info = MemoryInfo(
            total_system_memory=memory.total,
            available_system_memory=memory.available,
            used_system_memory=memory.used,
            memory_percent=memory.percent
        )
        
        # GPU memory if available and requested
        if include_gpu and _check_torch_availability():
            import torch
            if torch.cuda.is_available():
                try:
                    gpu_memory_total = torch.cuda.get_device_properties(0).total_memory
                    gpu_memory_used = torch.cuda.memory_allocated(0)
                    gpu_memory_free = gpu_memory_total - gpu_memory_used
                    gpu_memory_percent = (gpu_memory_used / gpu_memory_total) * 100
                    
                    memory_info.gpu_memory_total = gpu_memory_total
                    memory_info.gpu_memory_used = gpu_memory_used
                    memory_info.gpu_memory_free = gpu_memory_free
                    memory_info.gpu_memory_percent = gpu_memory_percent
                    
                except Exception as e:
                    logger.warning(f"Failed to get GPU memory info: {e}")
        
        return memory_info
    
    def cleanup_memory(self) -> None:
        """Perform aggressive memory cleanup.
        
        This method performs comprehensive memory cleanup including:
        - Python garbage collection
        - PyTorch CUDA cache clearing
        - System memory trimming where available
        """
        logger.debug("Performing memory cleanup...")
        
        # Python garbage collection
        collected = gc.collect()
        logger.debug(f"Garbage collection freed {collected} objects")
        
        # PyTorch CUDA cleanup
        if _check_torch_availability():
            import torch
            if torch.cuda.is_available():
                try:
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                    logger.debug("CUDA cache cleared")
                except Exception as e:
                    logger.warning(f"CUDA cleanup failed: {e}")
        
        # System-specific memory trimming
        try:
            if platform.system() == "Linux":
                import ctypes
                libc = ctypes.CDLL("libc.so.6")
                libc.malloc_trim(0)
                logger.debug("Linux memory trimming performed")
        except Exception as e:
            logger.debug(f"System memory trimming not available: {e}")
    
    def check_model_compatibility(self, model_size_bytes: int) -> Tuple[bool, str]:
        """Check if a model can be loaded with current hardware.
        
        Args:
            model_size_bytes: Size of the model in bytes
            
        Returns:
            Tuple of (can_load, reason)
        """
        config = self.detect_optimal_config()
        memory_info = self.get_memory_info()
        
        # Check if model fits in available memory
        if model_size_bytes > config.max_model_size:
            return False, f"Model size ({model_size_bytes / (1024**3):.1f}GB) exceeds maximum ({config.max_model_size / (1024**3):.1f}GB)"
        
        # Check current memory usage - be more lenient for development
        if memory_info.memory_percent > 90:  # Increased from 80% to 90%
            return False, f"System memory usage critically high ({memory_info.memory_percent:.1f}%)"
        
        # Check GPU memory if using CUDA
        if config.device == "cuda" and memory_info.gpu_memory_percent:
            if memory_info.gpu_memory_percent > 80:  # Slightly more lenient for GPU too
                return False, f"GPU memory usage too high ({memory_info.gpu_memory_percent:.1f}%)"
        
        return True, "Model can be loaded"
    
    def get_optimal_loading_strategy(self, model_size_bytes: int) -> Dict[str, Any]:
        """Get optimal loading strategy for a model.
        
        Args:
            model_size_bytes: Size of the model in bytes
            
        Returns:
            Dictionary with loading strategy recommendations
        """
        config = self.detect_optimal_config()
        
        # Check accelerate availability
        has_accelerate = _check_accelerate_availability()
        
        # Determine loading strategy based on model size and available optimizations
        use_device_mapping = has_accelerate and config.device == "cuda" and model_size_bytes > 1 * (1024**3)
        
        if model_size_bytes < 500 * (1024**2):  # < 500MB
            strategy = "fast_load"
            chunk_size = model_size_bytes  # Load in one chunk
        elif model_size_bytes < 2 * (1024**3):  # < 2GB
            strategy = "chunked_load"
            chunk_size = 100 * (1024**2)  # 100MB chunks
        else:  # >= 2GB
            strategy = "streaming_load" if not use_device_mapping else "device_mapped_load"
            chunk_size = 50 * (1024**2)  # 50MB chunks
        
        # Adjust strategy for optimizations
        if use_device_mapping and model_size_bytes > 4 * (1024**3):  # For very large models
            strategy = "device_mapped_load"
            # Use 8-bit quantization for extremely large models if on GPU
            use_8bit = model_size_bytes > 8 * (1024**3) and config.device == "cuda"
        else:
            use_8bit = False
        
        return {
            "strategy": strategy,
            "chunk_size": chunk_size,
            "device": config.device,
            "dtype": config.torch_dtype,
            "batch_size": config.recommended_batch_size,
            "use_memory_mapping": model_size_bytes > 1 * (1024**3),  # Use mmap for files > 1GB
            "use_device_mapping": use_device_mapping,
            "use_8bit_quantization": use_8bit,
            "attn_implementation": config.attn_implementation,
            "cleanup_before_load": True
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert hardware configuration to dictionary.
        
        Returns:
            Dictionary representation of hardware config
        """
        config = self.detect_optimal_config()
        memory_info = self.get_memory_info()
        
        return {
            "hardware_config": {
                "device": config.device,
                "torch_dtype": config.torch_dtype,
                "total_memory": config.total_memory,
                "available_memory": config.available_memory,
                "max_model_size": config.max_model_size,
                "recommended_batch_size": config.recommended_batch_size,
                "supports_half_precision": config.supports_half_precision,
                "cpu_cores": config.cpu_cores,
                "cpu_architecture": config.cpu_architecture,
                "gpu_name": config.gpu_name,
                "gpu_memory": config.gpu_memory,
                "gpu_compute_capability": config.gpu_compute_capability,
                "supports_device_mapping": config.supports_device_mapping,
                "attn_implementation": config.attn_implementation
            },
            "current_memory": {
                "total_system_memory": memory_info.total_system_memory,
                "available_system_memory": memory_info.available_system_memory,
                "used_system_memory": memory_info.used_system_memory,
                "memory_percent": memory_info.memory_percent,
                "gpu_memory_total": memory_info.gpu_memory_total,
                "gpu_memory_used": memory_info.gpu_memory_used,
                "gpu_memory_free": memory_info.gpu_memory_free,
                "gpu_memory_percent": memory_info.gpu_memory_percent
            },
            "optimizations": {
                "torch_available": _check_torch_availability(),
                "accelerate_available": _check_accelerate_availability()
            }
        } 