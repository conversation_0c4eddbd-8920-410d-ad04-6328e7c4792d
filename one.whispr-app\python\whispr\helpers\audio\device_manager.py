"""
Audio device manager for enumerating and managing audio devices.

This module provides functionality for discovering, configuring, and managing
audio input and output devices across different platforms.
"""

import logging
import sounddevice as sd
import time
import threading
from typing import Dict, List, Any, Optional

# Configure logging
logger = logging.getLogger('whispr.audio.device_manager')


class AudioDeviceManager:
    """Manager for audio device enumeration and configuration."""
    
    def __init__(self):
        """Initialize the audio device manager."""
        self.input_devices = []
        self.output_devices = []
        self._cached_devices = None
        self._last_refresh_time = 0
        self._refresh_lock = threading.Lock()
        self._cache_timeout = 2.0  # 2 seconds cache timeout to prevent spam
        
        logger.debug("AudioDeviceManager initialized")
    
    def refresh_devices(self, force: bool = False) -> Dict[str, List[Dict[str, Any]]]:
        """Refresh the list of available audio devices.
        
        Args:
            force: Force refresh even if cache is still valid
        
        Returns:
            Dictionary with input and output device lists
        """
        # Check if we can use cached results to prevent spam
        current_time = time.time()
        if not force and self._cached_devices is not None and (current_time - self._last_refresh_time) < self._cache_timeout:
            logger.debug(f"Using cached devices (age: {current_time - self._last_refresh_time:.1f}s)")
            return self._cached_devices
        
        with self._refresh_lock:
            # Double-check after acquiring lock
            if not force and self._cached_devices is not None and (current_time - self._last_refresh_time) < self._cache_timeout:
                logger.debug("Using cached devices after lock acquisition")
                return self._cached_devices
            
            logger.debug("Performing fresh device enumeration")
            return self._refresh_devices_impl()
    
    def _refresh_devices_impl(self) -> Dict[str, List[Dict[str, Any]]]:
        """Internal method to actually refresh devices."""
        try:
            # Force sounddevice to reload the device list
            sd._terminate()
            sd._initialize()

            # Get all devices
            devices = sd.query_devices()
            
            # Reset device lists
            self.input_devices = []
            self.output_devices = []
            
            # Get default devices
            try:
                default_input = sd.query_devices(kind='input')
                default_input_name = default_input['name'] if default_input else None
            except Exception:
                default_input_name = None
                logger.warning("Could not determine default input device")
            
            try:
                default_output = sd.query_devices(kind='output')
                default_output_name = default_output['name'] if default_output else None
            except Exception:
                default_output_name = None
                logger.warning("Could not determine default output device")
            
            # Group devices by hostapi to prioritize system devices
            hostapi_devices = {}
            
            # First pass: group devices by hostapi and identify defaults
            for i, device in enumerate(devices):
                hostapi = device.get('hostapi', 0)
                if hostapi not in hostapi_devices:
                    hostapi_devices[hostapi] = []
                
                # Mark default devices with partial matching (sounddevice truncates names)
                is_default_input = False
                is_default_output = False
                
                if default_input_name and device.get('max_input_channels', 0) > 0:
                    # Try exact match first, then partial match for truncated names
                    is_default_input = (device['name'] == default_input_name or 
                                       (len(default_input_name) >= 25 and device['name'].startswith(default_input_name)))
                
                if default_output_name and device.get('max_output_channels', 0) > 0:
                    # Try exact match first, then partial match for truncated names  
                    is_default_output = (device['name'] == default_output_name or
                                        (len(default_output_name) >= 25 and device['name'].startswith(default_output_name)))
                
                device_info = {
                    'id': str(i),
                    'name': device['name'],
                    'hostapi': hostapi,
                    'max_input_channels': device.get('max_input_channels', 0),
                    'max_output_channels': device.get('max_output_channels', 0),
                    'is_default_input': is_default_input,
                    'is_default_output': is_default_output
                }
                
                hostapi_devices[hostapi].append(device_info)
            
            # Find the WASAPI hostapi - this is what Windows Sound settings use
            # Only show devices that Windows considers "available" in Sound settings
            wasapi_hostapi = None
            for hostapi_id, devices_list in hostapi_devices.items():
                if devices_list and len(devices_list) > 0:
                    try:
                        hostapi_info = sd.query_hostapis(hostapi_id)
                        hostapi_name = hostapi_info.get('name', '').lower()
                        # Prioritize WASAPI as it matches Windows Sound settings
                        if 'wasapi' in hostapi_name:
                            wasapi_hostapi = hostapi_id
                            break
                    except Exception:
                        continue
            
            # If no WASAPI found, fall back to first hostapi with typical Windows devices
            if wasapi_hostapi is None:
                for hostapi_id, devices_list in hostapi_devices.items():
                    if devices_list and len(devices_list) > 0:
                        device = devices_list[0]
                        name = device['name'].lower()
                        # Look for typical Windows device naming patterns
                        if ('speakers' in name or 'microphone' in name or 'headset' in name or 
                            'headphone' in name or 'dell' in name or 'realtek' in name or
                            'nvidia' in name or 'intel' in name):
                            wasapi_hostapi = hostapi_id
                            break
            
            # Use only the primary hostapi (WASAPI preferred)
            windows_hostapis = [wasapi_hostapi] if wasapi_hostapi is not None else []
            
            # Process devices from Windows hostapis
            processed_names = set()
            
            for hostapi_id in windows_hostapis:
                if hostapi_id not in hostapi_devices:
                    continue
                    
                for device in hostapi_devices[hostapi_id]:
                    # Create a friendly label
                    device_name = device['name']
                    friendly_label = self._create_friendly_label(device_name)
                    
                    # Skip if we've already processed a device with this friendly name
                    if friendly_label in processed_names:
                        continue
                    
                    # Filter out problematic device names
                    name_lower = device_name.lower()
                    skip_patterns = [
                        'primary sound driver',
                        'primary sound capture driver', 
                        'microsoft sound mapper',
                        'virtual audio',
                        'voicemeeter',
                        'virtual cable',
                        'stereo mix',
                        'what u hear',
                        'bluetooth hands-free',
                        '@system32\\drivers\\bthhfenum.sys'  # Bluetooth driver entries
                    ]
                    
                    # Skip devices with problematic patterns
                    if any(pattern in name_lower for pattern in skip_patterns):
                        continue
                    
                    # Skip devices with empty or very short names in parentheses
                    if device_name.endswith('()') or device_name.endswith(' ()'):
                        continue
                    
                    # Add to the appropriate list
                    if device['max_input_channels'] > 0:
                        self.input_devices.append({
                            'id': device['id'],
                            'name': device_name,
                            'label': friendly_label,
                            'isDefault': device['is_default_input'],
                            'type': 'input',
                            'isAvailable': True,
                            'channels': device['max_input_channels'],
                            'sampleRate': int(device.get('default_samplerate', 44100)),
                            'hostApi': sd.query_hostapis(hostapi_id).get('name', 'Unknown')
                        })
                        processed_names.add(friendly_label)
                    
                    if device['max_output_channels'] > 0:
                        self.output_devices.append({
                            'id': device['id'],
                            'name': device_name,
                            'label': friendly_label,
                            'isDefault': device['is_default_output'],
                            'type': 'output',
                            'isAvailable': True,
                            'channels': device['max_output_channels'],
                            'sampleRate': int(device.get('default_samplerate', 44100)),
                            'hostApi': sd.query_hostapis(hostapi_id).get('name', 'Unknown')
                        })
                        processed_names.add(friendly_label)
            
            # If we didn't find any devices, fall back to all devices
            if not self.input_devices and not self.output_devices:
                logger.warning("No devices found in Windows hostapis, falling back to all devices")
                for devices_list in hostapi_devices.values():
                    for device in devices_list:
                        device_name = device['name']
                        friendly_label = self._create_friendly_label(device_name)
                        
                        if device['max_input_channels'] > 0:
                            self.input_devices.append({
                                'id': device['id'],
                                'name': device_name,
                                'label': friendly_label,
                                'isDefault': device['is_default_input'],
                                'type': 'input',
                                'isAvailable': True,
                                'channels': device['max_input_channels'],
                                'sampleRate': int(device.get('default_samplerate', 44100)),
                                'hostApi': 'Unknown'
                            })
                        
                        if device['max_output_channels'] > 0:
                            self.output_devices.append({
                                'id': device['id'],
                                'name': device_name,
                                'label': friendly_label,
                                'isDefault': device['is_default_output'],
                                'type': 'output',
                                'isAvailable': True,
                                'channels': device['max_output_channels'],
                                'sampleRate': int(device.get('default_samplerate', 44100)),
                                'hostApi': 'Unknown'
                            })
            
            # Sort devices - default first, then alphabetically
            self.input_devices.sort(key=lambda d: (not d['isDefault'], d.get('label', d['name']).lower()))
            self.output_devices.sort(key=lambda d: (not d['isDefault'], d.get('label', d['name']).lower()))
            
            # Cache the result with timestamp
            self._cached_devices = {
                'input': self.input_devices,
                'output': self.output_devices
            }
            self._last_refresh_time = time.time()
            
            logger.info(f"Found {len(self.input_devices)} input, {len(self.output_devices)} output devices from {len(windows_hostapis)} Windows hostapis")
            return self._cached_devices
        
        except Exception as e:
            logger.error(f"Error refreshing audio devices: {e}")
            return {
                'input': [],
                'output': []
            }
    
    def get_devices(self) -> List[Dict[str, Any]]:
        """Get the list of audio devices.
        
        Returns:
            List of all audio devices (input and output)
        """
        # Use cached refresh (respects cache timeout)
        device_data = self.refresh_devices()
        
        # Combine input and output devices into a single list for the API
        devices = []
        devices.extend(device_data.get('input', []))
        devices.extend(device_data.get('output', []))
        
        return devices
    
    def get_input_devices(self) -> List[Dict[str, Any]]:
        """Get the list of input devices.
        
        Returns:
            List of input devices
        """
        device_data = self.refresh_devices()
        return device_data.get('input', [])
    
    def get_output_devices(self) -> List[Dict[str, Any]]:
        """Get the list of output devices.
        
        Returns:
            List of output devices
        """
        device_data = self.refresh_devices()
        return device_data.get('output', [])
    
    def get_device_by_id(self, device_id: str, device_type: str = 'input') -> Optional[Dict[str, Any]]:
        """Get a device by its ID.
        
        Args:
            device_id: The device ID
            device_type: The device type ('input' or 'output')
            
        Returns:
            The device information or None if not found
        """
        device_data = self.refresh_devices()
        devices = device_data.get('input', []) if device_type == 'input' else device_data.get('output', [])
        
        for device in devices:
            if device['id'] == device_id:
                return device
        
        return None
    
    def get_default_device(self, device_type: str = 'input') -> Optional[Dict[str, Any]]:
        """Get the default device of the specified type.
        
        Args:
            device_type: The device type ('input' or 'output')
            
        Returns:
            The default device or None if not found
        """
        device_data = self.refresh_devices()
        devices = device_data.get('input', []) if device_type == 'input' else device_data.get('output', [])
        
        for device in devices:
            if device['isDefault']:
                return device
        
        # If no default found, return the first device
        return devices[0] if devices else None
    
    def test_device(self, device_id: str, device_type: str = 'input', duration: float = 1.0) -> Dict[str, Any]:
        """Test an audio device.
        
        Args:
            device_id: The device ID to test
            device_type: The device type ('input' or 'output')
            duration: Test duration in seconds
            
        Returns:
            Dictionary with test results
        """
        try:
            device_index = int(device_id)
            
            if device_type == 'input':
                # Test input device by recording
                import numpy as np
                
                recording = sd.rec(int(duration * 44100), samplerate=44100, channels=1, device=device_index)
                sd.wait()  # Wait for recording to complete
                
                # Check if we got any audio
                max_amplitude = np.max(np.abs(recording))
                
                return {
                    'success': True,
                    'max_amplitude': float(max_amplitude),
                    'has_signal': max_amplitude > 0.01,
                    'duration': duration
                }
            else:
                # Test output device by playing a tone
                import numpy as np
                
                # Generate a 440Hz sine wave
                t = np.linspace(0, duration, int(duration * 44100), False)
                tone = 0.1 * np.sin(2 * np.pi * 440 * t)
                
                sd.play(tone, samplerate=44100, device=device_index)
                sd.wait()  # Wait for playback to complete
                
                return {
                    'success': True,
                    'duration': duration
                }
                
        except Exception as e:
            logger.error(f"Error testing device {device_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _create_friendly_label(self, device_name: str) -> str:
        """Create a friendly label for the device.
        
        Args:
            device_name: The original device name
            
        Returns:
            A friendly label for display
        """
        # For now, just return the original name
        # Could be enhanced with device name cleanup/mapping in the future
        return device_name

    def get_status(self) -> Dict[str, Any]:
        """Get the current status of the device manager.
        
        Returns:
            Dictionary with status information
        """
        return {
            'input_devices_count': len(self.input_devices),
            'output_devices_count': len(self.output_devices),
            'devices_cached': self._cached_devices is not None,
            'default_input': self.get_default_device('input'),
            'default_output': self.get_default_device('output')
        } 