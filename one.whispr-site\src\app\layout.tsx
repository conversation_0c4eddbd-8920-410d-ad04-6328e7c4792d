import "./layout.css";
import type { Metadata } from "next";
import { <PERSON><PERSON>_<PERSON> } from "next/font/google";
import { ThemeProvider } from "@/components/layout/theme-provider";

// Initialize Chakra Petch with all needed weights
const chakraPetch = Cha<PERSON>_Petch({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  display: "swap",
  variable: "--font-sans",
});

export const metadata: Metadata = {
  title: "One Whispr",
  description: "AI powered voice to text",
  icons: {
    icon: [
      { url: "/icon.ico" },
      { url: "/one.whispr-icon.png" }
    ],
    apple: "/one.whispr-icon.png",
    shortcut: "/icon.ico"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning className={chakraPetch.variable}>
      <head />
      <body className="antialiased min-h-screen flex flex-col font-sans">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <main className="flex-grow">
            {children}
          </main>
        </ThemeProvider>
      </body>
    </html>
  );
}
