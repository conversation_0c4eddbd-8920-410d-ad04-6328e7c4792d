/**
 * useStepIntegration Hook
 * Consolidates common patterns for step lifecycle and proceed state management
 */

import { useEffect, useCallback } from 'react';
import { useSetupFlow, useStepLifecycle } from '../contexts/SetupFlowContext';
import type { SetupStep } from '../types/core';

interface UseStepIntegrationOptions {
  step: SetupStep;
  canProceed: boolean;
  onEnter?: () => Promise<void> | void;
  onExit?: () => Promise<void> | void;
  deps?: React.DependencyList;
}

/**
 * Hook that consolidates common step integration patterns:
 * - Automatically manages setupActions.setCanProceed based on canProceed parameter
 * - Registers step lifecycle handlers for onEnter and onExit
 * - Handles dependency arrays properly for lifecycle re-registration
 * 
 * @param options Configuration object
 * @param options.step The step name for lifecycle registration
 * @param options.canProceed Boolean indicating if the step can proceed to next
 * @param options.onEnter Optional callback for when the step is entered
 * @param options.onExit Optional callback for when the step is exited
 * @param options.deps Optional dependency array for lifecycle handlers
 */
export function useStepIntegration({
  step,
  canProceed,
  onEnter,
  onExit,
  deps = []
}: UseStepIntegrationOptions) {
  const { actions: setupActions } = useSetupFlow();

  // Automatically update proceed state when canProceed changes
  useEffect(() => {
    setupActions.setCanProceed(canProceed);
  }, [canProceed, setupActions]);

  // Create wrapped handlers to ensure they're async
  const handleEnter = useCallback(async () => {
    if (onEnter) {
      await onEnter();
    }
  }, [onEnter]);

  const handleExit = useCallback(async () => {
    if (onExit) {
      await onExit();
    }
  }, [onExit]);

  // Register step lifecycle handlers
  useStepLifecycle(
    step,
    {
      onEnter: onEnter ? handleEnter : undefined,
      onExit: onExit ? handleExit : undefined,
    },
    deps
  );
}

export default useStepIntegration; 