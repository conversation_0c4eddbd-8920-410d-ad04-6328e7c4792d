import { useEffect } from 'react';
import { useSetupState } from './useSetupState';
import { useSetupActions } from './useSetupActions';
import { useSetupEvents } from './useSetupEvents';

/**
 * Hook for managing the setup process - refactored for better separation of concerns
 */
export const useSetup = () => {
  // Use separated state management
  const { state, actions: stateActions } = useSetupState();

  // Use separated IPC actions
  const ipcActions = useSetupActions(stateActions);

  // Set up event listeners
  useSetupEvents({
    setState: stateActions.updateState,
    launchMainApp: ipcActions.launchMainApp
  });

  // Auto-start the setup process
  useEffect(() => {
    console.log('[SETUP] useEffect triggered - starting setup process');

    const startSetup = async () => {
      try {
        // Get environment variables from main process
        if (!window.electron) {
          console.error('[SETUP] Electron API not available');
          throw new Error('Electron API not available');
        }

        console.log('[SETUP] Getting environment variables...');
        const env = await window.electron.ipcRenderer.invoke('launcher:get-env');
        const isDev = env.NODE_ENV === 'development';
        const forceDownload = env.FORCE_DOWNLOAD === 'true';
        console.log('[SETUP] Environment check - isDev:', isDev, 'forceDownload:', forceDownload);

        // Always check for downloads first (matches old working behavior)
        console.log('[SETUP] Checking launch readiness');

        // Get the readiness result directly from the IPC call
        const readiness = await window.electron.ipcRenderer.invoke('launcher:check-ready');
        console.log('[SETUP] Readiness result:', readiness);

        // Update state with the readiness info
        stateActions.setLaunchReady(readiness);

        const needsDownload = !readiness.allReady;
        console.log('[SETUP] Needs download:', needsDownload);

        if (needsDownload) {
          // Sequential downloads - main app first, then backend
          console.log('[SETUP] Starting sequential downloads - mainApp:', readiness.mainAppNeeded, 'backend:', readiness.backendNeeded);

          if (readiness.mainAppNeeded) {
            // Check if this is Microsoft Store first launch
            if (readiness.reason?.includes('Microsoft Store first launch')) {
              console.log('[SETUP] Step 1: Starting Microsoft Store setup');
              stateActions.setPhase('downloading');
              await ipcActions.startMicrosoftStoreSetup();
            } else {
              console.log('[SETUP] Step 1: Starting main app download');
              stateActions.setPhase('downloading');
              await ipcActions.startMainAppDownload();
            }
          } else if (readiness.backendNeeded) {
            console.log('[SETUP] Step 1: Starting backend download (no main app needed)');
            stateActions.setPhase('downloading');
            await ipcActions.startBackendDownload();
          }
        } else {
          console.log('[SETUP] No downloads needed - launching main app');
          stateActions.setPhase('starting');
          await ipcActions.launchMainApp();
        }
      } catch (error) {
        console.error('[SETUP] Error in startSetup:', error);
        stateActions.setError(error instanceof Error ? error.message : String(error));
      }
    };

    startSetup();
  }, []); // Empty dependency array - only run once on mount

  // Auto-progression: main app download → backend download → launch
  useEffect(() => {
    const checkProgression = async () => {
      if (!state.launchReady) return;

      const mainAppNeeded = state.launchReady.mainAppNeeded;
      const backendNeeded = state.launchReady.backendNeeded;
      const mainAppDone = !mainAppNeeded || state.downloadComplete;
      const backendDone = !backendNeeded || state.backendComplete;

      // Step 1: Main app download completed, start backend download if needed
      if (mainAppDone && backendNeeded && !state.backendComplete && !state.backendDownloading && state.currentPhase === 'downloading') {
        console.log('[SETUP] Step 2: Main app complete, starting backend download');
        await ipcActions.startBackendDownload();
      }

      // Step 2: All downloads complete, launch main app
      else if (mainAppDone && backendDone && state.currentPhase === 'downloading') {
        console.log('[SETUP] All downloads complete - launching main app');
        stateActions.setPhase('starting');
        await ipcActions.launchMainApp();
      }
    };

    checkProgression();
  }, [state.downloadComplete, state.backendComplete, state.backendDownloading, state.launchReady, state.currentPhase, ipcActions, stateActions]);

  return {
    state,
    actions: ipcActions
  };
};