/**
 * Singleton instances for services
 * Separated to avoid circular dependencies in the main services index
 */

import { MainAppDownloader } from './download/mainAppDownloader';
import { BackendDownloader } from './download/backendDownloader';
import { MicrosoftStoreHandler } from './microsoft/storeHandler';
import { AppLauncher } from './launcher/appLauncher';

// Create singleton instances
export const microsoftStoreHandler = new MicrosoftStoreHandler();
export const downloader = new MainAppDownloader();
export const backendDownloader = new BackendDownloader(downloader, microsoftStoreHandler);
export const launcher = new AppLauncher(downloader, backendDownloader, microsoftStoreHandler);
