import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { MAIN_APP_UPDATES } from '../../constants';

/**
 * Handles version checking and manifest fetching for main app
 */
export class VersionChecker {
  private downloadPath: string;

  constructor(downloadPath: string) {
    this.downloadPath = downloadPath;
  }

  /**
   * Fetch server version info (contains releaseDate)
   */
  async fetchServerVersion(): Promise<any | null> {
    try {
      const versionUrl = MAIN_APP_UPDATES.versionUrl;
      console.log(`[DOWNLOADER] Fetching server version from ${versionUrl}`);

      const response = await axios.get(versionUrl, {
        timeout: 30000,
        headers: {
          'User-Agent': 'OneWhispr-Setup/1.0.0'
        }
      });

      if (response.status !== 200) {
        throw new Error(`Failed to fetch version: ${response.status}`);
      }

      return response.data;
    } catch (error) {
      console.error('[DOWNLOADER] Error fetching server version:', error);
      return null;
    }
  }

  /**
   * Fetch the download manifest
   */
  async fetchManifest(): Promise<any | null> {
    try {
      const manifestUrl = MAIN_APP_UPDATES.manifestUrl;
      console.log(`[DOWNLOADER] Fetching manifest from ${manifestUrl}`);

      const response = await axios.get(manifestUrl, {
        timeout: 30000,
        headers: {
          'User-Agent': 'OneWhispr-Setup/1.0.0'
        }
      });

      if (response.status !== 200) {
        throw new Error(`Failed to fetch manifest: ${response.status}`);
      }

      const responseData = response.data;

      // Check if we got a string (directory listing) instead of JSON
      if (typeof responseData === 'string') {
        console.log('[DOWNLOADER] Response preview:', responseData.substring(0, 200) + '...');
        console.error('[DOWNLOADER] Received directory listing instead of JSON manifest');
        throw new Error('Server returned directory listing instead of manifest JSON');
      }

      const manifest = responseData;
      console.log('[DOWNLOADER] Response preview:', JSON.stringify(manifest, null, 2).substring(0, 200) + '...');

      // Validate manifest structure
      if (!manifest.version || !manifest.files || !Array.isArray(manifest.files)) {
        console.error('[DOWNLOADER] Invalid manifest structure. Expected: {version: string, files: array}');
        console.error('[DOWNLOADER] Received:', manifest);
        throw new Error('Invalid manifest structure');
      }

      return manifest;
    } catch (error) {
      console.error('[DOWNLOADER] Error fetching manifest:', error);

      // In development mode with custom download path, provide a helpful message
      if (process.env.DOWNLOAD_PATH) {
        console.log('[DOWNLOADER] Development mode detected - manifest server may not be properly configured');
        console.log('[DOWNLOADER] This is expected during development testing');
      }

      return null;
    }
  }

  /**
   * Check if download is needed by comparing release dates with server
   */
  async checkDownloadNeeded(): Promise<{ needed: boolean, reason: string }> {
    try {
      // Fetch server version info (contains releaseDate)
      const serverVersion = await this.fetchServerVersion();

      if (!serverVersion) {
        return { needed: false, reason: 'Failed to fetch server version - will try to launch existing app' };
      }

      // Check if local version file exists
      const versionPath = path.join(this.downloadPath, 'version.json');

      if (!fs.existsSync(versionPath)) {
        return { needed: true, reason: 'No version file found - first time installation' };
      }

      // Read the installed version
      const localVersion = await fs.readJson(versionPath);

      // Check for rollback flag (forces update regardless of date)
      if (serverVersion.isRollback === true) {
        return { needed: true, reason: 'Rollback update required' };
      }

      // Compare release dates (like Python components)
      if (localVersion.releaseDate && serverVersion.releaseDate) {
        const localDate = new Date(localVersion.releaseDate);
        const serverDate = new Date(serverVersion.releaseDate);

        if (serverDate > localDate) {
          return { needed: true, reason: `New release available: ${serverVersion.releaseDate}` };
        }

        // Release dates match - no update needed
        return { needed: false, reason: 'All files up to date' };
      }

      // Fallback to version comparison if no releaseDate
      if (localVersion.version !== serverVersion.version) {
        return { needed: true, reason: `New version available: ${serverVersion.version}` };
      }

      // If we reach here, versions match - no need for file-by-file checking
      return { needed: false, reason: 'All files up to date' };
    } catch (error) {
      console.error('[DOWNLOADER] Error checking download needed:', error);
      return { needed: false, reason: 'Error checking files - will try to launch existing app' };
    }
  }
}
