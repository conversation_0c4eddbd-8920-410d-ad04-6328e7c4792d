'use client';

import { z } from 'zod';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { MdOutlineEmail } from 'react-icons/md';
import { <PERSON><PERSON>he<PERSON>, FiX } from 'react-icons/fi';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { FormSeparator } from '../ui/FormSeparator';
import { EmailDisplay } from '../ui/EmailDisplay';
import { useAuthForm } from '../hooks/useAuthForm';
import { useAuthNavigation } from '../hooks/useAuthNavigation';
import { AuthMode } from '../utils/auth-utils';
import { authenticateWithPassword } from '../../login/actions';
import { processPassword } from '../../register/actions';
import { generateLoginOTP } from '../../login/actions';

// Schema for login password
const loginPasswordSchema = z.object({
  password: z.string().min(1, { message: 'Please enter your password' }),
});

// Schema for registration/reset password with Whispr's requirements
const confirmPasswordSchema = z.object({
  password: z.string()
    .min(6, { message: 'Password must be at least 6 characters' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one number' }),
  confirmPassword: z.string().min(1, { message: 'Please confirm your password' }),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type LoginPasswordValues = z.infer<typeof loginPasswordSchema>;
type ConfirmPasswordValues = z.infer<typeof confirmPasswordSchema>;

type PasswordFormType = LoginPasswordValues | ConfirmPasswordValues;

interface PasswordFormProps {
  mode: AuthMode;
  email: string;
  firstName?: string;
  lastName?: string;
  token?: string;
  callbackUrl?: string | null;
}

/**
 * Unified password form that handles login, registration, and password reset
 */
export function PasswordForm({ 
  mode = 'login',
  email,
  firstName = '',
  lastName = '',
  token = '',
  callbackUrl
}: PasswordFormProps) {
  const navigation = useAuthNavigation();
  const [password, setPassword] = useState('');
  
  // Determine which schema to use based on mode
  const schema = mode === 'login' 
    ? loginPasswordSchema 
    : confirmPasswordSchema;
  
  // Handle form submission based on mode
  const handleSubmit = async (data: any) => {
    try {
      if (mode === 'login') {
        // Login flow - Create FormData for server action
        const formData = new FormData();
        formData.append('email', email);
        formData.append('password', data.password);
        
        // Call the server action
        const result = await authenticateWithPassword(formData);
        
        if (result.success) {
          // Token set by server action as cookie
          navigation.handleAuthSuccess('auth-token-set', email, 'login', callbackUrl);
        } else {
          // Handle error
          form.setError('password', { 
            type: 'manual', 
            message: result.error || 'Invalid credentials' 
          });
        }
      } else if (mode === 'register') {
        // Registration flow - Create FormData for server action
        const formData = new FormData();
        formData.append('email', email);
        formData.append('firstName', firstName);
        formData.append('lastName', lastName);
        formData.append('password', data.password);
        formData.append('confirmPassword', data.confirmPassword);
        
        // Call the server action
        const result = await processPassword(formData);
        
        if (result.success) {
          // Proceed to OTP verification
          navigation.navigateToRegister('otp', { 
            email, 
            callbackUrl,
            firstName: encodeURIComponent(firstName),
            lastName: encodeURIComponent(lastName)
          });
        } else {
          // Handle error
          form.setError('password', {
            type: 'manual',
            message: result.error || 'Error processing password'
          });
        }
      } else if (mode === 'reset') {
        // Password reset flow
        try {
          // Create FormData for server action
          const formData = new FormData();
          formData.append('email', email);
          formData.append('token', token);
          formData.append('password', data.password);
          formData.append('confirmPassword', data.confirmPassword);
          
          // Import and call the resetPassword action
          const { resetPassword } = await import('../../forgot-password/actions');
          const result = await resetPassword(formData);
          
          if (result.success) {
            // Navigate back to login with success message
            navigation.navigateToLogin('email', { message: 'password-reset-success' });
          } else {
            // Handle error
            form.setError('password', {
              type: 'manual',
              message: result.error || 'Failed to reset password'
            });
          }
        } catch (error) {
          console.error('Password reset error:', error);
          form.setError('password', {
            type: 'manual',
            message: 'An unexpected error occurred during password reset'
          });
        }
      }
    } catch (error) {
      console.error('Password form error:', error);
      form.setError('password', {
        type: 'manual',
        message: 'An unexpected error occurred'
      });
    }
  };
  
  // Setup form with custom hook
  const { form, loading, setLoading, submitHandler } = useAuthForm({
    schema: schema as any,
    defaultValues: mode === 'login' 
      ? { password: '' } 
      : { password: '', confirmPassword: '' },
    onSubmit: handleSubmit,
  });
  
  // Handle sign in with OTP code for login mode
  const handleSignInWithCode = async () => {
    if (mode === 'login') {
      try {
        setLoading(true);
        // Create FormData for the server action
        const formData = new FormData();
        formData.append('email', email);
        
        // Try to generate an OTP
        const result = await generateLoginOTP(formData);
        
        if (result.success) {
          // Navigate to OTP verification screen
          navigation.navigateToLogin('otp', { email, callbackUrl });
        } else {
          // Handle error
          form.setError('password', {
            type: 'manual',
            message: result.error || 'Failed to send verification code'
          });
        }
      } catch (error) {
        console.error('Error generating OTP:', error);
        form.setError('password', {
          type: 'manual',
          message: 'An unexpected error occurred'
        });
      } finally {
        setLoading(false);
      }
    }
  };
  
  // Get button text based on mode and loading state
  const getButtonText = () => {
    if (loading) {
      switch (mode) {
        case 'login': return 'Logging in...';
        case 'register': return 'Processing...';
        case 'reset': return 'Processing...';
      }
    }
    
    return 'Continue';
  };
  
  return (
    <>
      {/* Header with email and optional back button */}
      {mode === 'register' ? (
        <div className="text-center mb-4">
          <p className="text-base text-muted-foreground mb-1">Creating account for:</p>
          <p className="font-medium">{firstName} {lastName}</p>
          <p className="text-base text-muted-foreground">{email}</p>
        </div>
      ) : (
        <EmailDisplay 
          email={email} 
          prefix={mode === 'login' ? 'Logging in as:' : 'Create a new password for:'} 
        />
      )}
      
      {/* Password form */}
      <Form {...form}>
        <form onSubmit={submitHandler} className="space-y-4">
          {mode === 'login' ? (
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <div className="flex justify-between items-center">
                    <FormLabel>Password</FormLabel>
                    <Link 
                      href={`/forgot-password?email=${encodeURIComponent(email)}&trigger=login`} 
                      className="text-sm font-medium text-muted-foreground hover:text-primary"
                    >
                      Forgot password?
                    </Link>
                  </div>
                  <FormControl>
                    <Input 
                      placeholder="Enter your password" 
                      type="password" 
                      autoComplete="current-password"
                      className="text-base"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : (
            <>
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{mode === 'reset' ? 'New Password' : 'Password'}</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder={mode === 'reset' ? "Enter your new password" : "Create a password"}
                        type="password" 
                        autoComplete="new-password"
                        className="text-base"
                        {...field} 
                        onChange={(e) => {
                          field.onChange(e);
                          setPassword(e.target.value);
                        }}
                      />
                    </FormControl>
                    
                    {/* Password requirements */}
                    {field.value && (
                      <div className="mt-2 text-xs space-y-1">
                        <div className={`flex items-center ${field.value.length >= 6 ? 'text-green-500' : 'text-gray-400'}`}>
                          {field.value.length >= 6 ? <FiCheck className="mr-1" /> : <FiX className="mr-1" />}
                          At least 6 characters
                        </div>
                        <div className={`flex items-center ${/[A-Z]/.test(field.value) ? 'text-green-500' : 'text-gray-400'}`}>
                          {/[A-Z]/.test(field.value) ? <FiCheck className="mr-1" /> : <FiX className="mr-1" />}
                          Contains uppercase letter
                        </div>
                        <div className={`flex items-center ${/[a-z]/.test(field.value) ? 'text-green-500' : 'text-gray-400'}`}>
                          {/[a-z]/.test(field.value) ? <FiCheck className="mr-1" /> : <FiX className="mr-1" />}
                          Contains lowercase letter
                        </div>
                        <div className={`flex items-center ${/[0-9]/.test(field.value) ? 'text-green-500' : 'text-gray-400'}`}>
                          {/[0-9]/.test(field.value) ? <FiCheck className="mr-1" /> : <FiX className="mr-1" />}
                          Contains number
                        </div>
                      </div>
                    )}
                    
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{mode === 'reset' ? 'Confirm New Password' : 'Confirm Password'}</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder={mode === 'reset' ? "Confirm your new password" : "Confirm your password"}
                        type="password" 
                        autoComplete="new-password"
                        className="text-base"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
          
          <Button type="submit" className="w-full text-base" disabled={loading}>
            {getButtonText()}
          </Button>
        </form>
      </Form>
      
      {/* Email code option for login only */}
      {mode === 'login' && (
        <>
          <FormSeparator />
          
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={handleSignInWithCode}
            type="button"
            disabled={loading}
          >
            <MdOutlineEmail className="mr-2 h-4 w-4" />
            Email Sign In Code
          </Button>
        </>
      )}
    </>
  );
} 