/**
 * Backend process management for One.Whispr.
 */

import { spawn, ChildProcess } from 'child_process';
import { app } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import { BackendOptions } from './types';
import { backendEvents } from './websocket';

// Process state
let backendProcess: ChildProcess | null = null;
let portPromiseResolve: ((port: number) => void) | null = null;
let portPromiseReject: ((error: Error) => void) | null = null;

/**
 * Get backend executable and script paths based on environment
 */
function getBackendPaths(): { backendPath: string; scriptPath: string } {
  const appPath = app.getAppPath();
  const isDev = !app.isPackaged;

  let backendPath: string;
  let scriptPath: string;

  if (isDev) {
    // Development mode - use regular Python interpreter
    backendPath = path.join(appPath, '.dist', 'python', 'python.exe');
    scriptPath = path.join(appPath, 'python', 'whispr', 'main.py');
  } else {
    // Production mode - use compiled standalone executable
    // Works with both regular and separated builds:
    // - Regular build: Single executable with everything embedded
    // - Separated build: Executable + _internal folder + optional scripts folder
    backendPath = path.join(process.resourcesPath, 'backend', 'One Whispr Backend.exe');
    scriptPath = ''; // No script needed - it's built into the executable
  }

  return { backendPath, scriptPath };
}

/**
 * Prepare environment variables for backend process
 */
function prepareEnvironment(options: BackendOptions, voiceModelsPath: string, isDev: boolean): NodeJS.ProcessEnv {
  return {
    ...process.env,
    PYTHONUNBUFFERED: '1',
    PYTHONIOENCODING: 'utf-8',
    PYTHONDONTWRITEBYTECODE: '1',
    TERM: 'xterm-256color', // Make Python think it's in a terminal
    // Force transformers library to work in offline mode
    TRANSFORMERS_OFFLINE: '1',
    HF_HUB_OFFLINE: '1',
    HF_DATASETS_OFFLINE: '1',
    // Disable transformers telemetry
    DISABLE_TELEMETRY: '1',
    // Voice models configuration
    WHISPR_VOICEMODELS_PATH: voiceModelsPath,
    WHISPR_IS_DEV: isDev ? 'true' : 'false',
    ...options.env,
  };
}

/**
 * Prepare command line arguments for backend process
 */
function prepareArguments(scriptPath: string, options: BackendOptions): string[] {
  const isDev = !app.isPackaged;
  
  if (isDev && scriptPath !== '') {
    // Development mode with Python interpreter and script
    return [
      '-u', // Force unbuffered output
      '-s', // Don't add user site directory to sys.path
      scriptPath,
      '--port', '0',
      '--log-level', 'DEBUG',
      ...(options.args || []),
    ];
  } else {
    // Production mode OR development mode with compiled executable
    return [
      '--port', '0',
      '--log-level', isDev ? 'DEBUG' : 'INFO',
      ...(options.args || []),
    ];
  }
}

/**
 * Start the backend process
 */
export function startBackendProcess(options: BackendOptions = {}): Promise<number> {
  return new Promise((resolve, reject) => {
    if (backendProcess) {
      console.warn('Backend process already exists, cleaning up first...');
      cleanupBackendProcess();
    }

    try {
      const { backendPath, scriptPath } = getBackendPaths();
      const appPath = app.getAppPath();
      
      const isDev = !app.isPackaged;
      console.log(`Starting backend (${isDev ? 'development' : 'production'} mode)`);
      console.log(`Using backend executable: ${backendPath}`);
      if (isDev && scriptPath) {
        console.log(`Script path: ${scriptPath}`);
      }
      console.log(`Working directory: ${appPath}`);

      // Set up voice models path based on environment
      const voiceModelsPath = isDev
        ? path.join(appPath, '.dist', 'voicemodels')
        : path.join(process.resourcesPath, 'voicemodels');

      console.log(`Voice models path: ${voiceModelsPath}`);

      // Check if files exist
      console.log(`Backend executable exists: ${fs.existsSync(backendPath)}`);
      if (isDev && scriptPath) {
        console.log(`Script exists: ${fs.existsSync(scriptPath)}`);
      }

      // Check for separated build structure in production
      if (!isDev) {
        const backendDir = path.dirname(backendPath);
        const internalDir = path.join(backendDir, '_internal');
        const scriptsDir = path.join(backendDir, 'scripts');

        console.log(`Backend directory: ${backendDir}`);
        console.log(`_internal directory exists: ${fs.existsSync(internalDir)}`);
        console.log(`scripts directory exists: ${fs.existsSync(scriptsDir)}`);

        // Log build type detection
        if (fs.existsSync(internalDir)) {
          console.log('Detected: Separated build with _internal structure');
        } else {
          console.log('Detected: Regular build structure');
        }
      }

      const env = prepareEnvironment(options, voiceModelsPath, isDev);
      const args = prepareArguments(scriptPath, options);
      
      console.log(`Arguments: ${JSON.stringify(args)}`);
      
      // Spawn backend process
      console.log('About to spawn backend process...');

      // Set working directory to where the backend executable is located
      // This ensures it can find its _internal dependencies
      const backendWorkingDir = path.dirname(backendPath);
      console.log(`Backend working directory: ${backendWorkingDir}`);

      backendProcess = spawn(backendPath, args, {
        cwd: backendWorkingDir,  // Use backend executable directory, not app directory
        env,
        stdio: ['ignore', 'pipe', 'pipe'],
        shell: false,  // Try without shell first
        windowsHide: false,
        detached: false,
      });
      
      console.log(`Backend process spawned with PID: ${backendProcess.pid}`);
      
      // Check if process is still running after a short delay
      setTimeout(() => {
        if (backendProcess && backendProcess.exitCode !== null) {
          console.error(`Backend process exited early with code: ${backendProcess.exitCode}`);
        } else if (backendProcess) {
          console.log('Backend process is still running...');
        }
      }, 1000);
      
      // Handle stdout
      backendProcess.stdout!.setEncoding('utf8');
      backendProcess.stdout!.on('data', (data: Buffer) => {
        const output = data.toString().trim();
        if (output) {
          console.log(`[Backend] ${output}`);

          // Check if this line contains the WebSocket port
          const portMatch = output.match(/WebSocket server started on.*:(\d+)/);
          if (portMatch && portPromiseResolve) {
            const port = parseInt(portMatch[1], 10);
            portPromiseResolve(port);
            portPromiseResolve = null;
            portPromiseReject = null;
          }

          // Check for ML library initialization events
          if (output.includes('Background ML library initialization completed successfully')) {
            console.log('[Backend] ML libraries initialization completed - emitting event');
            backendEvents.emit('ml_libraries_initialized', {
              message: 'ML libraries initialization completed successfully',
              timestamp: new Date().toISOString()
            });
          }
        }
      });
      
      // Handle stderr
      backendProcess.stderr!.setEncoding('utf8');
      backendProcess.stderr!.on('data', (data: Buffer) => {
        const output = data.toString().trim();
        if (output) {
          console.error(`[Backend Error] ${output}`);
        }
      });
      
      // Handle process exit
      backendProcess.on('exit', (code: number | null) => {
        console.log(`Backend process exited with code ${code}`);
        
        // Try to read any remaining output
        if (backendProcess && backendProcess.stdout) {
          backendProcess.stdout.removeAllListeners('data');
          const remainingData = backendProcess.stdout.read();
          if (remainingData) {
            console.log(`[Backend Final Output] ${remainingData.toString()}`);
          }
        }
        
        cleanupBackendProcess();
        if (options.onExit) {
          options.onExit(code);
        }
      });
      
      // Handle process errors
      backendProcess.on('error', (error: Error) => {
        console.error('Failed to start backend process:', error);
        cleanupBackendProcess();
        if (options.onError) {
          options.onError(error);
        }
        reject(error);
      });
      
      // Wait for the port from stdout
      const portPromise = new Promise<number>((resolvePort, rejectPort) => {
        portPromiseResolve = resolvePort;
        portPromiseReject = rejectPort;
        
        // Set a timeout (increased to 30 seconds)
        setTimeout(() => {
          if (portPromiseReject) {
            portPromiseReject(new Error('Timeout waiting for backend to start'));
            portPromiseResolve = null;
            portPromiseReject = null;
          }
        }, 30000);
      });
      
      portPromise
        .then((port) => {
          if (options.onReady) {
            options.onReady();
          }
          resolve(port);
        })
        .catch((error) => {
          console.error('Error getting port from backend:', error);
          cleanupBackendProcess();
          reject(error);
        });
    } catch (error) {
      console.error('Error setting up backend:', error);
      cleanupBackendProcess();
      reject(error);
    }
  });
}

/**
 * Clean up the backend process and related resources
 */
export function cleanupBackendProcess(): void {
  // Kill backend process
  if (backendProcess) {
    try {
      if (backendProcess.pid) {
        console.log(`Terminating backend process with PID: ${backendProcess.pid}`);
        if (process.platform === 'win32') {
          // Kill process tree on Windows
          spawn('taskkill', ['/pid', backendProcess.pid.toString(), '/f', '/t'], { 
            stdio: 'ignore',
            detached: true 
          });
        } else {
          process.kill(-backendProcess.pid, 'SIGKILL');
        }
      }
    } catch (error) {
      console.error('Error killing backend process:', error);
    }
    backendProcess = null;
  }
  
  // Clean up any pending port promise
  if (portPromiseReject) {
    portPromiseReject(new Error('Backend terminated'));
    portPromiseResolve = null;
    portPromiseReject = null;
  }
}

/**
 * Kill any existing backend processes that might be running
 */
export function killExistingBackendProcesses(): Promise<void> {
  return new Promise((resolve) => {
    if (process.platform === 'win32') {
      // Kill any existing python processes that might be running our script
      const killProcess = spawn('taskkill', ['/f', '/im', 'python.exe'], { 
        stdio: 'ignore',
        detached: true 
      });
      
      killProcess.on('close', () => {
        // Wait a bit for processes to terminate
        setTimeout(resolve, 1000);
      });
      
      killProcess.on('error', () => {
        // Ignore errors, processes might not exist
        setTimeout(resolve, 100);
      });
    } else {
      // On Unix-like systems, try to kill python3 processes
      const killProcess = spawn('pkill', ['-f', 'python.*whispr.*main.py'], {
        stdio: 'ignore',
        detached: true 
      });
      
      killProcess.on('close', () => {
        setTimeout(resolve, 1000);
      });
      
      killProcess.on('error', () => {
        setTimeout(resolve, 100);
      });
    }
  });
}

/**
 * Check if the backend process is running
 */
export function isBackendRunning(): boolean {
  return backendProcess !== null && backendProcess.exitCode === null;
}