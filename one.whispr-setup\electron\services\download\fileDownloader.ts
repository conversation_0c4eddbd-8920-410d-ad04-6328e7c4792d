import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import axios from 'axios';
import { getLauncherWindow } from '../../window';

interface ManifestFile {
  path: string;
  size: number;
  checksum: string;
  url: string;
}

interface DownloadProgress {
  file: string;
  totalFiles: number;
  currentFile: number;
  progress: number;
  totalProgress: number;
  speed: number;
  eta: number;
}

/**
 * Handles file downloading and checksum verification
 */
export class FileDownloader {
  private downloadPath: string;
  private abortController: AbortController | null = null;

  constructor(downloadPath: string) {
    this.downloadPath = downloadPath;
  }

  setAbortController(controller: AbortController | null): void {
    this.abortController = controller;
  }

  /**
   * Download a single file with progress reporting
   */
  async downloadFile(file: ManifestFile, fileIndex: number, totalFiles: number, customFilePath?: string): Promise<void> {
    return new Promise(async (resolve, reject) => {
      try {
        console.log(`[DOWNLOADER] Downloading ${file.path}`);
        
        // Use custom file path if provided, otherwise use proper directory structure
        const filePath = customFilePath || (() => {
          const properPath = file.path.replace(/^\.\//, '');
          return path.join(this.downloadPath, properPath);
        })();
        const fileDir = path.dirname(filePath);
        
        // Ensure directory exists
        await fs.ensureDir(fileDir);
        
        // Create write stream
        const writer = fs.createWriteStream(filePath);
        
        // Track progress
        let downloadedBytes = 0;
        const startTime = Date.now();
        let lastUpdateTime = startTime;
        let lastBytes = 0;
        let speed = 0;
        
        // Calculate timeout based on file size (minimum 60s, +30s per 50MB)
        const timeoutMs = Math.max(60000, 60000 + Math.floor(file.size / (50 * 1024 * 1024)) * 30000);
        console.log(`[DOWNLOADER] File ${file.path} (${(file.size / 1024 / 1024).toFixed(1)}MB) - timeout: ${timeoutMs / 1000}s`);

        // Download the file
        const response = await axios({
          method: 'get',
          url: file.url,
          responseType: 'stream',
          signal: this.abortController?.signal,
          timeout: timeoutMs,
          headers: {
            'User-Agent': 'OneWhispr-Setup/1.0.0'
          }
        });

        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;
          
          // Calculate speed and ETA every 500ms
          const now = Date.now();
          if (now - lastUpdateTime >= 500) {
            const timeDiff = (now - lastUpdateTime) / 1000;
            const bytesDiff = downloadedBytes - lastBytes;
            speed = bytesDiff / timeDiff;
            
            lastUpdateTime = now;
            lastBytes = downloadedBytes;
          }
          
          // Calculate progress
          const fileProgress = (downloadedBytes / file.size) * 100;
          const totalProgress = ((fileIndex - 1) / totalFiles) * 100 + (fileProgress / totalFiles);
          const eta = speed > 0 ? (file.size - downloadedBytes) / speed : 0;
          
          // Send progress update
          const launcherWindow = getLauncherWindow();
          if (launcherWindow) {
            launcherWindow.webContents.send('download:progress', {
              file: file.path,
              totalFiles,
              currentFile: fileIndex,
              progress: fileProgress,
              totalProgress,
              speed,
              eta,
              downloaded: downloadedBytes,
              total: file.size
            } as DownloadProgress);
          }
        });

        response.data.on('error', (error: Error) => {
          console.error(`[DOWNLOADER] Stream error for ${file.path}:`, error);
          writer.destroy();
          reject(error);
        });

        response.data.on('end', () => {
          console.log(`[DOWNLOADER] Download completed for ${file.path}`);
          writer.end();
        });

        writer.on('finish', () => {
          console.log(`[DOWNLOADER] File written successfully: ${filePath}`);
          resolve();
        });

        writer.on('error', (error: Error) => {
          console.error(`[DOWNLOADER] Write error for ${file.path}:`, error);
          reject(error);
        });

        response.data.pipe(writer);

      } catch (error) {
        console.error(`[DOWNLOADER] Error downloading ${file.path}:`, error);
        reject(error);
      }
    });
  }

  /**
   * Verify file checksum
   */
  async verifyChecksum(filePath: string, expectedChecksum: string): Promise<boolean> {
    try {
      const fileBuffer = await fs.readFile(filePath);
      const hash = crypto.createHash('sha256');
      hash.update(fileBuffer);
      const actualChecksum = hash.digest('hex');
      
      const isValid = actualChecksum === expectedChecksum;
      console.log(`[DOWNLOADER] Checksum verification for ${path.basename(filePath)}: ${isValid ? 'PASS' : 'FAIL'}`);
      
      if (!isValid) {
        console.error(`[DOWNLOADER] Expected: ${expectedChecksum}`);
        console.error(`[DOWNLOADER] Actual: ${actualChecksum}`);
      }
      
      return isValid;
    } catch (error) {
      console.error(`[DOWNLOADER] Error verifying checksum for ${filePath}:`, error);
      return false;
    }
  }
}
