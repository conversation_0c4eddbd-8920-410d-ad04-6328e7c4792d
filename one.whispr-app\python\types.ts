/**
 * Type definitions for backend integration.
 */

export interface BackendOptions {
  args?: string[];
  env?: NodeJS.ProcessEnv;
  onReady?: () => void;
  onExit?: (code: number | null) => void;
  onError?: (error: Error) => void;
}

export interface IPCMessage {
  id?: string;
  type: string;
  command?: string;
  params?: any;
  event?: string;
  data?: any;
  result?: any;
  error?: string;
  args?: any;
}

export interface PendingRequest {
  resolve: (value: any) => void;
  reject: (reason: any) => void;
  timeout: NodeJS.Timeout;
} 