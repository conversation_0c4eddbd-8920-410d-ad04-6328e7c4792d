import { BaseRepository } from '../../database/repositories/base';
import { SchemaWithMigrations } from '../../database/schemas/manager';
import { TextReplacement, DEFAULT_TEXT_REPLACEMENTS } from './types';

/**
 * Repository for text replacement rules
 */
export class TextReplacementRepository extends BaseRepository<TextReplacement> {
  constructor() {
    super('text_replacements');
    this.initializeDefaults();
  }

  /**
   * Ensure the table exists with proper schema
   */
  protected ensureTable(): void {
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS text_replacements (
        id TEXT PRIMARY KEY,
        data TEXT NOT NULL
      );
    `);
  }

  /**
   * Initialize with default text replacements if empty
   */
  private initializeDefaults(): void {
    try {
      const existing = this.findAll();
      if (existing.length === 0) {
        // Create default text replacements
        DEFAULT_TEXT_REPLACEMENTS.forEach(replacement => {
          this.create(replacement);
        });

        console.log('Initialized default text replacements');
      }
    } catch (error) {
      console.error('Error initializing default text replacements:', error);
    }
  }

  /**
   * Find text replacements by mode
   */
  findByMode(modeId: string): TextReplacement[] {
    try {
      const allItems = this.findAll();
      return allItems.filter(item => 
        item.isActive && 
        (item.modes.length === 0 || item.modes.includes(modeId))
      );
    } catch (error) {
      console.error('Error finding text replacements by mode:', error);
      return [];
    }
  }

  /**
   * Get active text replacements ordered by order_index
   */
  getActiveByOrder(modeId?: string): TextReplacement[] {
    try {
      let items = this.findAll().filter(item => item.isActive);
      
      if (modeId) {
        items = items.filter(item => 
          item.modes.length === 0 || item.modes.includes(modeId)
        );
      }
      
      return items.sort((a, b) => (a.order_index || 0) - (b.order_index || 0));
    } catch (error) {
      console.error('Error getting text replacements by order:', error);
      return [];
    }
  }

  /**
   * Search text replacements by original text
   */
  searchByOriginal(searchTerm: string): TextReplacement[] {
    try {
      const term = searchTerm.toLowerCase();
      const allItems = this.findAll();
      return allItems.filter(item => 
        item.isActive && 
        item.original.toLowerCase().includes(term)
      );
    } catch (error) {
      console.error('Error searching text replacements:', error);
      return [];
    }
  }

  /**
   * Get regex-based replacements
   */
  getRegexReplacements(modeId?: string): TextReplacement[] {
    try {
      let items = this.findAll().filter(item => 
        item.isActive && item.isRegex === true
      );
      
      if (modeId) {
        items = items.filter(item => 
          item.modes.length === 0 || item.modes.includes(modeId)
        );
      }
      
      return items.sort((a, b) => (a.order_index || 0) - (b.order_index || 0));
    } catch (error) {
      console.error('Error getting regex replacements:', error);
      return [];
    }
  }

  /**
   * Get literal text replacements
   */
  getLiteralReplacements(modeId?: string): TextReplacement[] {
    try {
      let items = this.findAll().filter(item => 
        item.isActive && item.isRegex !== true
      );
      
      if (modeId) {
        items = items.filter(item => 
          item.modes.length === 0 || item.modes.includes(modeId)
        );
      }
      
      return items.sort((a, b) => (a.order_index || 0) - (b.order_index || 0));
    } catch (error) {
      console.error('Error getting literal replacements:', error);
      return [];
    }
  }

  /**
   * Validate regex pattern
   */
  validateRegexPattern(pattern: string): { valid: boolean; error?: string } {
    try {
      new RegExp(pattern);
      return { valid: true };
    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : 'Invalid regex pattern'
      };
    }
  }

  /**
   * Bulk import text replacements
   */
  bulkImport(items: Omit<TextReplacement, 'id'>[]): boolean {
    try {
      // Validate regex patterns first
      for (const item of items) {
        if (item.isRegex) {
          const validation = this.validateRegexPattern(item.original);
          if (!validation.valid) {
            console.error(`Invalid regex pattern: ${item.original} - ${validation.error}`);
            return false;
          }
        }
      }
      
      // Create all items
      for (const item of items) {
        this.create(item);
      }
      
      return true;
    } catch (error) {
      console.error('Error bulk importing text replacements:', error);
      return false;
    }
  }

  /**
   * Increment usage count for a text replacement
   */
  incrementUsage(id: string): boolean {
    try {
      const item = this.findById(id);
      if (!item) {
        console.error(`Text replacement not found: ${id}`);
        return false;
      }

      const updatedItem: TextReplacement = {
        ...item,
        frequency: (item.frequency || 0) + 1,
        lastUsed: Date.now()
      };

      const result = this.update(id, updatedItem);
      return result !== null;
    } catch (error) {
      console.error('Error incrementing text replacement usage:', error);
      return false;
    }
  }

  /**
   * Get usage analytics for text replacements
   */
  getUsageAnalytics(): {
    totalUsage: number;
    averageFrequency: number;
    mostFrequentRules: TextReplacement[];
    recentlyUsedRules: TextReplacement[];
    unusedRules: TextReplacement[];
  } {
    try {
      const allItems = this.findAll().filter(item => item.isActive);
      
      const totalUsage = allItems.reduce((sum, item) => sum + (item.frequency || 0), 0);
      const averageFrequency = allItems.length > 0 ? totalUsage / allItems.length : 0;
      
      const mostFrequentRules = allItems
        .filter(item => (item.frequency || 0) > 0)
        .sort((a, b) => (b.frequency || 0) - (a.frequency || 0))
        .slice(0, 10);

      const recentlyUsedRules = allItems
        .filter(item => item.lastUsed && item.lastUsed > 0)
        .sort((a, b) => (b.lastUsed || 0) - (a.lastUsed || 0))
        .slice(0, 10);

      const unusedRules = allItems.filter(item => 
        (!item.frequency || item.frequency === 0) && 
        (!item.lastUsed || item.lastUsed === 0)
      );

      return {
        totalUsage,
        averageFrequency,
        mostFrequentRules,
        recentlyUsedRules,
        unusedRules
      };
    } catch (error) {
      console.error('Error getting text replacement usage analytics:', error);
      return {
        totalUsage: 0,
        averageFrequency: 0,
        mostFrequentRules: [],
        recentlyUsedRules: [],
        unusedRules: []
      };
    }
  }

  /**
   * Get text replacement statistics
   */
  getStatistics(): {
    total: number;
    active: number;
    inactive: number;
    regex: number;
    literal: number;
    byMode: Record<string, number>;
    mostUsed: { original: string; frequency: number }[];
  } {
    try {
      const allItems = this.findAll();
      const active = allItems.filter(item => item.isActive);
      const inactive = allItems.filter(item => !item.isActive);
      const regex = active.filter(item => item.isRegex === true);
      const literal = active.filter(item => item.isRegex !== true);

      // Count by mode
      const byMode: Record<string, number> = {};
      active.forEach(item => {
        item.modes.forEach(modeId => {
          byMode[modeId] = (byMode[modeId] || 0) + 1;
        });
      });

      // Get most used replacements
      const mostUsed = active
        .filter(item => (item.frequency || 0) > 0)
        .sort((a, b) => (b.frequency || 0) - (a.frequency || 0))
        .slice(0, 10)
        .map(item => ({
          original: item.original,
          frequency: item.frequency || 0
        }));

      return {
        total: allItems.length,
        active: active.length,
        inactive: inactive.length,
        regex: regex.length,
        literal: literal.length,
        byMode,
        mostUsed
      };
    } catch (error) {
      console.error('Error getting text replacement statistics:', error);
      return {
        total: 0,
        active: 0,
        inactive: 0,
        regex: 0,
        literal: 0,
        byMode: {},
        mostUsed: []
      };
    }
  }

  /**
   * Get schema definition for this repository
   */
  static getSchema(): SchemaWithMigrations {
    return {
      name: 'text_replacements',
      createStatement: `
        CREATE TABLE IF NOT EXISTS text_replacements (
          id TEXT PRIMARY KEY,
          data TEXT NOT NULL
        );
      `,
      migrations: [
        {
          version: 1,
          up: `
            -- Add index for faster lookups
            CREATE INDEX IF NOT EXISTS idx_text_replacements_id ON text_replacements(id);
          `,
          down: `
            DROP INDEX IF EXISTS idx_text_replacements_id;
          `
        }
      ]
    };
  }
}