'use client';

interface EmailDisplayProps {
  email: string;
  prefix?: string;
  className?: string;
}

/**
 * A consistent way to display an email address in auth flows
 */
export function EmailDisplay({ 
  email, 
  prefix = "Logging in as", 
  className = "" 
}: EmailDisplayProps) {
  return (
    <div className={`text-center mb-4 ${className}`}>
      <p className="font-medium mb-1">{prefix}</p>
      <p className="text-base text-muted-foreground">{email}</p>
    </div>
  );
} 