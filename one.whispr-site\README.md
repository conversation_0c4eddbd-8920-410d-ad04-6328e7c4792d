# one.whispr

## Supabase Database Management

This project uses Supa<PERSON> as the database backend. Database schema changes are managed through migration files stored in the `src/migrations` directory.

### Database Setup (One-Time Only)

Before you can use migrations, you need to set up the required database function. Run this SQL once in the Supabase SQL Editor:

```sql
-- Create execute_sql function needed for migrations
CREATE OR REPLACE FUNCTION public.execute_sql(sql_query TEXT) 
RETURNS VOID AS $$
BEGIN
  EXECUTE sql_query;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant access to roles
GRANT EXECUTE ON FUNCTION public.execute_sql TO authenticated;
GRANT EXECUTE ON FUNCTION public.execute_sql TO anon;
GRANT EXECUTE ON FUNCTION public.execute_sql TO service_role;
```

### How to Apply Migrations

To apply migrations to your database:

1. Make sure your environment variables are set:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

2. Run the migration script:
   ```bash
   npm run db:apply-migrations
   ```

The script will:
- Create the `_migrations` table if it doesn't exist
- Apply any new migrations that haven't been run yet
- Track which migrations have been applied

### Creating New Migrations

When you need to make database changes:

1. Create a new SQL file in `src/migrations` with a sequential number prefix (e.g., `002_add_users_table.sql`)
2. Write your SQL statements (CREATE TABLE, ALTER TABLE, etc.)
3. Apply the migration with `npm run db:apply-migrations`

**Important Notes:**
- Don't use `BEGIN` and `COMMIT` transaction blocks in your migrations
- Always use `IF NOT EXISTS` for `CREATE TABLE` and similar statements
- Consider creating rollback files for complex changes (e.g., `002_add_users_table_rollback.sql`)

## Authentication & Database Setup

The application uses Supabase for authentication and database functionality.

### Database Migrations

The project includes several database migrations:

1. `001_create_waitlist_table.sql` - Creates the initial waitlist table
2. `002_create_profiles_table.sql` - Creates the user profiles table linked to Supabase auth
3. `003_add_rls_to_waitlist.sql` - Adds Row Level Security policies to the waitlist table

To apply these migrations, use the migration script:

```bash
npm run migrate
```

### Authentication Flow

The authentication system is implemented using Supabase Auth with the following features:

- Email/password registration with email verification
- Password reset functionality
- Magic link (passwordless) login
- Profile management

When a user registers, a corresponding entry is automatically created in the `profiles` table via a database trigger defined in the migrations.

### Environment Variables

Make sure the following environment variables are properly set in your `.env.local` file:

```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
NEXT_PUBLIC_SITE_URL=your-site-url
```

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

## Production

```bash
# Build for production
npm run build

# Start production server
npm run start
``` 