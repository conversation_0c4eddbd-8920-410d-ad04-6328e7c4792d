// Add electron-squirrel-startup at the very top
// eslint-disable-next-line @typescript-eslint/no-var-requires
if (require('electron-squirrel-startup')) 
  process.exit(0);

import { app } from 'electron';
import { createLauncherWindow, showLauncherWindow } from './window';
import { launcher, downloader, backendDownloader } from './services';
import { setupLauncherIpc } from './ipc/launcher';
import { setupDownloadIpc } from './ipc/download';
import { setupSetupIpc } from './ipc/setup';
import { setupAutoUpdater } from './utils/autoUpdater';
import { IS_MICROSOFT } from './constants';

// Setup IPC handlers
setupLauncherIpc(launcher);
setupDownloadIpc({
  mainAppDownloader: downloader,
  backendDownloader: backendDownloader
});
setupSetupIpc(null);

// Setup app ready handler
app.whenReady().then(async () => {
  console.log('[LAUNCHER] App ready, initializing launcher...');

  // Setup auto-updater (skip for Microsoft Store builds)
  if (!IS_MICROSOFT) {
    console.log('[LAUNCHER] Setting up auto-updater for direct builds');
    setupAutoUpdater();
  } else {
    console.log('[LAUNCHER] Skipping auto-updater for Microsoft Store build');
  }

  // Create and show the launcher window
  createLauncherWindow();
  showLauncherWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Clean up handlers on app quit
app.on('before-quit', () => {
  console.log('[LAUNCHER] Cleaning up before quit...');
  launcher.cleanup();
});