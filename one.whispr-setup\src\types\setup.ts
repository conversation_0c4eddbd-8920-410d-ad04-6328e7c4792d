export interface DownloadProgress {
  file: string;
  totalFiles: number;
  currentFile: number;
  progress: number;
  totalProgress: number;
  speed: number;
  eta: number;
}

export interface BackendDownloadProgress {
  type: 'runtime' | 'scripts';
  progress: number;
  speed: number;
  eta: number;
  status: string;
}

export interface LaunchReadiness {
  mainAppReady: boolean;
  backendReady: boolean;
  allReady: boolean;
  mainAppNeeded: boolean;
  backendNeeded: boolean;
  reason: string;
}

export interface SetupState {
  // Overall readiness
  launchReady: LaunchReadiness | null;

  // Download state
  downloadNeeded: boolean;
  downloadReason: string;
  isDownloading: boolean;
  downloadProgress: DownloadProgress | null;
  downloadError: string | null;
  downloadComplete: boolean;

  // Backend download state
  backendDownloading: boolean;
  backendProgress: BackendDownloadProgress | null;
  backendError: string | null;
  backendComplete: boolean;

  // Main app state
  mainAppError: string | null;

  // Overall state
  currentPhase: 'checking' | 'downloading' | 'starting' | 'waiting' | 'error';
}
